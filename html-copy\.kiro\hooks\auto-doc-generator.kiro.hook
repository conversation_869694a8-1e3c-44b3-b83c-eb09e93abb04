{"enabled": true, "name": "Auto Documentation Generator", "description": "Automatically generates comprehensive documentation for modified code files including function signatures, parameters, return types, usage examples, and updates README.md with new exports", "version": "1", "when": {"type": "fileEdited", "patterns": ["*.js", "*.ts", "*.jsx", "*.tsx", "*.py", "*.java", "*.cpp", "*.c", "*.cs", "*.php", "*.rb", "*.go"]}, "then": {"type": "askAgent", "prompt": "Generate comprehensive documentation for the current file:\n1. Extract function and class signatures\n2. Document parameters and return types\n3. Provide usage examples based on existing code\n4. Update the README.md with any new exports\n5. Ensure documentation follows project standards"}}