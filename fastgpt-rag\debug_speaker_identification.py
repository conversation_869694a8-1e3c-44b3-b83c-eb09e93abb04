#!/usr/bin/env python3

"""
调试版本的说话人识别脚本，用于定位问题
"""
import argparse
import sys
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Tuple

import numpy as np
import sherpa_onnx
import soundfile as sf

try:
    import sounddevice as sd
    print("✓ sounddevice 导入成功")
except ImportError:
    print("❌ sounddevice 导入失败")
    print("请先安装: pip install sounddevice")
    sys.exit(-1)


def get_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--speaker-file",
        type=str,
        required=True,
        help="说话人文件路径",
    )

    parser.add_argument(
        "--model",
        type=str,
        required=True,
        help="说话人嵌入模型文件路径",
    )

    parser.add_argument(
        "--silero-vad-model",
        type=str,
        required=True,
        help="VAD模型路径",
    )

    parser.add_argument("--threshold", type=float, default=0.6)
    parser.add_argument("--num-threads", type=int, default=1)
    parser.add_argument("--debug", type=bool, default=False)
    parser.add_argument("--provider", type=str, default="cpu")

    return parser.parse_args()


def load_speaker_embedding_model(args):
    print("🔄 加载说话人嵌入模型...")
    config = sherpa_onnx.SpeakerEmbeddingExtractorConfig(
        model=args.model,
        num_threads=args.num_threads,
        debug=args.debug,
        provider=args.provider,
    )
    if not config.validate():
        raise ValueError(f"Invalid config. {config}")
    extractor = sherpa_onnx.SpeakerEmbeddingExtractor(config)
    print("✓ 说话人嵌入模型加载成功")
    return extractor


def load_speaker_file(args) -> Dict[str, List[str]]:
    print("🔄 加载说话人文件...")
    if not Path(args.speaker_file).is_file():
        raise ValueError(f"--speaker-file {args.speaker_file} does not exist")

    ans = defaultdict(list)
    with open(args.speaker_file, encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            fields = line.split()
            if len(fields) != 2:
                raise ValueError(f"Invalid line: {line}. Fields: {fields}")

            speaker_name, filename = fields
            ans[speaker_name].append(filename)
    
    print(f"✓ 加载了 {len(ans)} 个说话人的数据")
    for name, files in ans.items():
        print(f"  - {name}: {len(files)} 个文件")
    return ans


def load_audio(filename: str) -> Tuple[np.ndarray, int]:
    data, sample_rate = sf.read(
        filename,
        always_2d=True,
        dtype="float32",
    )
    data = data[:, 0]  # use only the first channel
    samples = np.ascontiguousarray(data)
    return samples, sample_rate


def compute_speaker_embedding(
    filenames: List[str],
    extractor: sherpa_onnx.SpeakerEmbeddingExtractor,
) -> np.ndarray:
    assert len(filenames) > 0, "filenames is empty"

    ans = None
    for filename in filenames:
        print(f"  处理文件: {filename}")
        samples, sample_rate = load_audio(filename)
        stream = extractor.create_stream()
        stream.accept_waveform(sample_rate=sample_rate, waveform=samples)
        stream.input_finished()

        assert extractor.is_ready(stream)
        embedding = extractor.compute(stream)
        embedding = np.array(embedding)
        if ans is None:
            ans = embedding
        else:
            ans += embedding

    return ans / len(filenames)


g_sample_rate = 16000


def main():
    args = get_args()
    print("🚀 开始执行说话人识别脚本")
    print(f"参数: {args}")
    
    # 1. 加载模型
    extractor = load_speaker_embedding_model(args)
    
    # 2. 加载说话人文件
    speaker_file = load_speaker_file(args)

    # 3. 注册说话人
    print("🔄 注册说话人...")
    manager = sherpa_onnx.SpeakerEmbeddingManager(extractor.dim)
    for name, filename_list in speaker_file.items():
        print(f"注册说话人: {name}")
        embedding = compute_speaker_embedding(
            filenames=filename_list,
            extractor=extractor,
        )
        status = manager.add(name, embedding)
        if not status:
            raise RuntimeError(f"Failed to register speaker {name}")
        print(f"✓ {name} 注册成功")

    print("✓ 所有说话人注册完成")

    # 4. 初始化VAD模型
    print("🔄 初始化VAD模型...")
    vad_config = sherpa_onnx.VadModelConfig()
    vad_config.silero_vad.model = args.silero_vad_model
    vad_config.silero_vad.min_silence_duration = 0.25
    vad_config.silero_vad.min_speech_duration = 0.25
    vad_config.sample_rate = g_sample_rate

    window_size = vad_config.silero_vad.window_size
    vad = sherpa_onnx.VoiceActivityDetector(vad_config, buffer_size_in_seconds=100)
    print("✓ VAD模型初始化成功")

    # 5. 查询音频设备
    print("🔄 查询音频设备...")
    try:
        devices = sd.query_devices()
        print(f"✓ 找到 {len(devices)} 个音频设备")
        
        if len(devices) == 0:
            print("❌ 没有找到麦克风设备")
            sys.exit(0)

        print("📋 可用的音频设备:")
        for i, device in enumerate(devices):
            print(f"  {i}: {device['name']} (输入通道: {device['max_input_channels']}, 输出通道: {device['max_output_channels']})")

        default_input_device_idx = sd.default.device[0]
        print(f"🎤 使用默认设备: {devices[default_input_device_idx]['name']}")

    except Exception as e:
        print(f"❌ 查询音频设备时出错: {e}")
        print("这可能是权限问题或音频驱动问题")
        return

    # 6. 开始录音和识别
    print("🎙️ 开始录音，请说话...")
    samples_per_read = int(0.1 * g_sample_rate)  # 0.1 second = 100 ms

    idx = 0
    buffer = []
    
    try:
        with sd.InputStream(channels=1, dtype="float32", samplerate=g_sample_rate) as s:
            print("✓ 音频流创建成功，开始监听...")
            while True:
                samples, _ = s.read(samples_per_read)  # a blocking read
                samples = samples.reshape(-1)
                buffer = np.concatenate([buffer, samples])
                
                while len(buffer) > window_size:
                    vad.accept_waveform(buffer[:window_size])
                    buffer = buffer[window_size:]

                while not vad.empty():
                    if len(vad.front.samples) < 0.5 * g_sample_rate:
                        # this segment is too short, skip it
                        vad.pop()
                        continue
                        
                    stream = extractor.create_stream()
                    stream.accept_waveform(
                        sample_rate=g_sample_rate, waveform=vad.front.samples
                    )
                    vad.pop()
                    stream.input_finished()

                    print("🔄 计算嵌入向量...", end="")
                    embedding = extractor.compute(stream)
                    embedding = np.array(embedding)
                    name = manager.search(embedding, threshold=args.threshold)
                    if not name:
                        name = "未知说话人"
                    print(f"\r{idx}: 识别结果: {name}")
                    idx += 1
                    
    except Exception as e:
        print(f"❌ 录音过程中出错: {e}")
        print("这可能是麦克风权限问题")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 检测到 Ctrl+C，退出程序")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()