import requests
import json
from typing import Optional, List, Dict, Any


class FastGPTDataset:
    """FastGPT知识库操作类"""
    
    def __init__(self, base_url: str = "http://**************:3000", 
                 authorization_token: str = "fastgpt-gZHVXrSu6UqOmvmAKsYeHhtkxdywJBqkwuM8KuZ8ia3TxdGxJqQv6Ic53ctajPJf"):
        """
        初始化FastGPT数据集客户端
        
        Args:
            base_url (str): FastGPT服务器地址
            authorization_token (str): 授权令牌
        """
        self.base_url = base_url.rstrip('/')
        self.auth_token = authorization_token
        self.headers = {
            'Authorization': f'Bearer {self.auth_token}',
            'Content-Type': 'application/json'
        }
    
    def list_dataset_collections(self, dataset_id: str, 
                               offset: int = 0, 
                               page_size: int = 10,
                               parent_id: Optional[str] = None,
                               search_text: str = "") -> Optional[Dict]:
        """
        查询知识库集合列表
        
        Args:
            dataset_id (str): 数据集ID
            offset (int): 偏移量
            page_size (int): 每页大小
            parent_id (str, optional): 父级ID
            search_text (str): 搜索文本
            
        Returns:
            Dict: 响应数据，包含集合列表和总数
        """
        url = f'{self.base_url}/api/core/dataset/collection/listV2'
        
        data = {
            "offset": offset,
            "pageSize": page_size,
            "datasetId": dataset_id,
            "parentId": parent_id,
            "searchText": search_text
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"查询集合列表失败: {e}")
            return None
    
    def get_collection_count(self, dataset_id: str) -> int:
        """
        获取知识库中集合的总数量
        
        Args:
            dataset_id (str): 数据集ID
            
        Returns:
            int: 集合总数量
        """
        result = self.list_dataset_collections(dataset_id, page_size=1)
        if result and 'data' in result:
            return result['data'].get('total', 0)
        return 0    
   
    def split_text_by_length(self, text: str, max_length: int = 1000, 
                           overlap: int = 100) -> List[str]:
        """
        按长度分割文本
        
        Args:
            text (str): 要分割的文本
            max_length (int): 每块最大长度，默认1000字符
            overlap (int): 重叠字符数，默认100字符
            
        Returns:
            List[str]: 分割后的文本块列表
        """
        if len(text) <= max_length:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + max_length
            
            # 如果不是最后一块，尝试在句号、换行符等处分割
            if end < len(text):
                # 寻找合适的分割点
                for i in range(end, max(start + max_length // 2, end - 200), -1):
                    if text[i] in ['。', '！', '？', '\n', '\r\n']:
                        end = i + 1
                        break
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # 设置下一块的起始位置，考虑重叠
            start = max(start + 1, end - overlap)
            
            if start >= len(text):
                break
                
        return chunks
    
    def create_collection(self, dataset_id: str, name: str, 
                         parent_id: Optional[str] = None,
                         type: int = 2,
                         metadata: Optional[Dict] = None) -> Optional[Dict]:
        """
        创建知识库集合
        
        Args:
            dataset_id (str): 数据集ID
            name (str): 集合名称
            parent_id (str, optional): 父级集合ID
            type (int): 集合类型，默认2
            metadata (Dict, optional): 元数据
            
        Returns:
            Dict: 创建结果
        """
        url = f'{self.base_url}/api/core/dataset/collection/create'
        
        data = {
            "datasetId": dataset_id,
            "parentId": parent_id,
            "name": name,
            "type": type,
            "metadata": metadata or {}
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"创建集合失败: {e}")
            return None   
 
    def insert_data_to_collection(self, collection_id: str, 
                                 text_content: str,
                                 max_chunk_length: int = 1000,
                                 chunk_overlap: int = 100,
                                 q: Optional[str] = None,
                                 a: Optional[str] = None) -> Optional[Dict]:
        """
        向集合中插入数据，自动处理文本分块
        
        Args:
            collection_id (str): 集合ID
            text_content (str): 要插入的文本内容
            max_chunk_length (int): 最大分块长度，默认1000字符
            chunk_overlap (int): 分块重叠长度，默认100字符
            q (str, optional): 问题文本
            a (str, optional): 答案文本
            
        Returns:
            Dict: 插入结果
        """
        url = f'{self.base_url}/api/core/dataset/data/insertData'
        
        # 如果文本超过最大长度，进行分块
        if len(text_content) > max_chunk_length:
            text_chunks = self.split_text_by_length(
                text_content, max_chunk_length, chunk_overlap
            )
            print(f"文本过长，已分割为 {len(text_chunks)} 个块")
        else:
            text_chunks = [text_content]
        
        results = []
        
        for i, chunk in enumerate(text_chunks):
            data = {
                "collectionId": collection_id,
                "q": q or f"文档片段 {i+1}",
                "a": a or chunk,
                "indexes": [
                    {
                        "defaultIndex": True,
                        "type": "chunk",
                        "dataId": "",
                        "text": chunk
                    }
                ]
            }
            
            try:
                response = requests.post(url, headers=self.headers, json=data)
                response.raise_for_status()
                result = response.json()
                results.append(result)
                print(f"成功插入第 {i+1} 个文本块")
                
            except requests.exceptions.RequestException as e:
                print(f"插入第 {i+1} 个文本块失败: {e}")
                results.append({"error": str(e)})
        
        return {
            "total_chunks": len(text_chunks),
            "results": results,
            "success_count": len([r for r in results if "error" not in r])
        }
    
    def insert_text_to_dataset(self, dataset_id: str, text_content: str,
                              collection_name: Optional[str] = None,
                              max_chunk_length: int = 1000,
                              chunk_overlap: int = 100) -> Optional[Dict]:
        """
        向数据集插入文本，如果需要会自动创建集合
        
        Args:
            dataset_id (str): 数据集ID
            text_content (str): 要插入的文本内容
            collection_name (str, optional): 集合名称，如果不存在会创建
            max_chunk_length (int): 最大分块长度
            chunk_overlap (int): 分块重叠长度
            
        Returns:
            Dict: 插入结果
        """
        # 如果没有指定集合名称，使用默认名称
        if not collection_name:
            collection_name = "自动创建的集合"
        
        # 查找或创建集合
        collections = self.list_dataset_collections(dataset_id)
        collection_id = None
        
        if collections and 'data' in collections:
            # 查找是否已存在同名集合
            for collection in collections['data'].get('list', []):
                if collection.get('name') == collection_name:
                    collection_id = collection.get('_id')
                    break
        
        # 如果没找到集合，创建新集合
        if not collection_id:
            print(f"创建新集合: {collection_name}")
            create_result = self.create_collection(dataset_id, collection_name)
            if create_result and '_id' in create_result:
                collection_id = create_result['_id']
            else:
                print("创建集合失败")
                return None
        
        # 插入数据到集合
        return self.insert_data_to_collection(
            collection_id, text_content, max_chunk_length, chunk_overlap
        )


# 使用示例和测试函数
def example_usage():
    """使用示例"""
    # 初始化客户端
    client = FastGPTDataset(
        base_url="http://**************:3000",
        authorization_token="fastgpt-gZHVXrSu6UqOmvmAKsYeHhtkxdywJBqkwuM8KuZ8ia3TxdGxJqQv6Ic53ctajPJf"
    )
    
    dataset_id = "6593e137231a2be9c5603ba7"
    
    # 1. 查询知识库有多少集合
    print("=== 查询知识库集合数量 ===")
    collection_count = client.get_collection_count(dataset_id)
    print(f"知识库中共有 {collection_count} 个集合")
    
    # 2. 列出所有集合
    print("\n=== 列出所有集合 ===")
    collections = client.list_dataset_collections(dataset_id)
    if collections and 'data' in collections:
        for collection in collections['data'].get('list', []):
            print(f"集合名称: {collection.get('name')}, ID: {collection.get('_id')}")
    
    # 3. 插入长文本（会自动分块）
    print("\n=== 插入长文本示例 ===")
    long_text = """
    这是一段很长的文本内容，用于演示自动分块功能。
    当文本超过指定长度时，系统会自动将其分割成多个较小的块。
    每个块都会单独插入到知识库中，这样可以提高检索的准确性。
    分块时会考虑句号、换行符等自然分割点，避免在词语中间分割。
    同时还会在相邻块之间保持一定的重叠，确保信息的连续性.* 10  # 重复10次制造长文本
    """
    result = client.insert_text_to_dataset(
        dataset_id=dataset_id,
        text_content=long_text,
        collection_name="测试集合",
        max_chunk_length=500,  # 500字符分块
        chunk_overlap=50       # 50字符重叠
    )
    
    if result:
        print(f"插入完成: 共分为 {result['total_chunks']} 个块")
        print(f"成功插入 {result['success_count']} 个块")


# 兼容原有函数
def list_dataset_collection_with_config(authorization_token=None, dataset_id=None):
    """
    带配置参数的版本，允许自定义授权令牌和数据集ID
    保持与原代码的兼容性
    
    Args:
        authorization_token (str): 授权令牌
        dataset_id (str): 数据集ID
    """
    # API 端点
    url = 'http://**************:3000/api/core/dataset/collection/listV2'
    
    # 使用提供的令牌或默认值
    auth_token = authorization_token or "fastgpt-gZHVXrSu6UqOmvmAKsYeHhtkxdywJBqkwuM8KuZ8ia3TxdGxJqQv6Ic53ctajPJf"
    ds_id = dataset_id or "6593e137231a2be9c5603ba7"
    
    # 请求头
    headers = {
        'Authorization': f'Bearer {auth_token}',
        'Content-Type': 'application/json'
    }
    
    # 请求体数据
    data = {
        "offset": 0,
        "pageSize": 10,
        "datasetId": ds_id,
        "parentId": None,
        "searchText": ""
    }
    
    try:
        # 发送 POST 请求
        response = requests.post(url, headers=headers, json=data)
        # 返回响应对象以便进一步处理
        return response
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None


if __name__ == "__main__":
    # 运行示例
    example_usage()