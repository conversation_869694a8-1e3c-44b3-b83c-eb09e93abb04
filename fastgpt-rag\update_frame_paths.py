import psycopg2
import sys
import os

# 数据库连接参数
DB_CONFIG = {
    "dbname": "datansha-test",
    "user": "GZe9",
    "password": "password_GZe9",
    "host": "**************",
    "port": "5433",
}


def get_database_connection():
    """获取数据库连接"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except psycopg2.Error as e:
        print(f"数据库连接错误: {e}")
        return None


def update_frame_paths():
    """更新frame_path字段的路径前缀"""

    old_prefix = "/mnt/datasets/"
    new_prefix = "/home/<USER>/llm_project/datansha/save_data/datasets/"

    conn = get_database_connection()
    if not conn:
        return

    try:
        cursor = conn.cursor()

        # 检查需要更新的记录数量
        check_query = """
        SELECT COUNT(*) 
        FROM frame_analysis 
        WHERE frame_path LIKE %s
        """
        cursor.execute(check_query, (old_prefix + "%",))
        count = cursor.fetchone()[0]
        print(f"找到 {count} 条需要更新路径的记录")

        if count == 0:
            print("没有找到包含旧路径前缀的记录")
            return

        # 显示示例记录
        sample_query = """
        SELECT id, frame_path 
        FROM frame_analysis 
        WHERE frame_path LIKE %s 
        LIMIT 5
        """
        cursor.execute(sample_query, (old_prefix + "%",))
        samples = cursor.fetchall()

        print("\n示例记录（路径更新）:")
        for record_id, path in samples:
            new_path = path.replace(old_prefix, new_prefix)
            print(f"ID {record_id}:")
            print(f"  旧路径: {path}")
            print(f"  新路径: {new_path}")

        # 确认更新
        response = input(f"\n是否要更新这 {count} 条记录的路径? (y/N): ")
        if response.lower() != "y":
            print("操作已取消")
            return

        # 执行路径更新
        update_query = """
        UPDATE frame_analysis 
        SET frame_path = REPLACE(frame_path, %s, %s)
        WHERE frame_path LIKE %s
        """

        print("正在更新路径...")
        cursor.execute(update_query, (old_prefix, new_prefix, old_prefix + "%"))
        updated_count = cursor.rowcount

        conn.commit()
        print(f"成功更新了 {updated_count} 条记录的路径")

        # 验证更新结果
        cursor.execute(check_query, (old_prefix + "%",))
        remaining = cursor.fetchone()[0]
        print(f"仍包含旧路径前缀的记录数: {remaining}")

    except psycopg2.Error as e:
        print(f"数据库错误: {e}")
        conn.rollback()
    except Exception as e:
        print(f"错误: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("数据库连接已关闭")


def update_camera_ids():
    """更新camera_id字段"""

    # Camera ID映射关系
    camera_id_mappings = {"4088": "4086", "4090": "4087", "4010": "4048"}

    conn = get_database_connection()
    if not conn:
        return

    try:
        cursor = conn.cursor()

        # 检查需要更新的记录数量
        check_query = """
        SELECT COUNT(*) 
        FROM frame_analysis 
        WHERE camera_id IN ('4088', '4090', '4010')
        """
        cursor.execute(check_query)
        count = cursor.fetchone()[0]
        print(f"找到 {count} 条需要更新camera_id的记录")

        if count == 0:
            print("没有找到需要更新的camera_id记录")
            return

        # 显示每个camera_id的记录数量
        detail_query = """
        SELECT camera_id, COUNT(*) 
        FROM frame_analysis 
        WHERE camera_id IN ('4088', '4090', '4010')
        GROUP BY camera_id
        ORDER BY camera_id
        """
        cursor.execute(detail_query)
        details = cursor.fetchall()

        print("\n各camera_id的记录数量:")
        for camera_id, record_count in details:
            new_id = camera_id_mappings.get(camera_id, camera_id)
            print(f"  {camera_id} -> {new_id}: {record_count} 条记录")

        # 显示示例记录
        sample_query = """
        SELECT id, camera_id, video_id, timestamp 
        FROM frame_analysis 
        WHERE camera_id IN ('4088', '4090', '4010')
        ORDER BY camera_id, id
        LIMIT 5
        """
        cursor.execute(sample_query)
        samples = cursor.fetchall()

        print("\n示例记录:")
        for record_id, camera_id, video_id, timestamp in samples:
            new_id = camera_id_mappings.get(camera_id, camera_id)
            print(
                f"ID {record_id}: {camera_id} -> {new_id} (video_id: {video_id}, time: {timestamp})"
            )

        # 确认更新
        response = input(f"\n是否要更新这 {count} 条记录的camera_id? (y/N): ")
        if response.lower() != "y":
            print("操作已取消")
            return

        print("正在更新camera_id...")
        total_updated = 0

        # 逐个更新每个camera_id
        for old_id, new_id in camera_id_mappings.items():
            update_query = """
            UPDATE frame_analysis 
            SET camera_id = %s
            WHERE camera_id = %s
            """
            cursor.execute(update_query, (new_id, old_id))
            updated_count = cursor.rowcount
            total_updated += updated_count
            print(f"  {old_id} -> {new_id}: 更新了 {updated_count} 条记录")

        conn.commit()
        print(f"总共成功更新了 {total_updated} 条记录的camera_id")

        # 验证更新结果
        cursor.execute(check_query)
        remaining = cursor.fetchone()[0]
        print(f"仍需要更新的camera_id记录数: {remaining}")

    except psycopg2.Error as e:
        print(f"数据库错误: {e}")
        conn.rollback()
    except Exception as e:
        print(f"错误: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("数据库连接已关闭")


def update_frame_names():
    """更新frame_path字段中的文件名"""

    conn = get_database_connection()
    if not conn:
        return

    try:
        cursor = conn.cursor()

        # 获取所有frame_path记录用于文件名更新
        select_query = """
        SELECT id, frame_path 
        FROM frame_analysis 
        WHERE frame_path IS NOT NULL AND frame_path != ''
        ORDER BY id
        LIMIT 10
        """
        cursor.execute(select_query)
        records = cursor.fetchall()

        print(f"显示前10条记录用于文件名检查:")

        if len(records) == 0:
            print("没有找到需要更新文件名的记录")
            return

        # 显示示例记录
        print("\n当前文件名示例:")
        for record_id, path in records:
            filename = os.path.basename(path) if path else "无文件名"
            print(f"ID {record_id}: {filename}")

        print("\n文件名更新选项:")
        print("1. 自定义替换规则")
        print("2. 标准化文件名格式")
        print("3. 批量添加前缀/后缀")

        choice = input("请选择操作类型 (1-3): ")

        if choice == "1":
            # 自定义替换规则
            old_pattern = input("请输入要替换的文件名模式: ")
            new_pattern = input("请输入新的文件名模式: ")

            if old_pattern and new_pattern:
                # 检查会影响多少条记录
                check_query = """
                SELECT COUNT(*) 
                FROM frame_analysis 
                WHERE frame_path LIKE %s
                """
                cursor.execute(check_query, ("%" + old_pattern + "%",))
                count = cursor.fetchone()[0]

                print(f"将影响 {count} 条记录")

                if count > 0:
                    confirm = input("确认执行替换? (y/N): ")
                    if confirm.lower() == "y":
                        update_query = """
                        UPDATE frame_analysis 
                        SET frame_path = REPLACE(frame_path, %s, %s)
                        WHERE frame_path LIKE %s
                        """

                        cursor.execute(
                            update_query,
                            (old_pattern, new_pattern, "%" + old_pattern + "%"),
                        )
                        updated_count = cursor.rowcount
                        conn.commit()
                        print(f"成功更新了 {updated_count} 条记录的文件名")
                else:
                    print("没有找到匹配的记录")

        elif choice == "2":
            print("标准化文件名格式:")
            print("将统一文件名格式为: frame_[number]_[timestamp].jpg")

            confirm = input("确认执行标准化? (y/N): ")
            if confirm.lower() == "y":
                # 这里可以添加具体的标准化逻辑
                print("标准化功能需要根据具体需求实现")

        elif choice == "3":
            prefix = input("请输入要添加的前缀 (留空跳过): ")
            suffix = input("请输入要添加的后缀 (留空跳过): ")

            if prefix or suffix:
                # 构建更新查询
                if prefix and suffix:
                    # 同时添加前缀和后缀
                    update_query = """
                    UPDATE frame_analysis 
                    SET frame_path = REGEXP_REPLACE(frame_path, '([^/]+)$', %s || '\\1' || %s)
                    WHERE frame_path IS NOT NULL
                    """
                    cursor.execute(update_query, (prefix, suffix))
                elif prefix:
                    # 只添加前缀
                    update_query = """
                    UPDATE frame_analysis 
                    SET frame_path = REGEXP_REPLACE(frame_path, '([^/]+)$', %s || '\\1')
                    WHERE frame_path IS NOT NULL
                    """
                    cursor.execute(update_query, (prefix,))
                elif suffix:
                    # 只添加后缀
                    update_query = """
                    UPDATE frame_analysis 
                    SET frame_path = REGEXP_REPLACE(frame_path, '([^/]+)$', '\\1' || %s)
                    WHERE frame_path IS NOT NULL
                    """
                    cursor.execute(update_query, (suffix,))

                updated_count = cursor.rowcount
                conn.commit()
                print(f"成功更新了 {updated_count} 条记录的文件名")
        else:
            print("无效选择")

    except psycopg2.Error as e:
        print(f"数据库错误: {e}")
        conn.rollback()
    except Exception as e:
        print(f"错误: {e}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("数据库连接已关闭")


def main():
    """主函数，提供菜单选择"""
    print("=== Frame Analysis 数据更新工具 ===")
    print("1. 更新路径前缀 (frame_path)")
    print("2. 更新camera_id (4088->4086, 4090->4087, 4010->4048)")
    print("3. 更新文件名 (frame_path中的文件名)")
    print("4. 执行路径和camera_id更新")
    print("5. 执行所有更新操作")
    print("0. 退出")

    while True:
        choice = input("\n请选择操作 (0-5): ")

        if choice == "1":
            update_frame_paths()
        elif choice == "2":
            update_camera_ids()
        elif choice == "3":
            update_frame_names()
        elif choice == "4":
            print("执行路径更新...")
            update_frame_paths()
            print("\n执行camera_id更新...")
            update_camera_ids()
        elif choice == "5":
            print("执行路径更新...")
            update_frame_paths()
            print("\n执行camera_id更新...")
            update_camera_ids()
            print("\n执行文件名更新...")
            update_frame_names()
        elif choice == "0":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
