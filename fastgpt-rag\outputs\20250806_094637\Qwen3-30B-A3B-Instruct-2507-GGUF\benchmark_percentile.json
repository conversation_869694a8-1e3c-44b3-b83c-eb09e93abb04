[{"Percentiles": "10%", "TTFT (s)": 18.4956, "ITL (s)": 0.0198, "TPOT (s)": 0.0257, "Latency (s)": 36.0078, "Input tokens": 21, "Output tokens": 376, "Output (tok/s)": 10.1314, "Total (tok/s)": 10.6703}, {"Percentiles": "25%", "TTFT (s)": 25.4948, "ITL (s)": 0.0241, "TPOT (s)": 0.026, "Latency (s)": 46.452, "Input tokens": 26, "Output tokens": 650, "Output (tok/s)": 11.5833, "Total (tok/s)": 12.1939}, {"Percentiles": "50%", "TTFT (s)": 32.7251, "ITL (s)": 0.0262, "TPOT (s)": 0.0265, "Latency (s)": 50.7724, "Input tokens": 28, "Output tokens": 858, "Output (tok/s)": 16.6429, "Total (tok/s)": 17.1944}, {"Percentiles": "66%", "TTFT (s)": 35.7319, "ITL (s)": 0.0273, "TPOT (s)": 0.0268, "Latency (s)": 52.6745, "Input tokens": 31, "Output tokens": 926, "Output (tok/s)": 19.1592, "Total (tok/s)": 19.5478}, {"Percentiles": "75%", "TTFT (s)": 37.24, "ITL (s)": 0.0282, "TPOT (s)": 0.0269, "Latency (s)": 55.6834, "Input tokens": 34, "Output tokens": 986, "Output (tok/s)": 19.9346, "Total (tok/s)": 20.4082}, {"Percentiles": "80%", "TTFT (s)": 38.1999, "ITL (s)": 0.029, "TPOT (s)": 0.0269, "Latency (s)": 57.0157, "Input tokens": 37, "Output tokens": 1026, "Output (tok/s)": 20.6629, "Total (tok/s)": 21.5648}, {"Percentiles": "90%", "TTFT (s)": 40.2973, "ITL (s)": 0.0326, "TPOT (s)": 0.0271, "Latency (s)": 62.7506, "Input tokens": 41, "Output tokens": 1347, "Output (tok/s)": 37.6098, "Total (tok/s)": 38.4529}, {"Percentiles": "95%", "TTFT (s)": 40.5053, "ITL (s)": 0.0383, "TPOT (s)": 0.0281, "Latency (s)": 80.7032, "Input tokens": 45, "Output tokens": 1516, "Output (tok/s)": 38.258, "Total (tok/s)": 39.8362}, {"Percentiles": "98%", "TTFT (s)": 40.5053, "ITL (s)": 0.0521, "TPOT (s)": 0.0281, "Latency (s)": 80.7032, "Input tokens": 45, "Output tokens": 1516, "Output (tok/s)": 38.258, "Total (tok/s)": 39.8362}, {"Percentiles": "99%", "TTFT (s)": 40.5053, "ITL (s)": 0.0669, "TPOT (s)": 0.0281, "Latency (s)": 80.7032, "Input tokens": 45, "Output tokens": 1516, "Output (tok/s)": 38.258, "Total (tok/s)": 39.8362}]