import cv2
import os

def compare_images_by_histogram(path1, path2, method=cv2.HISTCMP_CORREL):
    """
    通过比较灰度直方图来分析两张图片的相似度。
    
    参数:
    path1 (str): 第一张图片的路径。
    path2 (str): 第二张图片的路径。
    method (int): OpenCV中直方图的比较方法。
        - cv2.HISTCMP_CORREL: 相关性。值越高，越相似 (范围-1到1)。我们用这个。
        - cv2.HISTCMP_CHISQR: 卡方。值越低，越相似。
        - cv2.HISTCMP_BHATTACHARYYA: 巴氏距离。值越低，越相似。
    """
    # 以灰度模式读取图片
    img1 = cv2.imread(path1, cv2.IMREAD_GRAYSCALE)
    img2 = cv2.imread(path2, cv2.IMREAD_GRAYSCALE)

    if img1 is None or img2 is None:
        print("错误: 无法读取一张或两张图片。请检查路径。")
        return

    # 计算两张图片的直方图
    # 参数: [图片], [通道], [掩码], [直方图大小], [像素值范围]
    hist1 = cv2.calcHist([img1], [0], None, [256], [0, 256])
    hist2 = cv2.calcHist([img2], [0], None, [256], [0, 256])

    # 归一化直方图，使其不受图片大小影响
    cv2.normalize(hist1, hist1, 0, 1, cv2.NORM_MINMAX)
    cv2.normalize(hist2, hist2, 0, 1, cv2.NORM_MINMAX)

    # 比较直方图
    similarity = cv2.compareHist(hist1, hist2, method)
    
    filename1 = os.path.basename(path1)
    filename2 = os.path.basename(path2)
    
    print("--- 直方图分析报告 ---")
    print(f"图片: '{filename1}' vs '{filename2}'")
    # 我们用的是相关性方法，得分在1附近表示非常相似
    print(f"亮度分布相似度 (相关性): {similarity:.4f}")
    
    # 结合平均亮度进行综合判断
    avg_brightness1 = img1.mean()
    avg_brightness2 = img2.mean()
    print(f"平均亮度: {avg_brightness1:.2f} -> {avg_brightness2:.2f}")

    print("-" * 25)
    if similarity > 0.9:
        print("结论: 两张图片的整体亮度和色调分布非常相似。")
    elif similarity < 0.5:
        print("结论: 两张图片的整体亮度和色调分布差异巨大。")
    else:
        print("结论: 两张图片的亮度和色调分布有一定差异。")

    # 再次使用平均亮度判断方向
    if avg_brightness2 > avg_brightness1 + 5: # 加一个小的缓冲
        print("从平均值看，图片2比图片1更亮。")
    elif avg_brightness2 < avg_brightness1 - 5:
        print("从平均值看，图片2比图片1更暗。")


# --- 使用示例 ---
if __name__ == "__main__":
    image_path_1 = "frame_4046_2025_07_24_12_16_53.jpg"  # 第一张图片路径
    image_path_2 = "frame_4046_2025_07_24_18_46_54.jpg"  # 第二张图片路径

    
    compare_images_by_histogram(image_path_1, image_path_2)

    print("\n比较一张图片和它自己：")
    compare_images_by_histogram(image_path_1, image_path_1)