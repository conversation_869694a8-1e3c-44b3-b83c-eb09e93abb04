{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Reasoning]:\n", "Greetings! I'm <PERSON><PERSON><PERSON>-<PERSON><PERSON>, an artificial intelligence assistant created by DeepSeek. I'm at your service and would be delighted to assist you with any inquiries or tasks you may have.\n", "\n", "[Reply]:\n", "Greetings! I'm <PERSON><PERSON><PERSON>-<PERSON><PERSON>, an artificial intelligence assistant created by DeepSeek. I'm at your service and would be delighted to assist you with any inquiries or tasks you may have."]}], "source": ["import Agently\n", "agent = (\n", "    Agently.create_agent()\n", "        .set_settings(\"current_model\", \"OAIClient\")\n", "        .set_settings(\"model.OAIClient.url\", \"https://api.deepseek.com\")\n", "        .set_settings(\"model.OAIClient.auth\", { \"api_key\": \"<API-Key>\" })\n", "        .set_settings(\"model.OAIClient.options\", { \"model\": \"deepseek-reasoner\" })\n", ")\n", "\n", "response = agent.input(input(\"[Ask Something]:\")).get_generator()\n", "\n", "is_reasoning = False\n", "for event, data in response:\n", "    if event == \"response:reasoning_delta\":\n", "        if not is_reasoning:\n", "            print(\"[Reasoning]:\")\n", "            is_reasoning = True\n", "        print(data, end=\"\", flush=True)\n", "    if event == \"response:delta\":\n", "        if is_reasoning:\n", "            print(\"\\n\\n[Reply]:\")\n", "            is_reasoning = False\n", "        print(data, end=\"\", flush=True)"]}], "metadata": {"kernelspec": {"display_name": "3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}