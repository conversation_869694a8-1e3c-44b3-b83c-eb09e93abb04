{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyM5fi+7Z1ge2woBnqCwbXwe", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/%E7%BA%BF%E4%B8%8A%E5%BF%AB%E9%80%9F%E8%AF%95%E7%94%A8%E6%A8%A1%E6%9D%BF.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["## **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_ 3.0 线上快速试用模板**"], "metadata": {"id": "xSv8xcNocskE"}}, {"cell_type": "markdown", "source": ["## 一、安装运行所需包文件\n", "\n", "⬇️ 点击下方的执行按钮：( ▸ )，等待包安装完毕即可（通常需要大概1分钟左右）"], "metadata": {"id": "SnfVg7evc0I_"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2io053h7cbXx"}, "outputs": [], "source": ["!pip install Agently"]}, {"cell_type": "markdown", "source": ["## 二、设置你需要使用的模型，然后开始编写你自己的代码\n", "\n", "ℹ️ 不知道如何编写，或者不知道该写点什么？\n", "\n", "你可以：\n", "\n", "- 阅读 [📚 Agently 3.0 应用开发手册](https://github.com/Maplemx/Agently/blob/main/docs/guidebook/application_development_handbook.ipynb) 来进一步了解如何使用Agently框架编写应用\n", "\n", "- 浏览 [💡 Agently 案例广场](https://github.com/Maplemx/Agently/tree/main/playground) 来获取灵感\n", "\n", "你也可以：\n", "\n", "- 根据注释填入你的API-Key然后直接运行下面的样例代码，再在样例代码的基础上进行修改"], "metadata": {"id": "CwCNtHWhdquA"}}, {"cell_type": "code", "source": ["import Agently\n", "agent_factory = Agently.AgentFactory()\n", "\n", "# 模型设置\n", "\n", "## 选择：OpenAI - GPT 3.5-turbo | GPT 4 | GPT 4 Vision\n", "agent_factory\\\n", "    .set_settings(\"current_model\", \"OpenAI\")\\\n", "    .set_settings(\"model.OpenAI.auth\", { \"api_key\": \"\" })\n", "    ## 更换Base URL（可选）\n", "    ## 如果你使用了其他服务供应商提供的转发接口\n", "    ## 或者使用了FastAPI等遵循OpenAI Client规范的本地模型服务代理工具，需要更换Base URL\n", "    ## 请使用下面的设置来设置Base URL\n", "    #.set_settings(\"model.OpenAI.url\", \"https://redirect-service-provider/api/v1\")\\\n", "    ## 调整模型请求参数options（可选）\n", "    ## 如果你需要切换模型，或是调整请求参数，可以使用下面的设置进行调整\n", "    ## 参数说明请参考OpenAI Platform的官方文档\n", "    #.set_settings(\"model.OpenAI.options\", { \"model\": \"gpt-4\" })\\\n", "    ## ❗️使用代理（可选）\n", "    ## 如果你在本地机器使用了比如Clash、VPN、V2Ray等代理工具来访问OpenAI的API\n", "    ## 你需要查看这些代理工具提供的代理服务地址，并填入下面的设置\n", "    ## 注意：使用Google Colab时一般不需要设置代理\n", "    #.set_proxy(\"http://127.0.0.1:7890\")\n", "\n", "## 选择：智谱AI - ChatGLM-turbo | CharacterGLM\n", "## 如何获取：https://open.bigmodel.cn/overview\n", "'''\n", "agent_factory\\\n", "    .set_settings(\"current_model\", \"ZhipuAI\")\\\n", "    .set_settings(\"model.ZhipuAI.auth\", { \"api_key\": \"\" })\n", "'''\n", "## 选择：百度文心大模型\n", "## 如何获取：https://aistudio.baidu.com/index\n", "'''\n", "agent_factory\\\n", "    .set_settings(\"current_model\", \"ERNIE\")\\\n", "    .set_settings(\"model.ERNIE.auth\", {\n", "        \"aistudio\": \"\",\n", "    })\n", "'''\n", "\n", "# 开始试用\n", "## 你可以使用下面的方式打开调试模式（debug mode），打印请求数据，并获得实时输出反馈\n", "## 注意：调试模式和样例代码中的.on_delta(lambda data: print(data, end=\"\"))\n", "##      会产生显示冲突，打开后请手动删除.on_delta(lambda data: print(data, end=\"\"))\n", "#agent_factory.set_settings(\"is_debug\", True)\n", "\n", "## 在这里编写你的代码：\n", "agent = agent_factory.create_agent()\n", "agent.active_session()\n", "while True:\n", "    user_input = input(\"[用户]: \")\n", "    if user_input == \"#exit\":\n", "        break\n", "    print(\"[AGENT]: \", end=\"\")\n", "    agent\\\n", "        .input(user_input)\\\n", "        .on_delta(lambda data: print(data, end=\"\"))\\\n", "        .start()\n", "    print(\"\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SaAiE5-ceQrf", "outputId": "7584cb4e-9622-43b0-c2aa-5bc6499226d7"}, "execution_count": 3, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[用户]: 你好\n", "[AGENT]: 你好！有什么我可以帮助你的吗？\n", "[用户]: 我该怎么使用pip安装python包\n", "[AGENT]: 要使用pip安装Python包，可以按照以下步骤进行操作：\n", "\n", "1. 确保你已经安装了Python，并且安装路径已经加入到系统环境变量中。\n", "\n", "2. 打开命令行界面（或者终端），输入以下命令来检查是否已经安装了pip：\n", "```\n", "pip --version\n", "```\n", "如果提示未找到命令，表示未安装pip，可以参考官方文档安装pip：https://pip.pypa.io/en/stable/installation/\n", "\n", "3. 在命令行界面中，通过以下命令安装Python包：\n", "```\n", "pip install package_name\n", "```\n", "将`package_name`替换为你要安装的具体包的名称。例如，要安装`numpy`包，可以使用以下命令：\n", "```\n", "pip install numpy\n", "```\n", "4. 等待安装完成。pip会自动从Python Package Index（PyPI）下载包，并安装到你的Python环境中。\n", "\n", "注意：如果你使用的是Python 2和Python 3并存的情况下，请使用对应版本的pip，即`pip2`或`pip3`，例如：\n", "```\n", "pip3 install package_name\n", "```\n", "\n", "希望以上步骤对你有所帮助！如果还有其他问题，请随时提问。\n", "[用户]: 我想安装的包名字是Agently，我怎么样保证安装的是最新版本？\n", "[AGENT]: 要确保安装的是最新版本的包，可以使用`--upgrade`参数来执行升级操作。具体步骤如下：\n", "\n", "1. 打开命令行界面（或者终端），输入以下命令来安装Agently包的最新版本：\n", "```\n", "pip install --upgrade Agently\n", "```\n", "这将会检查你当前已安装的Agently包的版本，如果已经安装了旧版本的包，它将会被升级为最新版本。\n", "\n", "2. 或者，如果你已经安装了Agently包，可以使用以下命令来仅升级包到最新版本：\n", "```\n", "pip install --upgrade Agently\n", "```\n", "这将会检查并升级Agently包到最新版本，如果已经安装了最新版本的包，它将不会再次下载和安装。\n", "\n", "通过使用`--upgrade`参数，你可以确保安装最新版本的Agently包。希望这对你有所帮助！如果还有问题，请随时提问。\n", "[用户]: 我们刚才说了什么？\n", "[AGENT]: 您提问了两个问题：\n", "1. 您询问了如何使用pip安装Python包。\n", "2. 您问如何确保安装的是最新版本的包。\n", "\n", "我回答了这两个问题，并提供了相应的步骤和命令。首先，我解释了如何使用pip安装Python包，并提供了检查pip安装和安装pip的官方文档链接。其次，我解释了如何确保安装最新版本的包，通过使用`--upgrade`参数来进行升级操作。我还提供了具体的命令示例。\n", "\n", "请您确认一下，是否还有其他问题需要解答？\n", "[用户]: #exit\n"]}]}]}