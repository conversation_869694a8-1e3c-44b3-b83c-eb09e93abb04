# 图片HTTP服务器项目

## 项目概述

这是一个基于FastAPI的高性能图片HTTP服务器，专门用于将固定文件夹中的图片通过HTTP接口提供访问。

**目标服务器配置：**
- IP地址：**************
- 端口：23333
- 图片目录：/home/<USER>/llm_project/images

## 项目结构

```
image_http_server/
├── image_server.py          # 主服务器文件
├── config.py               # 配置管理
├── start_server.py         # 开发环境启动脚本
├── production_start.py     # 生产环境启动脚本
├── test_server.py          # 服务器测试脚本
├── requirements.txt        # Python依赖包
├── deploy.sh              # Linux部署脚本
├── README.md              # 详细文档
├── 项目说明.md             # 项目说明（本文件）
└── test_images/           # 测试图片目录
```

## 快速开始

### 1. 安装依赖

```bash
cd image_http_server
pip install -r requirements.txt
```

### 2. 启动服务器

**开发环境（本地测试）：**
```bash
python start_server.py
```
- 服务地址：http://127.0.0.1:23333

**生产环境（目标服务器）：**
```bash
python production_start.py
```
- 服务地址：http://**************:23333

### 3. 测试服务

```bash
python test_server.py
```

## 主要功能

### 1. 图片文件访问
直接通过URL访问图片文件：
```
http://**************:23333/images/02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c
```

### 2. 图片信息API
获取图片的详细信息：
```
http://**************:23333/api/images/02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c
```

### 3. 图片列表API
列出所有可用图片：
```
http://**************:23333/api/images
```

### 4. API文档
自动生成的交互式API文档：
```
http://**************:23333/docs
```

## 部署说明

### Linux服务器部署

1. **上传文件到服务器**
   ```bash
   scp -r image_http_server/ user@**************:/path/to/project/
   ```

2. **在服务器上执行部署脚本**
   ```bash
   cd image_http_server
   chmod +x deploy.sh
   ./deploy.sh
   ```

3. **手动启动（如果需要）**
   ```bash
   python3 production_start.py
   ```

### Windows服务器部署

1. 复制整个 `image_http_server` 文件夹到目标位置
2. 安装依赖：`pip install -r requirements.txt`
3. 启动服务：`python production_start.py`

## 配置说明

### 环境变量配置

可以通过环境变量自定义配置：

```bash
export IMAGE_SERVER_HOST="**************"
export IMAGE_SERVER_PORT="23333"
export IMAGES_DIR="/home/<USER>/llm_project/images"
export LOG_LEVEL="INFO"
```

### 图片目录

- **生产目录**：`/home/<USER>/llm_project/images`
- **测试目录**：`./test_images`（当生产目录不存在时自动使用）

## 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- BMP (.bmp)
- WebP (.webp)
- TIFF (.tiff)
- 无扩展名文件（如哈希格式文件名）

## 安全特性

- CORS跨域支持
- 请求日志记录
- 错误处理和异常捕获
- 文件存在性验证

## 性能特性

- 异步处理
- 静态文件缓存
- 高效的文件服务
- 支持多worker进程

## 监控和维护

### 健康检查
```bash
curl http://**************:23333/health
```

### 日志查看
```bash
tail -f image_server.log
```

### 服务状态
```bash
# 检查端口占用
netstat -tulpn | grep 23333

# 检查进程
ps aux | grep image_server
```

## 故障排除

### 常见问题

1. **端口被占用**
   - 修改配置中的端口号
   - 或停止占用端口的进程

2. **图片目录不存在**
   - 检查目录路径是否正确
   - 确保有读取权限
   - 服务器会自动创建测试目录作为备用

3. **依赖包缺失**
   - 重新安装：`pip install -r requirements.txt`

4. **无法访问服务**
   - 检查防火墙设置
   - 确认服务器IP和端口配置
   - 查看服务器日志

## 扩展功能

可以轻松扩展的功能：
- 图片上传接口
- 图片缩略图生成
- 用户认证和权限控制
- 图片缓存和CDN集成
- 图片格式转换
- 批量操作API

## 技术栈

- **FastAPI**: 现代高性能Web框架
- **Uvicorn**: ASGI服务器
- **Pydantic**: 数据验证和序列化
- **Python 3.10+**: 编程语言

## 联系信息

如有问题或需要支持，请查看详细文档 `README.md` 或联系开发团队。
