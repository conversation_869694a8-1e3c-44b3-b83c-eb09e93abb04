// Main JavaScript file for Kiro Dev Clone

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function () {
    console.log('Kiro Dev Clone loaded');

    // Initialize components
    initNavigation();
    initInteractions();
});

// Navigation functionality
function initNavigation() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function () {
            const isOpen = navToggle.getAttribute('aria-expanded') === 'true';

            // Toggle aria-expanded attribute
            navToggle.setAttribute('aria-expanded', !isOpen);

            // Toggle menu visibility
            navMenu.classList.toggle('nav__menu--open');

            // Update button accessibility
            navToggle.setAttribute('aria-label',
                isOpen ? 'Open navigation menu' : 'Close navigation menu'
            );
        });

        // Close menu when clicking on nav links (mobile)
        const navLinks = navMenu.querySelectorAll('.nav__link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth < 768) {
                    navMenu.classList.remove('nav__menu--open');
                    navToggle.setAttribute('aria-expanded', 'false');
                    navToggle.setAttribute('aria-label', 'Open navigation menu');
                }
            });
        });

        // Close menu when clicking outside (mobile)
        document.addEventListener('click', (e) => {
            if (window.innerWidth < 768 &&
                !navToggle.contains(e.target) &&
                !navMenu.contains(e.target) &&
                navMenu.classList.contains('nav__menu--open')) {
                navMenu.classList.remove('nav__menu--open');
                navToggle.setAttribute('aria-expanded', 'false');
                navToggle.setAttribute('aria-label', 'Open navigation menu');
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 768) {
                navMenu.classList.remove('nav__menu--open');
                navToggle.setAttribute('aria-expanded', 'false');
                navToggle.setAttribute('aria-label', 'Open navigation menu');
            }
        });
    }
}

// General interactions
function initInteractions() {
    // Hero section interactions
    initHeroAnimations();

    // Feature cards interactions
    initFeatureCards();

    // Smooth scrolling for anchor links
    initSmoothScrolling();
}

// Hero section animations and interactions
function initHeroAnimations() {
    const heroButtons = document.querySelectorAll('.hero .btn');

    // Add ripple effect to buttons
    heroButtons.forEach(button => {
        button.addEventListener('click', function (e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Parallax effect for hero background
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero');
        if (hero) {
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        }
    });
}

// Feature cards interactions
function initFeatureCards() {
    const featureCards = document.querySelectorAll('.feature-card');

    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                // Add staggered animation delay
                setTimeout(() => {
                    entry.target.classList.add('feature-card--visible');
                }, index * 100);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    featureCards.forEach(card => {
        card.classList.add('feature-card--hidden');
        observer.observe(card);

        // Add focus states for accessibility
        card.addEventListener('focus', function () {
            this.classList.add('feature-card--focused');
        });

        card.addEventListener('blur', function () {
            this.classList.remove('feature-card--focused');
        });
    });
}

// Smooth scrolling for navigation links
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80; // Account for fixed nav

                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}