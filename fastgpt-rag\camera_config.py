# 海康摄像头配置文件

# 摄像头连接信息
CAMERA_CONFIG = {
    "ip": "*************",      # 摄像头IP地址
    "port": "554",              # RTSP端口，通常是554
    "username": "admin",        # 用户名
    "password": "password",     # 密码
    "channel": "101",           # 通道号，主码流通常是101，子码流是201
}

# 捕获设置
CAPTURE_CONFIG = {
    "interval": 5,              # 保存间隔（秒）
    "output_dir": "captured_frames",  # 输出目录
    "show_preview": True,       # 是否显示预览窗口
    "image_quality": 95,        # JPEG质量 (1-100)
}

# 生成RTSP URL
def get_rtsp_url():
    return f"rtsp://{CAMERA_CONFIG['username']}:{CAMERA_CONFIG['password']}@{CAMERA_CONFIG['ip']}:{CAMERA_CONFIG['port']}/Streaming/Channels/{CAMERA_CONFIG['channel']}"

# 常见的海康摄像头RTSP地址格式：
# 主码流: rtsp://用户名:密码@IP:554/Streaming/Channels/101
# 子码流: rtsp://用户名:密码@IP:554/Streaming/Channels/201
# 第三码流: rtsp://用户名:密码@IP:554/Streaming/Channels/301