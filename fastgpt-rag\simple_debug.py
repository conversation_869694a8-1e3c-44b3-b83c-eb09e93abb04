#!/usr/bin/env python3
"""
简化的数据集调试脚本
"""

import json
import os
from pathlib import Path

def create_test_dataset():
    """创建测试数据集"""
    print("=== 创建测试数据集 ===")
    
    test_data = [
        {
            "prompt": "什么是人工智能？",
            "response": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"
        },
        {
            "prompt": "Python是什么？", 
            "response": "Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。"
        },
        {
            "prompt": "机器学习的基本概念是什么？",
            "response": "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。"
        },
        {
            "prompt": "深度学习与传统机器学习有什么区别？",
            "response": "深度学习使用多层神经网络来学习数据的复杂模式，而传统机器学习通常使用更简单的算法。"
        },
        {
            "prompt": "什么是自然语言处理？",
            "response": "自然语言处理是人工智能的一个分支，专注于使计算机能够理解、解释和生成人类语言。"
        }
    ]
    
    filename = "test_openqa.jsonl"
    with open(filename, 'w', encoding='utf-8') as f:
        for item in test_data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    print(f"创建测试数据集: {filename}")
    print(f"包含 {len(test_data)} 条记录")
    
    # 验证文件内容
    print("\n验证文件内容:")
    with open(filename, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            data = json.loads(line.strip())
            print(f"  记录{i+1}: prompt长度={len(data['prompt'])}, response长度={len(data['response'])}")
    
    return filename

def find_cache_directories():
    """查找可能的缓存目录"""
    print("\n=== 查找缓存目录 ===")
    
    possible_dirs = [
        Path.home() / ".cache" / "evalscope",
        Path.home() / ".cache" / "huggingface",
        Path.home() / ".evalscope",
        Path("./cache"),
        Path("./datasets")
    ]
    
    for dir_path in possible_dirs:
        if dir_path.exists():
            print(f"找到目录: {dir_path}")
            try:
                files = list(dir_path.rglob("*.jsonl"))
                if files:
                    print(f"  包含的jsonl文件:")
                    for file in files:
                        size_mb = file.stat().st_size / (1024*1024)
                        print(f"    {file.name}: {size_mb:.2f} MB")
            except Exception as e:
                print(f"  读取目录时出错: {e}")
        else:
            print(f"目录不存在: {dir_path}")

def check_evalscope_config():
    """检查evalscope配置"""
    print("\n=== 检查evalscope配置 ===")
    
    try:
        from evalscope.perf.arguments import Arguments
        
        # 测试不同的配置
        configs = [
            {
                "name": "原始配置",
                "min_tokens": 1024,
                "max_tokens": 1024,
                "min_prompt_length": 1024,
                "max_prompt_length": 1024
            },
            {
                "name": "宽松配置",
                "min_tokens": 1,
                "max_tokens": 2048,
                "min_prompt_length": 1,
                "max_prompt_length": 2048
            }
        ]
        
        for config in configs:
            print(f"\n测试{config['name']}:")
            try:
                args = Arguments(
                    dataset='openqa',
                    min_tokens=config['min_tokens'],
                    max_tokens=config['max_tokens'],
                    min_prompt_length=config['min_prompt_length'],
                    max_prompt_length=config['max_prompt_length'],
                    number=[1]
                )
                print(f"  配置创建成功")
                print(f"  min_tokens: {args.min_tokens}")
                print(f"  max_tokens: {args.max_tokens}")
                print(f"  min_prompt_length: {args.min_prompt_length}")
                print(f"  max_prompt_length: {args.max_prompt_length}")
                
            except Exception as e:
                print(f"  配置创建失败: {e}")
                
    except ImportError as e:
        print(f"无法导入evalscope: {e}")

def main():
    """主函数"""
    print("开始简化调试...")
    
    # 1. 查找缓存目录
    find_cache_directories()
    
    # 2. 检查evalscope配置
    check_evalscope_config()
    
    # 3. 创建测试数据集
    test_file = create_test_dataset()
    
    print(f"\n=== 建议解决方案 ===")
    print("1. 数据集为空的原因可能是:")
    print("   - 下载的数据集文件损坏")
    print("   - 过滤条件太严格(min_tokens=1024太高)")
    print("   - 数据格式不匹配")
    print()
    print("2. 解决方案:")
    print("   - 已修改model-evalue.py中的参数，降低了限制")
    print("   - 可以使用创建的测试数据集进行测试")
    print(f"   - 测试文件: {test_file}")
    print()
    print("3. 下一步:")
    print("   - 重新运行model-evalue.py")
    print("   - 如果还有问题，可以尝试使用其他数据集")

if __name__ == "__main__":
    main()