from openai import OpenAI

model = "xiyan-sql"
client = OpenAI(api_key="ragflow-M1MDcyNjE0NjA1YTExZjBiZjI0NzYwMj", base_url=f"http://localhost:6006/v1")

completion = client.chat.completions.create(
    model=model,
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "介绍一下你自己,600字?"},
    ],
    stream=True
)

stream = True
if stream:
    for chunk in completion:
        print(chunk)
else:
    print(completion.choices[0].message.content)
# print(completion.choices[0].message.content)


# SQL 查询生成
# response = client.chat.completions.create(
#     model="xiyan-sql",
#     messages=[
#         {"role": "user", "content": "查询所有用户"}
#     ],
#     # SQL 相关参数
#     extra_body={
#         "dialect": "MySQL",
#         "db_schema": "CREATE TABLE users (id INT, name VARCHAR(100));",
#         "evidence": "简单查询"
#     }
# )
# print(response.choices[0].message.content)
# import openai

# # 配置客户端
# client = openai.OpenAI(
#     api_key="dummy-key",  # XiYanSQL 不需要真实 API key
#     base_url="http://localhost:6006/v1"
# )

# # 普通聊天
# response = client.chat.completions.create(
#     model="xiyan-sql",
#     messages=[
#         {"role": "user", "content": "你好"}
#     ]
# )
# print(response.choices[0].message.content)

# # SQL 查询生成
# response = client.chat.completions.create(
#     model="xiyan-sql",
#     messages=[
#         {"role": "user", "content": "查询所有用户"}
#     ],
#     # SQL 相关参数
#     extra_body={
#         "dialect": "MySQL",
#         "db_schema": "CREATE TABLE users (id INT, name VARCHAR(100));",
#         "evidence": "简单查询"
#     }
# )
# print(response.choices[0].message.content)