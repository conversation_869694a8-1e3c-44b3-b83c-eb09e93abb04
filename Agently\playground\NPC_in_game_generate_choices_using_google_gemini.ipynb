{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/NPC_in_game_generate_choices_using_google_gemini.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# NPC in Game Generate Choices Using Google Gemini"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** English\n", "\n", "**Agent Components:** Role\n", "\n", "**Description:**\n", "\n", "Today's show case is a simple demo shows how can we generate a NPC's line and choice options in game.\n", "\n", "This case presents how to use model Google Gemini Pro with Agently framework. You'll find the application codes are not different from using OpenAI GPT. That's an important meaning why we should use framework instead of request model directly.\n", "\n", "今天的案例是一个非常简单的案例，它展示了我们如何使用模型生成一个游戏中的NPC的一次台词和可选决策项。\n", "\n", "同时也演示了Agently框架对Google Gemini的适配。你会发现使用Agently框架进行开发，并不会因为切换不同的模型而对应用层逻辑的编写产生巨大的冲击，甚至可能毫无冲击。这也说明了为什么我们在应用开发的时候应该选择框架，而不是直接裸调用模型——将业务逻辑和模型解耦，可以让你在模型更新或是有更多选择的时候，不会因为业务代码和模型绑定过深导致业务逻辑需要重新开发。\n", "\n", "同时，对于中国开发者而言，如何设置本地代理（Proxy）去请求Gemini Pro也是一个需要解决的问题，Google官方并没有提供对应的方案，但Agently提供了支持使用代理，同时还可以完美适配流式请求、Agently表达方式的解决方案。详细可参看[附录部分](#scrollTo=0zzWYyyKcKHL)。"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Step 1: Install Packages"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install -U Agently"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Demo Code"], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "cccd02a7-0a6b-4114-f38f-8ae9215439c2"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Request Data]\n", " {\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",\n", "            \"parts\": [\n", "                {\n", "                    \"text\": \"[YOUR ROLE SETTINGS]?\"\n", "                }\n", "            ]\n", "        },\n", "        {\n", "            \"role\": \"model\",\n", "            \"parts\": [\n", "                {\n", "                    \"text\": \"DESC:\\n- a timid girl trying to find the way home\\n\"\n", "                }\n", "            ]\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"parts\": [\n", "                {\n", "                    \"text\": \"# [INPUT]:\\nenv: a gloomy cave\\ntask: find the way out\\n\\n\\n# [OUTPUT REQUIREMENT]:\\n## TYPE:\\nJSON can be parsed in Python\\n## FORMAT:\\n{\\n\\t\\\"line\\\": <String>,//your sign to the envrionment:{input.env}\\n\\t\\\"next_action_choices\\\": \\n\\t[\\n\\t\\t<String>,//one choice,\\n\\t\\tbased on {input.task} and {input.env}, give some different choices for what to do next,\\n\\t\\t...\\n\\t],\\n}\\n\\n\\n[OUTPUT]:\\n\"\n", "                }\n", "            ]\n", "        }\n", "    ],\n", "    \"options\": {}\n", "}\n", "[Realtime Response]\n", "\n", "```json\n", "{\n", "\t\"line\": \"You nervously stumble through the cave, unsure of which way to go.\",\n", "\t\"next_action_choices\": [\n", "\t\t\"Move forward cautiously.\",\n", "\t\t\"Turn around and go back the way you came.\",\n", "\t\t\"Call out for help.\"\n", "\t]\n", "}\n", "```\n", "--------------------------\n", "\n", "[Final Reply]\n", " ```json\n", "{\n", "\t\"line\": \"You nervously stumble through the cave, unsure of which way to go.\",\n", "\t\"next_action_choices\": [\n", "\t\t\"Move forward cautiously.\",\n", "\t\t\"Turn around and go back the way you came.\",\n", "\t\t\"Call out for help.\"\n", "\t]\n", "}\n", "``` \n", "--------------------------\n", "\n", "[Cleaned JSON String]:\n", " {\"line\":\"You nervously stumble through the cave, unsure of which way to go.\",\"next_action_choices\":[\"Move forward cautiously.\",\"Turn around and go back the way you came.\",\"Call out for help.\"]}\n", "\n", "--------------------------\n", "\n", "[Parse JSON to Dict] Done\n", "\n", "--------------------------\n", "\n", "[LINE]:\n", " You nervously stumble through the cave, unsure of which way to go.\n", "[CHOICE 1] Move forward cautiously.\n", "[CHOICE 2] Turn around and go back the way you came.\n", "[CHOICE 3] Call out for help.\n"]}], "source": ["import Agently\n", "\n", "# Turn on debug mode, you'll see Agently framework supports streaming output\n", "# using Google Gemini Pro\n", "agent_factory = Agently.AgentFactory(is_debug=True)\n", "\n", "# Google Gemini's settings are similar with OpenAI's settings\n", "(\n", "    agent_factory\n", "        .set_settings(\"current_model\", \"Google\")\n", "        .set_settings(\"model.Google.auth\", { \"api_key\": \"\" })\n", ")\n", "agent = agent_factory.create_agent()\n", "\n", "\n", "result = (\n", "    agent\\\n", "        .set_role(\"a timid girl trying to find the way home\")\n", "        .input({\n", "            \"env\": \"a gloomy cave\",\n", "            \"task\": \"find the way out\",\n", "        })\n", "        .output({\n", "            \"line\": (\"String\", \"your sign to the envrionment:{input.env}\"),\n", "            \"next_action_choices\": [\n", "                (\"String\", \"one choice\"),\n", "                \"based on {input.task} and {input.env}, give some different choices for what to do next\"\n", "            ],\n", "        })\n", "        .start()\n", "    )\n", "print(\"[LINE]:\\n\", result[\"line\"])\n", "for index, choice in enumerate(result[\"next_action_choices\"]):\n", "    print(f\"[CHOICE { str(index + 1) }]\", choice)"]}, {"cell_type": "markdown", "source": ["## Appendix: Using Proxy to Request Gemini Pro\n", "\n", "As we know, Google offical model client does not provide the settings or parameters to allow user setting proxies. But for some reasons, developers need to use proxy client to access and request Gemini Pro. So these codes below shows how to set proxy with Agently framework.\n", "\n", "众所周知，Google官方客户端没有提供配置代理的方案，但是我们开发者，尤其是国内开发者，需要使用代理才能访问和使用Gemini Pro。下面的代码展示了使用Agently框架配置代理，并对Gemini Pro发起请求。\n", "\n"], "metadata": {"id": "0zzWYyyKcKHL"}}, {"cell_type": "code", "source": ["import Agently\n", "\n", "agent_factory = Agently.AgentFactory()\n", "\n", "(\n", "    agent_factory\n", "        .set_settings(\"current_model\", \"Google\")\n", "        .set_settings(\"model.Google.auth\", { \"api_key\": \"\" })\n", ")\n", "\n", "# It's very easy: you just add one settings like this and that's done\n", "agent_factory.set_settings(\"proxy\", \"http://127.0.0.1:7890\")\n", "\n", "agent = agent_factory.create_agent()\n", "\n", "# Simple Request:\n", "# if you don't want to use any other features provided by <PERSON><PERSON>\n", "# you can just request Gemini Pro like this\n", "# it's the same as the directly request\n", "result = agent.input(\"Give me 3 words\").start()\n", "print(result)\n", "\n", "print(\"==============\")\n", "\n", "# Request with Chat History:\n", "# Agently using OpenAI-like messages format\n", "# and convert them automatically to Gemini Pro required\n", "result = (\n", "    agent\n", "        .chat_history([\n", "            { \"role\": \"user\", \"content\": \"remember me to buy some eggs from supermarket\" },\n", "            { \"role\": \"assistant\", \"content\": \"Sure.\" },\n", "            { \"role\": \"user\", \"content\": \"oh we need some milk too.\" },\n", "            { \"role\": \"assistant\", \"content\": \"Will do.\" },\n", "        ])\n", "        .input(\"What did we say?\")\n", "        .start()\n", ")\n", "print(result)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Y5nFwivNeM1u", "outputId": "7c6b24e0-b761-411a-8433-2a7870c4c762"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["* Serenity\n", "* Wanderlust\n", "* Euphoria\n", "==============\n", "We said that you need to buy some eggs and milk from the supermarket.\n"]}]}, {"cell_type": "markdown", "source": ["### Where can I find proxy address?\n", "\n", "Take `Clash for Windows` in MacOS as an example:\n", "\n", "1. Select `General` option in the left sidebar;\n", "2. You'll see `Port` item on the right board with numbers like `7890`;\n", "3. Click the icon besides the numbers you will see a popup window titled `Copy Command` with content like this in it:\n", "\n", "    ```\n", "    export https_proxy=http://127.0.0.1:%mixedPort%;\n", "    export http_proxy=http://127.0.0.1:%mixedPort%;\n", "    export all_proxy=socks5://127.0.0.1:%mixedPort%\n", "    ```\n", "4. `http://127.0.0.1` is the proxy host address and `7890` is the proxy port.\n", "5. So you can set `http://127.0.0.1:7890` into `.set_settings(\"proxy\", \"<proxy-settings>\")`", "<img width='840' alt='image' src='https://github.com/Maplemx/Agently/assets/4413155/cafa4dad-ea73-4481-9931-30eb35ff1100'>"], "metadata": {"id": "-auG14NWjiaA"}}, {"cell_type": "markdown", "source": ["\n", "---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}