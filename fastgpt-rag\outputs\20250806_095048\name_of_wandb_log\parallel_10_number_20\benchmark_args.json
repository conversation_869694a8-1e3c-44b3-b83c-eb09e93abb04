{"model": "modelscope.cn/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF", "model_id": "Qwen3-30B-A3B-Instruct-2507-GGUF", "attn_implementation": null, "api": "openai", "tokenizer_path": null, "port": 8877, "url": "http://192.168.10.120:11434/v1/chat/completions", "headers": {}, "connect_timeout": 600, "read_timeout": 600, "api_key": null, "no_test_connection": false, "number": 20, "parallel": 10, "rate": -1, "sleep_interval": 5, "log_every_n_query": 10, "debug": false, "wandb_api_key": null, "swanlab_api_key": "TAQWFItPpzVdQpOuXQKCU", "name": "name_of_wandb_log", "outputs_dir": "./outputs\\20250806_095048\\name_of_wandb_log\\parallel_10_number_20", "max_prompt_length": 2048, "min_prompt_length": 10, "prefix_length": 0, "prompt": null, "query_template": null, "apply_chat_template": true, "image_width": 224, "image_height": 224, "image_format": "RGB", "image_num": 1, "dataset": "openqa", "dataset_path": "C:\\Users\\<USER>\\.cache\\modelscope\\hub\\datasets\\AI-ModelScope\\HC3-Chinese\\open_qa.jsonl", "frequency_penalty": null, "repetition_penalty": null, "logprobs": null, "max_tokens": 2048, "min_tokens": 50, "n_choices": null, "seed": 0, "stop": null, "stop_token_ids": null, "stream": true, "temperature": 0.0, "top_p": null, "top_k": null, "extra_args": {"ignore_eos": true}}