#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试LLM配置是否正确
"""

from crewai import LLM

def test_llm():
    try:
        # 创建LLM实例
        llm = LLM(
            model="ep-20250122141838-8gz65",
            temperature=0.7,
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key="5dd64d02-d0b4-48fd-91f0-d4a91f359456"
        )
        
        print("LLM实例创建成功")
        print(f"LLM配置信息: model={llm.model}, base_url={llm.base_url}")
        
        # 尝试使用requests直接测试API
        import requests
        
        url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        headers = {
            "Authorization": "Bearer 5dd64d02-d0b4-48fd-91f0-d4a91f359456",
            "Content-Type": "application/json"
        }
        data = {
            "model": "ep-20250122141838-8gz65",
            "messages": [{"role": "user", "content": "你好，请简单回复一下"}],
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        print("正在直接测试API...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("API测试成功！")
        else:
            print(f"API测试失败，状态码: {response.status_code}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_llm()
