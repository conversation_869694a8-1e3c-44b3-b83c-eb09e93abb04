[plugins]
module_name = agent_component
exception_files = __init__.py
exception_dirs = utils

[default settings]
component_toggles.Role = True
component_toggles.Status = True
component_toggles.OpenAIAssistant = True
component_toggles.Segment = True
component_toggles.Search = False
plugin_settings.agent_component.Session = { "auto_save": true, "max_length": 12000, "strict_orders": true, "manual_history": false }

[orders]
prefix = EventListener, Status, ..., Search, Tool, Segment
suffix = ReplyReformer, ..., Session, EventListener