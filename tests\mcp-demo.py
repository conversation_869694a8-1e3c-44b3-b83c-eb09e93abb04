import Agently
agent = (
    Agently.create_agent()
        .set_settings("current_model", "OAIClient")
        .set_settings("model.OAIClient.auth", {"api_key": "d210f6a1-7eff-4dd2-8778-ff3db4f8c54d"})
        .set_settings("model.OAIClient.url", "https://ark.cn-beijing.volces.com/api/v3")
        .set_settings("model.OAIClient.options", {"model": "deepseek-v3-250324"})
)

from datetime import datetime
import pytz
 
@agent.tool(tool_name="get_now")
def get_current_datetime_decorated(
    timezone: (
        "str",
        "[*Required] Timezone string used in pytz.timezone() in Python"
    )
):
    """get current data and time"""
    tz = pytz.timezone(timezone)
    return datetime.now().astimezone(tz)
 
 
print(agent.input("我在济宁，现在几点了？").start())
 