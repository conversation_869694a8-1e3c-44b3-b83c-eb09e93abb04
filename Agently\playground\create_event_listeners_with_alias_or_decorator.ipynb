{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/create_event_listeners_with_alias_or_decorator.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Create Event Listener Functions with Alias or Decorator using GLM 4"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** English\n", "\n", "**Agent Components:** Decorator\n", "\n", "**Description:**\n", "\n", "Streaming response is a powerful feature that allow users receiving model response faster. In fact, Agently framework provide streaming response by default. How to fetch the streaming response in realtime? Event listener function is what you need.\n", "\n", "In this case, we'll show you how to create event listener function to handle different event from the response of agent request in two different ways: alias and decorator.\n", "\n", "Also, as you know, using the latest public language model from ZhipuAI: GLM-4!\n", "\n", "流式输出是一个非常有用的功能，它能够让用户更快看到模型请求的输出反馈。事实上，Agently默认就提供流式输出反馈。那么，开发者应该怎么接收和处理流式输出的数据呢？事件监听器（Event Listener）函数是你需要考虑的方案。\n", "\n", "在本次案例中，我们将为您展现两种创建事件监听器的方法：agent快捷方式（agent alias）和函数装饰器（decorator）。\n", "\n", "同时，如您所知，我们会使用智谱最新发布的GLM-4在线服务，让您了解如何简单配置即可使用新的智谱GLM-4模型。"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Step 1: Install Packages"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install -U Agently"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Demo Code"], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "markdown", "source": ["### Model Settings and Agent Prepare"], "metadata": {"id": "PGmP4UB4OE4e"}}, {"cell_type": "code", "source": ["import Agently\n", "\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"current_model\", \"ZhipuAI\")\n", "        .set_settings(\"model.ZhipuAI.auth\", { \"api_key\": \"\" })\n", "        # glm-4 model is used by default, if you want to use glm-3-turbo, use settings down below\n", "        #.set_settings(\"model.ZhipuAI.options\", { \"model\": \"glm-3-turbo\" })\n", ")\n", "\n", "agent = agent_factory.create_agent()"], "metadata": {"id": "EOQiS_0FOLnz"}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": ["### DEMO 1: Event Listener <PERSON><PERSON>\n"], "metadata": {"id": "hNvZ9XWIZrpX"}}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "659c48ab-750f-471a-b0fd-dbbffd07fe83"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[*] The first sentence begins with a star.\n", "[*] The second sentence, too, commences with an asterisk.\n", "[*] Lastly, the third sentence follows suit, starting with a star.\n", "\n", "---------------\n", "\n", "[Done]:\n", "[*] The first sentence be...\n", "\n", "\n", "---------------\n", "\n", "[Done Origin]:\n", "{'content': '[*] The first sentence begins with a star.\\n[*] The second sentence, too, commences with an asterisk.\\n[*] Lastly, the third sentence follows suit, starting with a star.', 'meta': {'id': '8308766035504850482', 'choices': [Choice(delta=ChoiceDelta(content='', role='assistant', tool_calls=None), finish_reason='stop', index=0)], 'created': 1705509898, 'model': 'glm-4', 'usage': CompletionUsage(prompt_tokens=15, completion_tokens=42, total_tokens=57)}}\n"]}], "source": ["def on_done(data: str):\n", "    print(f\"\\n\\n---------------\\n\\n[Done]:\\n{ data[:25] }...\")\n", "\n", "def on_done_origin(data: str):\n", "    print(f\"\\n\\n---------------\\n\\n[Done Origin]:\\n{ str(data) }\")\n", "\n", "(\n", "    agent\n", "        .input(\"generate 3 sentence in a list start with '*'\")\n", "        # You can use .on_delta() to add handler for streaming data chunk\n", "        # You can use lambda function as handler\n", "        .on_delta(lambda data: print(data, end=\"\"))\n", "        # You can use .on_done() to add handler to handle full generation result\n", "        # You can use a pre defined function as handler too\n", "        .on_done(on_done)\n", "        # You can also use .add_event_listener() to add handler for other event\n", "        # Event list:\n", "        # - response:delta -> chunk string\n", "        # - response:delta_origin -> full messages of streaming response\n", "        # - response:done -> content string\n", "        # - response:done_origin -> full messages of done response and full content\n", "        # - response:finally -> dict contains request info and response info\n", "        .add_event_listener(\"response:done_origin\", on_done_origin)\n", "        .start()\n", ")\n", "pass"]}, {"cell_type": "markdown", "source": ["### DEMO 2: @agent.on_event() Decorator"], "metadata": {"id": "9LNA6EF0aeid"}}, {"cell_type": "code", "source": ["# Handler function for specific event can be decorated\n", "# by @agent.on_event(<event_name>)\n", "# You don't have to add \"response:\" as prefix to event,\n", "# just input the event name part is good to go\n", "@agent.on_event(\"delta\")\n", "def on_delta(data: str):\n", "    print(data, end=\"\")\n", "\n", "@agent.on_event(\"done\")\n", "def on_done(data: str):\n", "    print(f\"\\n\\n---------------\\n\\n[Done]:\\n{ data[:25] }...\")\n", "\n", "@agent.on_event(\"done_origin\")\n", "def on_done_origin(data: str):\n", "    print(f\"\\n\\n---------------\\n\\n[Done Origin]:\\n{ str(data) }\")\n", "\n", "# ⬆️ These decorated functions will work as same effect as DEMO 1\n", "(\n", "    agent\n", "        .input(\"generate 3 sentence in a list start with '-'\")\n", "        .start()\n", ")\n", "pass"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2i3Lf0yMD9ag", "outputId": "e2113576-df09-4365-af67-0c1be9f49390"}, "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["- The sun sets over the horizon, painting the sky in hues of orange and purple.\n", "- A gentle breeze rustles the leaves of the trees, creating a soothing melody.\n", "- The crashing waves of the ocean provide a rhythmic backdrop to the serene beach.\n", "\n", "---------------\n", "\n", "[Done]:\n", "- The sun sets over the h...\n", "\n", "\n", "---------------\n", "\n", "[Done Origin]:\n", "{'content': '- The sun sets over the horizon, painting the sky in hues of orange and purple.\\n- A gentle breeze rustles the leaves of the trees, creating a soothing melody.\\n- The crashing waves of the ocean provide a rhythmic backdrop to the serene beach.', 'meta': {'id': '8308765794986650551', 'choices': [Choice(delta=ChoiceDelta(content='', role='assistant', tool_calls=None), finish_reason='stop', index=0)], 'created': 1705509876, 'model': 'glm-4', 'usage': CompletionUsage(prompt_tokens=15, completion_tokens=54, total_tokens=69)}}\n"]}]}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}