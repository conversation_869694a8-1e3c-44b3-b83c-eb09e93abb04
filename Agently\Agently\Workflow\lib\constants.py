# 入口类型
EXECUTOR_TYPE_START = 'Start'
# 结束类型
EXECUTOR_TYPE_END = 'End'

# 条件类型
EXECUTOR_TYPE_CONDITION = 'Condition'

# 内置声明好的执行器
BUILT_IN_EXECUTOR_TYPES = [
  EXECUTOR_TYPE_START,
  EXECUTOR_TYPE_END,
  EXECUTOR_TYPE_CONDITION
]
# 循环类型
EXECUTOR_TYPE_LOOP = 'Loop'

# 常规类型
EXECUTOR_TYPE_NORMAL = 'Normal'

SYS_SYMBOL = 'WORKFLOW_SYS_DATA'

# workflow 初始数据的句柄名
WORKFLOW_START_DATA_HANDLE_NAME = f'{SYS_SYMBOL}:{EXECUTOR_TYPE_START}'
WORKFLOW_END_DATA_HANDLE_NAME = f'{SYS_SYMBOL}:{EXECUTOR_TYPE_END}'

# 默认的输入 handle
DEFAULT_INPUT_HANDLE_VALUE = 'default'
DEFAULT_INPUT_HANDLE = {'handle': DEFAULT_INPUT_HANDLE_VALUE}
# 默认的输出 handle，表示数据输出的总出口（会输出所有数据）
DEFAULT_OUTPUT_HANDLE_VALUE = '*'
DEFAULT_OUTPUT_HANDLE = {'handle': DEFAULT_OUTPUT_HANDLE_VALUE}

# 默认存储的 checkpoint 名
DEFAULT_CHECKPOINT_NAME = 'default'
