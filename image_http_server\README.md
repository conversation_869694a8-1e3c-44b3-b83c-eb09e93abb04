# 图片HTTP服务器

一个基于FastAPI的高性能图片HTTP服务器，用于将固定文件夹中的图片通过HTTP接口提供访问。

## 功能特性

- 🚀 **高性能**: 基于FastAPI和Uvicorn，支持异步处理
- 📁 **静态文件服务**: 直接通过URL访问图片文件
- 🔗 **RESTful API**: 提供完整的图片管理API
- 📊 **自动文档**: 自动生成API文档（Swagger UI）
- 🛡️ **错误处理**: 完善的错误处理和日志记录
- 🌐 **CORS支持**: 支持跨域访问
- ⚙️ **灵活配置**: 支持环境变量配置

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置服务器

编辑 `config.py` 或设置环境变量：

```bash
# 设置服务器地址和端口
export IMAGE_SERVER_HOST="**************"
export IMAGE_SERVER_PORT="23333"

# 设置图片目录
export IMAGES_DIR="/home/<USER>/llm_project/images"

# 设置日志级别
export LOG_LEVEL="INFO"
```

### 3. 启动服务器

```bash
# 使用启动脚本（推荐）
python start_server.py

# 或直接运行
python image_server.py
```

### 4. 验证服务

```bash
# 测试服务器
python test_server.py

# 或在浏览器中访问
http://**************:23333/docs
```

## API接口

### 基础信息

- **服务地址**: `http://**************:23333`
- **API文档**: `http://**************:23333/docs`
- **健康检查**: `http://**************:23333/health`

### 主要端点

| 方法 | 路径 | 描述 | 示例 |
|------|------|------|------|
| GET | `/` | 获取服务信息 | `/` |
| GET | `/images/{filename}` | 获取图片文件 | `/images/test.jpg` |
| GET | `/api/images/{filename}` | 获取图片信息 | `/api/images/test.jpg` |
| GET | `/api/images` | 列出所有图片 | `/api/images` |
| GET | `/static/{filename}` | 静态文件访问 | `/static/test.jpg` |
| GET | `/health` | 健康检查 | `/health` |

### 使用示例

#### 1. 获取图片文件

```bash
# 直接访问图片（浏览器可直接显示）
curl http://**************:23333/images/02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c

# 在浏览器中访问
http://**************:23333/images/02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c
```

#### 2. 获取图片信息

```bash
curl http://**************:23333/api/images/02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c
```

响应示例：
```json
{
  "success": true,
  "message": "获取图片信息成功",
  "data": {
    "filename": "02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c",
    "size": 1024000,
    "created_time": "2025-08-08T10:30:00",
    "modified_time": "2025-08-08T10:30:00",
    "file_extension": "",
    "exists": true
  }
}
```

#### 3. 列出所有图片

```bash
curl http://**************:23333/api/images
```

响应示例：
```json
{
  "success": true,
  "message": "找到 5 张图片",
  "data": {
    "count": 5,
    "images": [
      "02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c",
      "test_image.png",
      "another_image.jpg"
    ],
    "directory": "/home/<USER>/llm_project/images"
  }
}
```

## 配置说明

### 环境变量

| 变量名 | 默认值 | 描述 |
|--------|--------|------|
| `IMAGE_SERVER_HOST` | `**************` | 服务器监听地址 |
| `IMAGE_SERVER_PORT` | `23333` | 服务器监听端口 |
| `IMAGES_DIR` | `/home/<USER>/llm_project/images` | 图片存储目录 |
| `LOG_LEVEL` | `INFO` | 日志级别 |
| `LOG_FILE` | `image_server.log` | 日志文件路径 |
| `CORS_ORIGINS` | `*` | 允许的跨域来源 |
| `ENVIRONMENT` | `development` | 运行环境 |

### 支持的图片格式

- JPEG (`.jpg`, `.jpeg`)
- PNG (`.png`)
- GIF (`.gif`)
- BMP (`.bmp`)
- WebP (`.webp`)
- TIFF (`.tiff`)
- SVG (`.svg`)
- 无扩展名文件（如您的哈希格式文件）

## 部署建议

### 开发环境

```bash
# 使用开发配置
export ENVIRONMENT=development
python start_server.py
```

### 生产环境

```bash
# 使用生产配置
export ENVIRONMENT=production
export LOG_LEVEL=WARNING

# 使用更多worker进程
uvicorn image_server:app --host ************** --port 23333 --workers 4
```

### 使用systemd服务

创建 `/etc/systemd/system/image-server.service`:

```ini
[Unit]
Description=Image HTTP Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/project
Environment=ENVIRONMENT=production
ExecStart=/usr/bin/python3 start_server.py
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl enable image-server
sudo systemctl start image-server
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep 23333
   
   # 修改端口
   export IMAGE_SERVER_PORT=8080
   ```

2. **图片目录不存在**
   - 服务器会自动创建 `./test_images` 目录作为备用
   - 检查目录权限和路径是否正确

3. **无法访问图片**
   - 检查文件权限
   - 确认文件确实存在于指定目录
   - 查看服务器日志

### 日志查看

```bash
# 查看实时日志
tail -f image_server.log

# 查看错误日志
grep ERROR image_server.log
```

## 开发

### 项目结构

```
.
├── image_server.py      # 主服务器文件
├── config.py           # 配置管理
├── start_server.py     # 启动脚本
├── test_server.py      # 测试脚本
├── requirements.txt    # 依赖包
├── README.md          # 说明文档
└── test_images/       # 测试图片目录
```

### 添加新功能

1. 在 `image_server.py` 中添加新的路由
2. 更新 `config.py` 中的配置选项
3. 在 `test_server.py` 中添加相应测试
4. 更新文档

## 许可证

MIT License
