import psycopg2

# Database connection parameters
DB_CONFIG = {
    'dbname': 'postgres',
    'user': 'postgrese9',
    'password': '123.Root',
    'host': '***********',
    'port': '5432'
}

def batch_update_frame_paths():
    """Batch update frame_path column and camera_id without confirmation"""
    
    old_prefix = '/mnt/datasets/'
    new_prefix = '/home/<USER>/llm_project/datansha/save_data/datasets/'
    
    # Camera ID mappings
    camera_id_mappings = {
        '4048': '4088',
        '4049': '4090'
    }
    
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        total_updated = 0
        
        # Update frame paths
        path_update_query = """
        UPDATE frame_analysis 
        SET frame_path = REPLACE(frame_path, %s, %s)
        WHERE frame_path LIKE %s
        """
        cursor.execute(path_update_query, (old_prefix, new_prefix, old_prefix + '%'))
        path_updated = cursor.rowcount
        total_updated += path_updated
        print(f"路径更新: {path_updated} 条记录")
        
        # Update camera IDs
        for old_id, new_id in camera_id_mappings.items():
            camera_update_query = """
            UPDATE frame_analysis 
            SET camera_id = %s
            WHERE camera_id = %s
            """
            cursor.execute(camera_update_query, (new_id, old_id))
            camera_updated = cursor.rowcount
            total_updated += camera_updated
            print(f"Camera ID {old_id} -> {new_id}: {camera_updated} 条记录")
        
        conn.commit()
        print(f"总共更新 {total_updated} 条记录")
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    batch_update_frame_paths()