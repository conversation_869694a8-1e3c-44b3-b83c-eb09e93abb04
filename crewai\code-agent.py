from crewai import Agent, Task, Crew
from crewai import LLM

llm = LLM(
    model="openai/ep-20250122141838-8gz65",
    api_base="https://ark.cn-beijing.volces.com/api/v3",
    api_key="5dd64d02-d0b4-48fd-91f0-d4a91f359456",
    temperature=0.7
)
# Create an agent with code execution enabled
coding_agent = Agent(
    llm=llm,
    role="Python Data Analyst",
    goal="Analyze data and provide insights using Python",
    backstory="You are an experienced data analyst with strong Python skills.",
    allow_code_execution=True
)

# Create a task that requires code execution
data_analysis_task = Task(
    description="Analyze the given dataset and calculate the average age of participants.",
    expected_output="A detailed analysis report with the calculated average age and any relevant insights from the data.",
    agent=coding_agent
)

# Create a crew and add the task
analysis_crew = Crew(
    agents=[coding_agent],
    tasks=[data_analysis_task]
)

# Execute the crew
result = analysis_crew.kickoff()

print(result)