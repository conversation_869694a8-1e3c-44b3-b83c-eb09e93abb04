{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/contrast_between_Agently_workflow_and_LangGraph.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# The Contrast between Agently Workflow and LangGraph"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["**Author:** Agently Team"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Intro 简介\n", "\n", "This document is a not-complete-compared introduction between Agently Workflow and LangGraph. We wrote this document due to many community developers wondering the differences between two framework modules. But because of the lack of time, we may have some lack of information or mistakes. If you discover those, please [report issues here](https://github.com/Maplemx/Agently/issues/new) or contact us, we would like to change this document after comfirming the mistakes to make this document objective.\n", "\n", "本文档是Agently Workflow和LangGraph的不全面比较文档。因为很多社区开发者想要了解这两个框架模块之间的差异，我们撰写了这篇文档。但由于准备时间以及对LangGraph框架了解的深度限制，我们也可能在对比时有所纰漏和错误。如果您发现了这些问题，可以[点击这里向我们提交](https://github.com/Maplemx/Agently/issues/new)，或是用其他方式与我们去得联系。我们在确认问题后，会及时修改文档内容，来确保文档的客观性。"], "metadata": {"id": "usZNKT8g4QXa"}}, {"cell_type": "markdown", "source": ["## Compare Table 功能对比表\n", "\n", "|Feature|Agently Workflow|LangGraph|\n", "|---|---|---|\n", "|**Create Workflow Instance**<br /><br />**创建工作流对象**|✅<br />Created by `Agently.Workflow()`<br /><br />由`Agently.Workflow()`类创建|✅<br />Created by `langgraph.graph.StateGraph()`<br />Customized structure class `State` is required for workflow data communication.<br /><br />通过`langgraph.graph.StateGraph()`类创建<br />需要传入自定义结构类`State`作为补充，用于后续工作流中的数据传递结构|\n", "|**Define Workflow Chunk/Node**<br /><br />**定义工作流块/节点**|✅ 🚀<br />Defined by function decorator `@workflow.chunk()`<br />Defined chunks will be found in dict `workflow.chunks`<br />Default named by function name.<br /><br />由装饰器`@workflow.chunk()`装饰执行函数完成定义<br />定义完成的工作块将可以在字典`workflow.chunks`中找到<br />工作块默认命名与执行函数命名一致|✅<br />Define node function directly<br />Register as a node by `workflow.add_node(\"node name\", node_func)`<br /><br />直接定义函数<br />通过`workflow.add_node(\"node name\", node_func)`方法注册成为一个node节点|\n", "|**Executor Function Compatibility**<br />**工作流块/节点运行函数兼容性**|🟠<br />Any Modules<br />Support async `workflow.start_async()`<br />Support streaming handler using listener<br />Not support streaming output using generator<br /><br />支持在运行函数内使用任何其他模块<br />支持异步`workflow.start_async()`<br />支持使用监听器处理流式输出<br />不支持使用generator进行流式输出|✅<br />Any Modules<br />Support async `app.ainvoke()`<br />Support streaming output using generator `app.astream()`<br /><br />支持在运行函数内使用任何其他模块<br />支持异步 `app.ainvoke()`<br />支持使用generator进行流式输出`workflow.astream()`|\n", "|**Start and End Points**<br /><br />**开始和结束**|✅ 🚀<br />Provide standard chunk type `Start` and `END`<br />Those two standard chunks will be put into `workflow.chunks` by default when workflow is created<br /><br />提供标准的chunk_type，`Start`和`End`<br />两个名为\"start\"和\"end\"的标准工作块在工作流创建时会默认内置到工作块池中|✅<br />Using `workflow.set_entry_ponit(\"node name\", node_func)` to set the node to start<br />Using `langgraph.graph.END` as the ending node<br /><br />通过`workflow.set_entry_ponit(\"node name\", node_func)`的方式确定起点块<br />使用内置的langgraph.graph.END作为终点块|\n", "|**Normal Connection**<br /><br />**普通连接**|✅ 🚀<br />Connect two chunks using`workflow.chunks[\"from_chunk_name\"].connect_to(workflow.chunks[\"to_chunk_name\"])`<br />Simplify expression: `.connect_to(\"to_chunk_name\")`<br />Chain expression like `.connect_to(\"chunk_a\").connect_to(\"chunk_b\")` supported.<br /><br />使用`workflow.chunks[\"from_chunk_name\"].connect_to(workflow.chunks[\"to_chunk_name\"])`连接<br />支持使用`.connect_to(\"to_chunk_name\")`方式简写<br />支持`.connect_to(\"chunk_a\").connect_to(\"chunk_b\")`这样的链式表达|✅<br />Connect two nodes using `workflow.add_edge(\"from_node_name\", \"to_node_name\")`<br /><br />使用`workflow.add_edge(\"from_node_name\", \"to_node_name\")`连接两个node节点|\n", "|**Conditional Connection**<br /><br />**条件连接**|🟠<br />Using expression like `.if_condition(lambda value, storage: value==1)` in chain expression<br />Get return value from last chunk as `value` and get workflow storage as `storage`<br />Support `.if_condition()`, `.else_condition()` currently<br />Can not support more than 2 conditions branches right now or use more \"if-else\" combination to express but we're working on it<br /><br />使用类似`.if_condition(lambda value, storage: value==1)`的形式进行条件判断表达<br />从上一个连接块获取函数return的返回值作为`value`，使用工作流全局存储作为`storage`<br />目前只能支持不超过两种情况的分支，或是使用多个if-else组合来进行表达，我们正在改进这一点|✅ 🚀<br />Using `workflow.add_conditional_edges(\"from_node_name\", condition_func, { \"return_value_1\": \"to_node_name_1\", ... })` to define conditional edges<br />Support return more than 2 conditions to branch<br /><br />使用`workflow.add_conditional_edges(\"from_node_name\", condition_func, { \"return_value_1\": \"to_node_name_1\", ... })`进行表达<br />支持在一个条件判断函数返回2个以上的条件结果，并进行分支规划|\n", "|**Ring Connection**<br />**成环支持**|✅<br />Yes, you can connect chunks into a ring to repeatly processing<br />You can use `if_condition()` to break the ring<br /><br />支持，你可以将多个工作块首尾相连形成环状来多次执行<br />你可以使用`if_condition()`进行条件判断来跳出环|✅<br />Yes, you can connect nodes into a ring to repeatly processing<br />You can use `workflow.add_conditional_edges()` to break the ring<br /><br />支持，你可以将多个工作节点首尾相连形成环状来多次执行<br />你可以使用`workflow.add_conditional_edges()`进行条件判断来跳出环|\n", "|**Parallel Branch**<br />**并行分支**|✅<br />You can connect one chunk to two or more different chunks and connect all these chunks to different handles of the end chunk to make a parallel branch<br /><br />你可以通过将一个工作块连接多个下游块，并将这些下游块连接到终点块的不同端点上（不同端点才会等待上游全部任务的完成）来构造并行分支|❌<br />Can not support, if you try to connect one node to two or more different nodes you will receive an error<br /><br />不支持，如果你尝试将一个node下游同时连接多个node，你会收到一个错误警告|\n", "|**Loop by List**<br />**基于列表的循环**|✅<br />Using `.loop_with(sub_workflow)` to pass items in list to sub workflow one by one and get a list return result<br /><br />支持使用`.loop_with(sub_workflow)`将上游块的列表中的元素一个一个地传递给子工作流sub workflow，并从子工作流获得汇集成列表的返回结果|❌<br />Not found in quick start guide book.<br /><br />在快速入门的相关文档中未找到|\n", "|**Start Workflow and Initial Data**<br />**启动工作流及启动时数据传递**|✅<br />Using `result = workflow.start(initial_inputs, storage=initial_storage)` to start workflow and get final result from chunk \"end\"'s return<br />Passing `initial_inputs` to parameter `inputs` of next chunk's executor function<br />Setting `initial_storage` to workflow's global storage<br /><br />使用`result = workflow.start(initial_inputs, storage=initial_storage)`的方式启动工作流<br />将`initial_inputs`通过工作块执行函数的`inputs`参数传给第一个处理工作块<br />将`initial_storage`作为工作流全局数据`storage`的初始数据|✅<br />Using `app = workflow.compile()` to compile workflow first<br />Using `final_state = app.invoke({\"state_key\": state_value}, config={})` to start workflow and get final state as result after workflow processing<br />Passing initial state data in `app.invoke()` to set initial state<br /><br />需要使用`app = workflow.compile()`将工作流事先进行编译<br />通过`final_state = app.invoke({\"state_key\": state_value}, config={})`启动工作流，并将运行后的最终state数据返回<br />在`app.invoke()`方法中传递初始的state数据|\n", "|**Runtime Data Communication**<br />**运行时数据通讯**|✅<br />Using `inputs` to passing data from last chunk to next chunk<br />Using different handles of chunk to manage data passing and running orders<br />Using `stroage` to store workflow global data<br /><br />使用`inputs`在具有直接连接顺序关系的工作块之间进行数据传递<br />（提供强时序相关的数据的可靠传递方式）<br />在一个工作块上使用多个端点（handles）进行数据传递分组及执行顺序控制<br />（如具有两个输入端点的工作块会等待两个端点对应的前置分支流完成处理后才开始工作）<br />使用`storage`进行工作流全局数据传递|🟠<br />Using pre-stated `state` only and you can not customize `state`'s structure in workflow<br />只使用预先定义好结构的`state`进行工作过程数据传递，并且工作流处理过程中不能修改`state`的结构。<br />（无法应对复杂处理过程或是多分支时序依赖较高的场景，因为这时`state`键值的状态不确定性变高）<br />（这可能也是LangGraph没有支持并行分支的原因）|\n", "|**Saving Data and Restore**<br /><br />**工作数据保存和恢复**|✅<br />Using `workflow.storage.get_all()` to get storage data and using `workflow.storage.set_with_dict()` to reset storage data<br /><br />支持使用`workflow.storage.get_all()`取出工作流的全局数据，并用`workflow.storage.set_with_dict()`更新工作流全局数据|✅ 🚀<br />Using `checkpointer = langgraph.checkpoint.MemorySaver()` to persist state between graph runs<br /><br />使用`checkpointer = langgraph.checkpoint.MemorySaver()`在多次工作流运行间保存状态数据|\n", "|**Workflow Graph**<br />**工作流图形生成**|✅<br />Using `workflow.draw()` to get mermaid code<br /><br />支持通过`workflow.draw()`获取工作流的Mermaid代码|✅ 🚀<br />Compile workflow by `app = workflow.compile()` first then use `app.get_graph().draw_mermaid()` to get mermaid code<br />Support ASCII, Mermaid, PNG<br /><br />通过`app = workflow.compile()`对工作流进行编译后，可以通过`app.get_graph().draw_mermaid()`的方式生成图形代码<br />除了Mermaid外，还支持ASCII和PNG格式输出|\n", "|**Framework Ecosystem**<br />**框架生态**|✅<br />Agently Settings to switch between models speedily without changing any logic code<br />Agently Agent Request to make model request easily with solid format control<br />Agently P-YAML to seperate coding logic and model requesting prompt<br />English and Chinese language support<br /><br />Agently Settings帮助开发者不修改任何业务逻辑代码，使用简单配置就能切换驱动模型<br />Agently Agent Request帮助开发者直观轻松地对模型请求进行表达，并获得高稳定性的结构化数据输出<br />Agently P-YAML帮助开发者将模型请求提示优化等工作从业务代码中分离出来<br />提供英文及中文双语支持|✅<br />World famous LangChain framework<br />LangSmith and LangServer to help developers monitor and deploy application<br />English support only<br /><br />LangChain是世界知名框架<br />LangSmith和LangServer能帮助开发者进行应用运行过程监控和服务部署<br />只提供英文支持|\n", "\n", "图例说明：\n", "✅ 完全支持此能力\n", "❌ 不支持此能力或较为复杂\n", "🟠 部分支持此能力，或在支持此能力时有瑕疵，需要改进\n", "🚀 在支持此能力时，提供了便利易用的方法"], "metadata": {"id": "fSd4S4hW7Wlk"}}, {"cell_type": "markdown", "source": ["## Code Comparison 实现代码对比"], "metadata": {"id": "lezyByPlLiDE"}}, {"cell_type": "markdown", "source": ["### Subject Settings 命题设计\n", "\n", "We'll try to cover as many abilities from the table above as possible to present differences between two frameworks.\n", "\n", "为了更好展现不同框架的特点，我们设计的命题将尽可能多地覆盖上表中的相关能力。"], "metadata": {"id": "gnhYCwYilROU"}}, {"cell_type": "markdown", "source": ["<img width=\"1024\" src=\"https://github.com/Maplemx/Agently/assets/4413155/392bb081-99c5-44ba-ab64-c752dc8443ae\" />"], "metadata": {"id": "KAhu4YX7zQ_e"}}, {"cell_type": "markdown", "source": ["**Task Description**\n", "\n", "1. Pass data `times=0` into workflow when workflow start\n", "2. Ask user to input some string\n", "3. Start two parallel branches:\n", "\n", "    - Branch A:\n", "        1. Add `times` by 1\n", "    \n", "    - Branch B:\n", "        1. Split user input string character by character into a list\n", "        2. Loop the character item in the list and print character item one by one (In real task this sub workflow should be much more complex)\n", "        3. Collect character items from sub workflow and regroup them into a string, print it and append it into list `all_user_inputs`.\n", "\n", "4. When two branches all finish their works, make a judgement about value of `times`, if `times>=3` then end this workflow, else go back to step 2.\n", "5. Get `all_user_inputs` after workflow finish and print it.\n", "\n", "**任务描述**\n", "\n", "1. 将数据`times=0`在工作流开始工作时，传递到工作流内部\n", "2. 请用户输入一个若干字符长度的字符串\n", "3. 开启两个平行分支：\n", "\n", "    - 分支A：\n", "        1. 将`times`的值增加1\n", "\n", "    - 分支B：\n", "        1. 将用户输入的字符串拆分成由单个字符组成的list\n", "        2. 循环访问list中的每一个元素，进行相同的处理过程：打印当前元素信息（在真实的任务中，这个子工作流的任务复杂度其实会高很多）\n", "        3. 回收子工作流中的字符元素并将它们重组为一个字符串，然后把字符串打印出来，并把字符串放入list`all_user_inputs`中存储\n", "\n", "4. 当分支A和B都完成后，做一个关于`times`当前值的判断，如果`times>=3`那么工作流结束，否则回到步骤2\n", "5. 在工作流执行完毕后，获取`all_user_inputs`的结果并打印出来"], "metadata": {"id": "htuI0uD1zerh"}}, {"cell_type": "markdown", "source": ["### Install Packages"], "metadata": {"id": "lSW2Nbsl-WWJ"}}, {"cell_type": "code", "source": ["!pip install -q -U langgraph Agently mermaid-python"], "metadata": {"id": "qVq52vPy-Vu7"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["### LangGraph"], "metadata": {"id": "YLb3gQAILqBa"}}, {"cell_type": "code", "source": ["from langgraph.graph import StateGraph, END\n", "\n", "# Define State Class\n", "from typing import TypedDict, Optional\n", "\n", "## I have to go back here to modified this class\n", "## when I write node functions down below time to time\n", "## because I find out that I need to pass more other data in process\n", "class State(TypedDict):\n", "    times: Optional[int] = None\n", "    user_input_data: Optional[str] = None\n", "    splited_list: Optional[list] = None\n", "    all_user_inputs: Optional[list] = None\n", "\n", "# Create Workflow Instance\n", "workflow = StateGraph(State)\n", "\n", "# Define Node Functions\n", "def start(state):\n", "    return\n", "\n", "def user_input(state):\n", "    return { \"user_input_data\": input(\"[Input String]: \") }\n", "\n", "def add_times(state):\n", "    return { \"times\": state.get(\"times\") + 1 }\n", "\n", "def split_input_to_list(state):\n", "    user_input_data = state.get(\"user_input_data\")\n", "    splited_list = []\n", "    for char in user_input_data:\n", "        splited_list.append(char)\n", "    return { \"splited_list\": splited_list }\n", "\n", "## I can't use a loop using LangGraph here so I just combine\n", "## `loop`, `print_char` item by item, `regroup_input` 3 steps\n", "## into this one\n", "def combined_steps(state):\n", "    splited_list = state.get(\"splited_list\")\n", "    regrouped_input = \"\"\n", "    for char in splited_list:\n", "        print(char)\n", "        regrouped_input += char\n", "    print(regrouped_input)\n", "    all_user_inputs = state.get(\"all_user_inputs\") or []\n", "    all_user_inputs.append(regrouped_input)\n", "    return { \"all_user_inputs\": all_user_inputs }\n", "\n", "# Register Node\n", "workflow.add_node(\"start\", start)\n", "workflow.add_node(\"user_input\", user_input)\n", "workflow.add_node(\"split_input_to_list\", split_input_to_list)\n", "workflow.add_node(\"combined_steps\", combined_steps)\n", "workflow.add_node(\"add_times\", add_times)\n", "\n", "# Connection\n", "workflow.set_entry_point(\"start\")\n", "workflow.add_edge(\"start\", \"user_input\")\n", "workflow.add_edge(\"user_input\", \"split_input_to_list\")\n", "## the connections seem simple because I warpped\n", "## the loop sub workflow logic into `combined_steps`\n", "workflow.add_edge(\"split_input_to_list\", \"combined_steps\")\n", "## I can not build parallel branches so I just make them run in orders\n", "## In this case that doesn't matter but maybe not in other cases\n", "workflow.add_edge(\"combined_steps\", \"add_times\")\n", "workflow.add_conditional_edges(\n", "  \"add_times\",\n", "  lambda state: \"end\" if state.get(\"times\") >= 3 else \"continue\",\n", "  {\n", "    \"end\": END,\n", "    \"continue\": \"user_input\"\n", "  }\n", ")\n", "\n", "# Compile\n", "app = workflow.compile()"], "metadata": {"id": "vkf9oNI54Zuv"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Draw Mermaid Workflow Graph\n", "from mermaid import Mermaid\n", "Mermaid(app.get_graph().draw_mermaid())"], "metadata": {"id": "HuUy6ZpO-2ZS", "outputId": "1f2b89e8-82d4-45ea-91fd-c79f037d8442", "colab": {"base_uri": "https://localhost:8080/", "height": 621}}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<mermaid.mermaid.Mermaid at 0x7e37d1265780>"], "text/html": ["\n", "        <div class=\"mermaid-4a66a537-137e-4af1-aaf4-c6c6400e58bd\"></div>\n", "        <script type=\"module\">\n", "            import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10.1.0/+esm'\n", "            const graphDefinition = '%%{init: {\"flowchart\": {\"curve\": \"linear\"}}}%%\\ngraph TD;\\n\t__start__[__start__]:::startclass;\\n\t__end__[__end__]:::endclass;\\n\tstart([start]):::otherclass;\\n\tuser_input([user_input]):::otherclass;\\n\tsplit_input_to_list([split_input_to_list]):::otherclass;\\n\tcombined_steps([combined_steps]):::otherclass;\\n\tadd_times([add_times]):::otherclass;\\n\t__start__ --> start;\\n\tcombined_steps --> add_times;\\n\tsplit_input_to_list --> combined_steps;\\n\tstart --> user_input;\\n\tuser_input --> split_input_to_list;\\n\tadd_times -. end .-> __end__;\\n\tadd_times -. continue .-> user_input;\\n\tclassDef startclass fill:#ffdfba;\\n\tclassDef endclass fill:#baffc9;\\n\tclassDef otherclass fill:#fad7de;\\n';\n", "            const element = document.querySelector('.mermaid-4a66a537-137e-4af1-aaf4-c6c6400e58bd');\n", "            const { svg } = await mermaid.render('graphDiv-4a66a537-137e-4af1-aaf4-c6c6400e58bd', graphDefinition);\n", "            element.innerHTML = svg;\n", "        </script>\n", "        "]}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "code", "source": ["# Start Workflow and Get Result\n", "result = app.invoke({ \"times\": 0 })\n", "print(result[\"all_user_inputs\"])"], "metadata": {"id": "YVAwWYRT_XiE", "outputId": "0a9538eb-53ad-459c-9160-6e5dddd88323", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Input String]: LangGraph\n", "L\n", "a\n", "n\n", "g\n", "G\n", "r\n", "a\n", "p\n", "h\n", "LangGraph\n", "[Input String]: and\n", "a\n", "n\n", "d\n", "and\n", "[Input String]: <PERSON><PERSON>\n", "A\n", "g\n", "e\n", "n\n", "t\n", "l\n", "y\n", "<PERSON><PERSON>\n", "['LangGraph', 'and', '<PERSON><PERSON>']\n"]}]}, {"cell_type": "markdown", "source": ["### Agently Workflow"], "metadata": {"id": "JmBbXla7L5nQ"}}, {"cell_type": "code", "source": ["import Agently\n", "\n", "# Create Workflow Instance\n", "workflow = Agently.Workflow()\n", "sub_workflow = Agently.Workflow()\n", "\n", "# Define Chunks\n", "## (Including define chunk function and register chunk)\n", "@workflow.chunk()\n", "def user_input(inputs, storage):\n", "    return input(\"[Input String]: \")\n", "\n", "@workflow.chunk()\n", "def add_times(inputs, storage):\n", "    storage.set(\"times\", storage.get(\"times\") + 1)\n", "    return\n", "\n", "## Need a wait point chunk to wait two branches to finish\n", "@workflow.chunk()\n", "def wait_point(inputs, storage):\n", "    return\n", "\n", "@workflow.chunk()\n", "def split_input_to_list(inputs, storage):\n", "    user_input_data = inputs[\"default\"]\n", "    splited_list = []\n", "    for char in user_input_data:\n", "        splited_list.append(char)\n", "    return splited_list\n", "\n", "## Use sub_workflow to handle items from `split_input_to_list`\n", "@sub_workflow.chunk()\n", "def print_char(inputs, storage):\n", "    print(inputs[\"default\"])\n", "    return inputs[\"default\"]\n", "\n", "@workflow.chunk()\n", "def append_to_all_user_inputs(inputs, storage):\n", "    all_user_inputs = storage.get(\"all_user_inputs\", [])\n", "    regrouped_input = ''.join(inputs[\"default\"][\"default\"])\n", "    print(regrouped_input)\n", "    all_user_inputs.append(regrouped_input)\n", "    storage.set(\"all_user_inputs\", all_user_inputs)\n", "    return\n", "\n", "# Connection\n", "## Sub Workflow in Loop\n", "sub_workflow.connect_to(\"print_char\").connect_to(\"end\")\n", "\n", "## Branch A\n", "(\n", "    workflow\n", "        .connect_to(\"user_input\")\n", "        .connect_to(\"add_times\")\n", "        ## Use different handle to wait different branch\n", "        .connect_to(\"wait_point.branch_a\")\n", "        .if_condition(lambda value, storage: storage.get(\"times\") >= 3)\n", "            .connect_to(\"end\")\n", "        .else_condition()\n", "            .connect_to(\"user_input\")\n", ")\n", "\n", "## Branch B\n", "(\n", "    ## Only define connections those were not defined yet\n", "    ## So, start from `user_input`\n", "    workflow.chunks[\"user_input\"]\n", "        .connect_to(\"split_input_to_list\")\n", "        ## Use .loop_with() to call sub workflow\n", "        .loop_with(sub_workflow)\n", "        .connect_to(\"append_to_all_user_inputs\")\n", "        ## Use different handle to wait different branch\n", "        .connect_to(\"wait_point.branch_b\")\n", ")"], "metadata": {"id": "AVSFkd3QL456", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "4c26ed3a-889d-437d-bd74-dba2429c1ab3"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<Agently.Workflow.Chunk.SchemaChunk at 0x77fefee41d80>"]}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "code", "source": ["# Draw Mermaid Workflow Graph\n", "## Yep, I can't use <PERSON><PERSON>() to draw it because it's complex\n", "print(workflow.draw())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "foG-awtfMXbf", "outputId": "09d11a18-59ec-45d3-d82f-e442176069ab"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["%%{ init: { 'flowchart': { 'curve': 'linear' }, 'theme': 'neutral' } }%%\n", "%% Rendered By Agently %%\n", "flowchart LR\n", "classDef chunk_style fill:#fbfcdb,stroke:#666,stroke-width:1px,color:#333;\n", "classDef loop_style fill:#f5f7fa,stroke:#666,stroke-width:1px,color:#333,stroke-dasharray: 5 5\n", "    subgraph Loop_1\n", "    direction LR\n", "    eeefd9bb-48f9-4c67-a65d-db73a5fe5a1b(\"start\"):::chunk_style -.-> |\"* -->-- default\"| e1994333-d244-4853-b8ef-cce16183c074(\"print_char\"):::chunk_style\n", "    e1994333-d244-4853-b8ef-cce16183c074(\"print_char\"):::chunk_style -.-> |\"* -->-- default\"| 1298e0ae-080d-43b8-9c17-cb08e6952274(\"end\"):::chunk_style\n", "    end\n", "    886d5aff-c668-4b51-b5e0-a7985b3850f8(\"start\"):::chunk_style -.-> |\"* -->-- default\"| 72a42165-539d-438c-b07a-02c6dda7be64(\"user_input\"):::chunk_style\n", "    72a42165-539d-438c-b07a-02c6dda7be64(\"user_input\"):::chunk_style -.-> |\"* -->-- default\"| 7a03950a-5b10-4667-b5d5-e8af6740bfc6(\"add_times\"):::chunk_style\n", "    7a03950a-5b10-4667-b5d5-e8af6740bfc6(\"add_times\"):::chunk_style -.-> |\"* -->-- branch_a\"| 8d9b7d96-18bd-4bd8-9231-52d3ac712c27(\"wait_point\"):::chunk_style\n", "    8d9b7d96-18bd-4bd8-9231-52d3ac712c27(\"wait_point\"):::chunk_style -.-> |\"* -- ◇ -- default\"| 6f20d15d-1b0d-47f8-8795-0336a836638d(\"end\"):::chunk_style\n", "    8d9b7d96-18bd-4bd8-9231-52d3ac712c27(\"wait_point\"):::chunk_style -.-> |\"* -- ◇ -- default\"| 72a42165-539d-438c-b07a-02c6dda7be64(\"user_input\"):::chunk_style\n", "    72a42165-539d-438c-b07a-02c6dda7be64(\"user_input\"):::chunk_style -.-> |\"* -->-- default\"| 2a1d5dc2-47b2-4764-9953-b400d39a6d37(\"split_input_to_list\"):::chunk_style\n", "    2a1d5dc2-47b2-4764-9953-b400d39a6d37(\"split_input_to_list\"):::chunk_style -.-> |\"* -->-- default\"| Loop_1:::loop_style\n", "    Loop_1:::loop_style -.-> |\"* -->-- default\"| d7886aa5-4865-4560-88fc-c8552a2f61ea(\"regroup_input\"):::chunk_style\n", "    d7886aa5-4865-4560-88fc-c8552a2f61ea(\"regroup_input\"):::chunk_style -.-> |\"* -->-- default\"| a8958c5a-43b7-448f-8d69-2a9816a3a33c(\"append_to_all_user_inputs\"):::chunk_style\n", "    a8958c5a-43b7-448f-8d69-2a9816a3a33c(\"append_to_all_user_inputs\"):::chunk_style -.-> |\"* -->-- branch_b\"| 8d9b7d96-18bd-4bd8-9231-52d3ac712c27(\"wait_point\"):::chunk_style\n"]}]}, {"cell_type": "markdown", "source": ["<image width=\"1024\" src=\"https://github.com/Maplemx/Agently/assets/4413155/91ddf134-31d5-4afe-9142-6e94e59d022f\" />"], "metadata": {"id": "8zTF8hDZNhpN"}}, {"cell_type": "code", "source": ["# Start Workflow and Get Result\n", "workflow.start(storage = { \"times\": 0 })\n", "print(workflow.storage.get(\"all_user_inputs\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dSvgF2wROB8L", "outputId": "41c0ad26-7215-4151-ec64-c7b024586925"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Input String]: LangGraph\n", "L\n", "a\n", "n\n", "g\n", "G\n", "r\n", "a\n", "p\n", "h\n", "LangGraph\n", "[Input String]: and\n", "a\n", "n\n", "d\n", "and\n", "[Input String]: <PERSON><PERSON>\n", "A\n", "g\n", "e\n", "n\n", "t\n", "l\n", "y\n", "<PERSON><PERSON>\n", "['LangGraph', 'and', '<PERSON><PERSON>']\n"]}]}, {"cell_type": "markdown", "source": ["### Code Comparison by Sections 实现代码分项对比"], "metadata": {"id": "EdmFUrugnsdF"}}, {"cell_type": "markdown", "source": ["#### Import and Preparation 包引入及创建准备"], "metadata": {"id": "VzRXEXZEo3Cz"}}, {"cell_type": "code", "source": ["# LangGraph\n", "## Step 1. import\n", "from langgraph.graph import StateGraph, END\n", "\n", "## Step 2. Define State Class (If using StateGraph)\n", "from typing import TypedDict, Optional\n", "class State(TypedDict):\n", "    times: Optional[int] = None\n", "    user_input_data: Optional[str] = None\n", "    splited_list: Optional[list] = None\n", "    all_user_inputs: Optional[list] = None\n", "\n", "## Step 3. Create Workflow Instance\n", "workflow = StateGraph(State)"], "metadata": {"id": "nmyPs1x_pRHJ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Agently Workflow\n", "## Step 1. import\n", "import Agently\n", "\n", "## Step 2. Create Workflow Instance\n", "workflow = Agently.Workflow()\n", "sub_workflow = Agently.Workflow()\n", "\n", "## You don't have to pre-define workflow data structure\n", "## Workflow global storage is a flexible k-v store"], "metadata": {"id": "4JuVYpJjp3F8"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["#### Chunk / Node Definition and Register 工作块/节点定义及注册"], "metadata": {"id": "BTCm5403qe7g"}}, {"cell_type": "code", "source": ["# LangGraph\n", "## Step 1. Define Node Function\n", "def split_input_to_list(state):\n", "    user_input_data = state.get(\"user_input_data\")\n", "    splited_list = []\n", "    for char in user_input_data:\n", "        splited_list.append(char)\n", "    return { \"splited_list\": splited_list }\n", "\n", "## Step 2. Register Node Function\n", "workflow.add_node(\"split_input_to_list\", split_input_to_list)"], "metadata": {"id": "gIcCIO2wqv34"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Agently\n", "## Step 1. Use Function Decorator to Define and Register Chunk Function\n", "@workflow.chunk()\n", "def split_input_to_list(inputs, storage):\n", "    user_input_data = inputs[\"default\"]\n", "    splited_list = []\n", "    for char in user_input_data:\n", "        splited_list.append(char)\n", "    return splited_list\n", "## Then you can use this chunk as `workflow.chunks[\"split_input_to_list\"]`"], "metadata": {"id": "c1oc3iSPrFXW"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["#### Connection 连接"], "metadata": {"id": "18DiSrGfrVGW"}}, {"cell_type": "code", "source": ["# LangGraph\n", "## Set Entry Point\n", "workflow.set_entry_point(\"start\")\n", "\n", "## Normal Connection\n", "workflow.add_edge(\"start\", \"user_input\")\n", "workflow.add_edge(\"user_input\", \"split_input_to_list\")\n", "workflow.add_edge(\"split_input_to_list\", \"combined_steps\")\n", "workflow.add_edge(\"combined_steps\", \"add_times\")\n", "\n", "## Condition\n", "workflow.add_conditional_edges(\n", "  \"add_times\",\n", "  lambda state: \"end\" if state.get(\"times\") >= 3 else \"continue\",\n", "  {\n", "    \"end\": END,\n", "    \"continue\": \"user_input\" # <- ## Ring Support\n", "  }\n", ")\n", "\n", "## No Parallel Branches\n", "## No Loop with Sub Workflow"], "metadata": {"id": "gXylpxtvrTf7"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Agently\n", "## Hidden Entry Point\n", "## `workflow.connect_to()` == `workflow.chunks[\"start\"].connect_to()`\n", "\n", "## Normal Connection\n", "workflow.connect_to(\"user_input\")\n", "\n", "## Chain Expression & Parallel Branches\n", "### Branch A\n", "(\n", "    workflow.chunks[\"user_input\"]\n", "        .connect_to(\"add_times\")\n", "        .connect_to(\"wait_point.branch_a\")\n", ")\n", "### Branch B\n", "(\n", "    workflow.chunks[\"user_input\"]\n", "        .connect_to(\"split_input_to_list\")\n", "        .loop_with(sub_workflow)\n", "        .connect_to(\"regroup_input\")\n", "        .connect_to(\"append_to_all_user_inputs\")\n", "        .connect_to(\"wait_point.branch_b\")\n", ")\n", "\n", "## Condition\n", "(\n", "    workflow.chunks[\"wait_point\"]\n", "        .if_condition(lambda value, storage: storage.get(\"times\") >= 3)\n", "            .connect_to(\"end\")\n", "        .else_condition()\n", "            .connect_to(\"user_input\") # <- ## Ring Support\n", ")\n", "\n", "## Loop with Sub Workflow\n", "sub_workflow.connect_to(\"print_char\").connect_to(\"end\")\n", "\"\"\"\n", "(\n", "    workflow.chunks[\"user_input\"]\n", "        .connect_to(\"split_input_to_list\")\n", "        # Receive list and pass item in list one by one to sub workflow\n", "        .loop_with(sub_workflow)\n", "        # Collect sub workflow results into a list and pass to next chunk\n", "        .connect_to(\"regroup_input\")\n", ")\n", "\"\"\""], "metadata": {"id": "DjprHX2W1B1B"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["### Start & Result 启动及运行结果"], "metadata": {"id": "uaKuUI9M4C_d"}}, {"cell_type": "code", "source": ["# LangGraph\n", "## Step 1. <PERSON><PERSON><PERSON>\n", "app = workflow.compile()\n", "\n", "## Step 2. Start\n", "result = app.invoke({ \"times\": 0 }) # <- Passing State Data as Pre-Defined\n", "\n", "## Step 3. <PERSON><PERSON><PERSON>\n", "print(result[\"all_user_inputs\"])\n", "### Result will get full state data after compiled workflow processed"], "metadata": {"id": "oOFwTN0C4Puv"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Agently\n", "## Step 1. Start\n", "result = workflow.start(storage = { \"time\": 0 }) # <- Passing Initial Data in Type `dict`\n", "### workflow.start(initial_inputs<any, [Optional]>, *, storage=initial_storage<dict, [Optional]>)\n", "\n", "## Step 2. <PERSON><PERSON><PERSON>\n", "print(result)\n", "### Result will get data from inputs in chunk \"end\"\n", "print(workflow.storage.get_all())\n", "### You can also get storage data from workflow before workflow's next `.start()`"], "metadata": {"id": "8zP3viG05XSQ"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}