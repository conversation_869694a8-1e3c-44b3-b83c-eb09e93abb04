2025-08-06 09:43:16,705 - evalscope - INFO - Starting benchmark with args: 
2025-08-06 09:43:16,705 - evalscope - INFO - {
    "model": "modelscope.cn/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF",
    "model_id": "Qwen3-30B-A3B-Instruct-2507-GGUF",
    "attn_implementation": null,
    "api": "openai",
    "tokenizer_path": null,
    "port": 8877,
    "url": "http://192.168.10.120:11434/v1/chat/completions",
    "headers": {},
    "connect_timeout": 600,
    "read_timeout": 600,
    "api_key": null,
    "no_test_connection": false,
    "number": 10,
    "parallel": 1,
    "rate": -1,
    "sleep_interval": 5,
    "log_every_n_query": 10,
    "debug": false,
    "wandb_api_key": null,
    "swanlab_api_key": "TAQWFItPpzVdQpOuXQKCU",
    "name": "name_of_wandb_log",
    "outputs_dir": "./outputs\\20250806_094312\\name_of_wandb_log\\parallel_1_number_10",
    "max_prompt_length": 1024,
    "min_prompt_length": 1024,
    "prefix_length": 0,
    "prompt": null,
    "query_template": null,
    "apply_chat_template": true,
    "image_width": 224,
    "image_height": 224,
    "image_format": "RGB",
    "image_num": 1,
    "dataset": "openqa",
    "dataset_path": null,
    "frequency_penalty": null,
    "repetition_penalty": null,
    "logprobs": null,
    "max_tokens": 1024,
    "min_tokens": 1024,
    "n_choices": null,
    "seed": 0,
    "stop": null,
    "stop_token_ids": null,
    "stream": true,
    "temperature": 0.0,
    "top_p": null,
    "top_k": null,
    "extra_args": {
        "ignore_eos": true
    }
}
2025-08-06 09:43:16,830 - evalscope - INFO - Test connection successful.
2025-08-06 09:43:21,123 - evalscope - ERROR - Exception in async function 'benchmark': Dataset is empty!
Traceback (most recent call last):
  File "D:\miniconda_install\envs\pytorch_is\lib\site-packages\evalscope\perf\utils\handler.py", line 17, in async_wrapper
    return await func(*args, **kwargs)
  File "D:\miniconda_install\envs\pytorch_is\lib\site-packages\evalscope\perf\benchmark.py", line 196, in benchmark
    async for request in get_requests(args, api_plugin):
  File "D:\miniconda_install\envs\pytorch_is\lib\site-packages\evalscope\perf\benchmark.py", line 70, in get_requests
    async for request in generator:
  File "D:\miniconda_install\envs\pytorch_is\lib\site-packages\evalscope\perf\benchmark.py", line 49, in generate_requests_from_dataset
    raise Exception('Dataset is empty!')
Exception: Dataset is empty!
