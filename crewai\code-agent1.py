from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import CodeInterpreterTool

llm = LLM(
    model="openai/ep-20250122141838-8gz65",
    api_base="https://ark.cn-beijing.volces.com/api/v3",
    api_key="5dd64d02-d0b4-48fd-91f0-d4a91f359456",
    temperature=0.7
)
# Initialize the tool
code_interpreter = CodeInterpreterTool()

# Define an agent that uses the tool
programmer_agent = Agent(
    llm=llm,
    role="Python Programmer",
    goal="Write and execute Python code to solve problems",
    backstory="An expert Python programmer who can write efficient code to solve complex problems.",
    tools=[code_interpreter],
    verbose=True,
)

# Example task to generate and execute code
coding_task = Task(
    description="Write a Python function to calculate the Fibonacci sequence up to the 10th number and print the result.",
    expected_output="The Fibonacci sequence up to the 10th number.",
    agent=programmer_agent,
)

# Create and run the crew
crew = Crew(
    agents=[programmer_agent],
    tasks=[coding_task],
    verbose=True,
    process=Process.sequential,
)
result = crew.kickoff()