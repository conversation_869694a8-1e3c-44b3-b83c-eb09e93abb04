2025-07-15 18:14:01 | INFO     | agentscope.manager._model:load_model_configs:138 - Load configs for model wrapper: my_config
2025-07-15 18:14:02 | INFO     | agentscope.service.service_toolkit:add_mcp_servers:465 - Added tool functions from MCP server `amap-amap-sse`: maps_direction_bicycling, maps_direction_driving, maps_direction_transit_integrated, maps_direction_walking, maps_distance, maps_geo, maps_regeocode, maps_ip_location, maps_schema_personal_map, maps_around_search, maps_search_detail, maps_text_search, maps_schema_navi, maps_schema_take_taxi, maps_weather.
2025-07-15 18:14:02 | WARNING  | agentscope.models.openai_model:__init__:117 - Fail to get max_length for doubao-1-5-pro-32k-250115: 'Model [doubao-1-5-pro-32k-250115] not found in OPENAI_MAX_LENGTH. The last updated date is 20231212'
Friday: 
[
    {
        "type": "tool_use",
        "id": "call_0i0mxz9cpmrc7fx40fckh3f5",
        "name": "generate_response",
        "input": {
            "response": "我叫Friday，是你的AI助手，能为你提供各种信息和帮助，无论是解答问题、进行文本创作，还是调用工具完成特定任务，都可以找我哦。"
        }
    }
]
system: Execute function generate_response:
Success
Friday: 我叫Friday，是你的AI助手，能为你提供各种信息和帮助，无论是解答问题、进行文本创作，还是调用工具完成特定任务，都可以找我哦。
user: 北京到上海的行程怎么规划
2025-07-15 18:14:31 | INFO     | agentscope.service.mcp_manager:cleanup:205 - Clean up MCP Server `amap-amap-sse` finished.
