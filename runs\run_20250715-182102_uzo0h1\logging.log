2025-07-15 18:21:03 | INFO     | agentscope.manager._model:load_model_configs:138 - Load configs for model wrapper: my_config
2025-07-15 18:21:03 | INFO     | agentscope.service.service_toolkit:add_mcp_servers:465 - Added tool functions from MCP server `add-tool`: add.
2025-07-15 18:21:03 | INFO     | agentscope.service.service_toolkit:add_mcp_servers:465 - Added tool functions from MCP server `multiply-tool`: multiply.
2025-07-15 18:21:04 | WARNING  | agentscope.models.openai_model:__init__:117 - Fail to get max_length for doubao-1-5-pro-32k-250115: 'Model [doubao-1-5-pro-32k-250115] not found in OPENAI_MAX_LENGTH. The last updated date is 20231212'
Friday: First, call the multiply function to calculate 2345 multiplied by 3456. Then, use the result and call the add function to add 4567 to it. Finally, call the generate_response function to give the result to the user.
[
    {
        "type": "tool_use",
        "id": "call_k1dkx25z60gv9qu4ndwyluwf",
        "name": "multiply",
        "input": {
            "a": 2345,
            "b": 3456
        }
    }
]
2025-07-15 18:21:07 | INFO     | agentscope.service.mcp_manager:execute_tool:268 - Executing multiply...
system: Execute function multiply:
[{'type': 'text', 'text': '8104320', 'annotations': None, 'meta': None}]
2025-07-15 18:21:07 | INFO     | agentscope.service.mcp_manager:cleanup:205 - Clean up MCP Server `multiply-tool` finished.
2025-07-15 18:21:07 | INFO     | agentscope.service.mcp_manager:cleanup:205 - Clean up MCP Server `add-tool` finished.
