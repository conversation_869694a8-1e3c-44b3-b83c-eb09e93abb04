{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/concurrency_and_asynchornous_dependency.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Complex Process using AsyncIO to Manage Concurrency and Asynchronous Dependencies"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** English\n", "\n", "**Agent Components:** None\n", "\n", "**Description:**\n", "\n", "Async is very important when we try to manage a complex process. Agently framework provides `.start_async()` to allow developers create their own async processes.\n", "\n", "Take this process down below as an example, let see how to use asyncio and Agently to manage concurrency and asynchronous dependencies.\n", "\n", "当我们试图管理一个复杂流程的时候，对异步（async）的支持非常重要。Agently框架提供`.start_async()`的方式来允许开发者进行异步请求。\n", "\n", "让我们通过案例来看看如何使用asyncio和Agently来实现如下所示的流程，处理并发和异步依赖。\n", "\n", "```\n", "[generate 3 colors] -> [generate 3 sentences using 3 colors] ->|\n", "                                                               |-> [tell a story using 3 sentences and 3 names]\n", "[generate 2 boys' names and 1 girl's name] --------------------|\n", "```"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Step 1: Install Packages"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install -U Agently\n", "!pip install nest_asyncio"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Demo Code"], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "0340f381-2e60-4bd7-cda3-dc8605b1cb70"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Generate Sentences] Start at: 1703606150.0052722\n", "[Generate Sentences] Job 1 start at: 1703606150.0053837\n", "[Generate Names] Start at: 1703606150.0946443\n", "[Generate Sentences] Colors ['red', 'blue', 'green']\n", "[Generate Sentences] Job 2 start at: 1703606151.4804964\n", "[Generate Names]\n", "{'boy_names': ['<PERSON>', '<PERSON>'], 'girl_name': ['<PERSON>']}\n", "[Generate Names] Finish at: 1703606152.2673044\n", "[Generate Names] Time cost: 2.1726601123809814\n", "[Generate Sentences]\n", "The red apple is juicy.\n", "The sky is blue.\n", "The leaves on the tree are green.\n", "[Generate Sentences] Finish at: 1703606153.2162726\n", "[Generate Sentences] Time cost: 3.211000442504883\n", "[Tell Story] Start at: 1703606153.2164001\n", "[Tell Story]\n", "Once upon a time, in a small town called Appleville, lived three friends named <PERSON>, <PERSON>, and <PERSON>. <PERSON> was known for his love for nature, <PERSON> was a curious and adventurous soul, and <PERSON> was the kindest and most intelligent girl in town.\n", "\n", "One sunny afternoon, <PERSON>, <PERSON>, and <PERSON> decided to go on a nature exploration. As they walked through the vibrant green forest, <PERSON> picked up a red apple from a tree. \"Look at this juicy apple,\" <PERSON> exclaimed, taking a bite and savoring its deliciousness.\n", "\n", "<PERSON>, always observant, pointed towards the sky and said, \"Look, the sky is so blue today! It's the perfect day for our adventure.\" They continued their journey, marveling at the serene blue sky above them.\n", "\n", "As they carried on, they came across a majestic tree, with leaves of vibrant green. <PERSON> couldn't resist climbing up the tree to get a better view of their surroundings. \"The leaves on this tree are so green and lush,\" <PERSON> shouted down to <PERSON> and <PERSON>. \"You won't believe the view from up here!\"\n", "\n", "Together, they explored the forest, discovering beautiful flowers, playful wildlife, and breathtaking landscapes. They shared stories, laughter, and made memories that would last a lifetime.\n", "\n", "The day turned into evening, and as they made their way back home, <PERSON> expressed her gratitude for their wonderful adventure. \"Thank you, <PERSON> and <PERSON>, for joining me today. It was truly an unforgettable experience.\"\n", "\n", "<PERSON> and <PERSON> smiled at <PERSON>, realizing how lucky they were to have such a wonderful friend like her. They knew that their bond and shared love for nature would keep them connected forever.\n", "\n", "And so, the story of <PERSON>, <PERSON>, and <PERSON>, the friends who appreciated the beauty of the world around them, became a legacy in the town of Appleville. The red apple, the blue sky, and the green leaves served as a constant reminder of their incredible journey together.\n", "[Tell Story] Finish at: 1703606167.5968707\n", "[Tell Story] Time cost: 14.380470514297485\n"]}], "source": ["import time\n", "import asyncio\n", "import Agently\n", "\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"model.OpenAI.auth\", { \"api_key\": \"\" })\n", ")\n", "\n", "agent_a = agent_factory.create_agent()\n", "agent_b = agent_factory.create_agent()\n", "\n", "async def generate_sentences():\n", "    start_time = time.time()\n", "    print(f\"[Generate Sentences] Start at: { start_time }\")\n", "    job1_time = time.time()\n", "    print(f\"[Generate Sentences] Job 1 start at: { job1_time }\")\n", "    colors = (\n", "        await agent_a\n", "            .input(\"Generate 3 colors.\")\n", "            .output([(\"String\", \"color\")])\n", "            .start_async()\n", "    )\n", "    print(\"[Generate Sentences] Colors\", colors)\n", "    job2_time = time.time()\n", "    print(f\"[Generate Sentences] Job 2 start at: { job2_time }\")\n", "    sentences = (\n", "        await agent_a\n", "            .input(colors)\n", "            .instruct(\"Generate 3 sentences using different color in {input}\")\n", "            .output([(\"String\", \"sentence\")])\n", "            .start_async()\n", "    )\n", "    print(\"[Generate Sentences]\")\n", "    for sentence in sentences:\n", "        print(sentence)\n", "    end_time = time.time()\n", "    print(f\"[Generate Sentences] Finish at: { end_time }\")\n", "    print(f\"[Generate Sentences] Time cost: { end_time - start_time }\")\n", "    return sentences\n", "\n", "async def generate_names():\n", "    start_time = time.time()\n", "    print(f\"[Generate Names] Start at: { start_time }\")\n", "    names = (\n", "        await agent_b\n", "            .input(\"Generate 2 boys' name and 1 girl's name\")\n", "            .output({\n", "                \"boy_names\": [(\"String\", )],\n", "                \"girl_name\": [(\"String\", )],\n", "            })\n", "            .start_async()\n", "    )\n", "    print(\"[Generate Names]\")\n", "    print(names)\n", "    end_time = time.time()\n", "    print(f\"[Generate Names] Finish at: { end_time }\")\n", "    print(f\"[Generate Names] Time cost: { end_time - start_time }\")\n", "    return names\n", "\n", "async def tell_story(sentences, names):\n", "    start_time = time.time()\n", "    print(f\"[Tell Story] Start at: { start_time }\")\n", "    story = (\n", "        await agent_a\n", "            .input({\n", "                \"sentences\": sentences,\n", "                \"names\": names,\n", "            })\n", "            .instruct(\n", "                \"Tell a story that characters using names in {names}\" +\n", "                \" and must include sentences in {sentences}\"\n", "            )\n", "            .start_async()\n", "    )\n", "    print(\"[Tell Story]\")\n", "    print(story)\n", "    end_time = time.time()\n", "    print(f\"[Tell Story] Finish at: { end_time }\")\n", "    print(f\"[Tell Story] Time cost: { end_time - start_time }\")\n", "    return story\n", "\n", "async def main():\n", "    sentences, names = await asyncio.gather(\n", "        generate_sentences(),\n", "        generate_names()\n", "    )\n", "    await tell_story(sentences, names)\n", "\n", "# use nest_asyncio to enable asyncio.run() in colab\n", "import nest_asyncio\n", "nest_asyncio.apply()\n", "\n", "asyncio.run(main())"]}, {"cell_type": "markdown", "source": ["#### How to Read Output Logs\n", "\n", "First of all, we require async functions [Generate Sentences] and [Generate Names] to start at the same time:\n", "\n", "首先，根据流程图示，我们需要[Generate Sentences]和[Generate Names]两个执行函数同时被执行，在输出中可以看到：\n", "\n", "```\n", "[Generate Sentences] Start at: 1703606150.0052722\n", "[Generate Names] Start at: 1703606150.0946443\n", "```\n", "OK, those two functions started at almost the same time.\n", "\n", "说明两个函数启动执行的时间相差不远。\n", "\n", "Then we require two process 'generate colors' and 'generate sentences' proceed in orders:\n", "\n", "然后，我们需要[Generate Sentences]内部的两个步骤顺序执行，在输出中可以看到：\n", "\n", "```\n", "[Generate Sentences] Job 1 start at: 1703606150.0053837\n", "[Generate Sentences] Job 2 start at: 1703606151.4804964\n", "[Generate Sentences] Finish at: 1703606153.2162726\n", "```\n", "\n", "Also we can check the colors result and the sentences result in the output logs. Sentences result indeed included colors generated at the first time.\n", "\n", "同时，我们也可以从输出中看到颜色(colors)和句子(sentences)的结果，句子的确使用了之前生成的颜色内容，说明执行顺序没有问题。\n", "\n", "```\n", "[Generate Sentences] Colors ['red', 'blue', 'green']\n", "[Generate Sentences]\n", "The red apple is juicy.\n", "The sky is blue.\n", "The leaves on the tree are green.\n", "```\n", "\n", "And we can see [Generate Names] finished in the middle of the [Generate Sentences] process:\n", "\n", "我们也可以看到[Generate Names]在[Generate Sentences]的执行过程中完成了自己的执行：\n", "\n", "```\n", "[Generate Names]\n", "{'boy_names': ['<PERSON>', '<PERSON>'], 'girl_name': ['<PERSON>']}\n", "[Generate Names] Finish at: 1703606152.2673044\n", "```\n", "\n", "At last, [<PERSON>] started when all other processes were finished:\n", "\n", "最后，[Tell Story]这个函数是在所有其他进程都执行完毕后，才开始执行：\n", "\n", "```\n", "[Tell Story] Start at: 1703606153.2164001\n", "```\n", "\n", "The output logs shows all functions executed in the orders we required.\n", "\n", "与我们预期的执行顺序一致。"], "metadata": {"id": "8EPsnOeV4F7F"}}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}