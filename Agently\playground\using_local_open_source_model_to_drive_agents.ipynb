{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "toc_visible": true, "gpuType": "T4", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/using_local_open_source_model_to_drive_agents.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Using Local Open-Source Model to Drive Agents with **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** & **Xinference**"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Showcase Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Description:**\n", "\n", "\"<u>How to use open source models to drive agents and develop applications?</u>\" is a question asked by Agently developer users most frequently. **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** ✕ **Xinference** may be a good answer to it.\n", "\n", "In this case we will show you how to use **Xinference** to start a private local open-source model API server, then use **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** framework to request the model and use the model to drive agents and develop applications easily.\n", "\n", "**Xinference** is so easy to use that you can even download and start a small model in Google Colab T4 server for free. Just [open this showcase file in Colab](https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/using_local_open_source_model_to_drive_agents.ipynb) and try it by your self now!\n", "\n", "“<u>Agently能不能支持使用开源模型/本地模型来驱动Agent，进行应用开发？</u>”这个问题是Agently开发者最常问到的一个问题。经过对多种模型服务方案的调研，Agently团队发现，**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** ✕ **Xinference**可能是目前体验最好的解决方案。\n", "\n", "在本案例中，我们将展示如何使用**Xinference**来启动一个本地私有的开源模型API服务，并使用**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_**应用开发框架通过简单的设置就能访问这个私有模型API，让私有模型驱动Agent，进行应用开发。\n", "\n", "**Xinference**的使用方式非常简单，并且支持你在Google Colab免费提供的T4环境中就能启动一个小参数的模型，这极大地降低了开发者上手尝试的门槛。你也可以根据本文档提供的方法，在拥有更强算力的本地环境中快速启动更大参数的模型。\n", "\n", "现在就[在Google Colab里打开这个案例文档](https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/using_local_open_source_model_to_drive_agents.ipynb)，自己动手试试吧！"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["> ⚠️ To run this showcase, you must make sure that you're using T4 runtime. How to change runtime type? Look at the right top conner of colab page and you can find a \"▾\" symbol next to the RAM status, click it and select the \"change runtime type\" option.\n", ">\n", "> ⚠️ 运行本案例需要使用到T4运行时环境。如何切换？打开Colab界面后，看向右上角，可以在RAM统计图表旁边看到一个\"▾\"标志，点开后选择“更换运行时类型”即可切换。"], "metadata": {"id": "8Tfhjw2m7Rwy"}}, {"cell_type": "markdown", "source": ["## Step 1: Install **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** and **Xinference** Package"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install -U -q typing_extensions xinference[transformers] Agently"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Start **Xinference** Local Server\n", "\n", "To start a local instance of Xinference, run `xinference` in the background via `nohup`:\n", "\n", "要启动一个本地运行的Xinference服务实例，只需要通过`nohup`后台运行`xinferencce`即可："], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "code", "source": ["!nohup xinference-local > xinference.log 2>&1 &"], "metadata": {"id": "7sJdTQta613w"}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": ["This command will start a Xinference server running in Colab machine with default host 127.0.0.1 and default port 9997.\n", "\n", "本行指令将会启动一个运行在Colab机器上的Xinference服务，该服务的默认地址是127.0.0.1，默认端口为9997。"], "metadata": {"id": "wUaqyfbg7Iuj"}}, {"cell_type": "markdown", "source": ["## Step 3: Download and Run a Open-Source Model\n", "\n", "Xinference supports a variety of LLMs. Learn more in https://inference.readthedocs.io/en/latest/models/builtin/.\n", "\n", "We will try `Qwen1.5-chat-1_8B` model as an example.\n", "\n", "Xinference内置支持了一系列语言模型，可以通过访问https://inference.readthedocs.io/zh-cn/latest/models/builtin/ 查看。\n", "\n", "接下来让我们尝试一下通义千问1.5的1.8B参数小模型`Qwen1.5-chat-1_8B`（适合在Colab环境中使用的小模型）。"], "metadata": {"id": "3m5kRJg99OKI"}}, {"cell_type": "code", "source": ["!xinference launch --model-name qwen1.5-chat --size-in-billions 1_8 --model-format pytorch -u my-llm"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LIVYkg8h94D7", "outputId": "ac48e27f-f938-438d-d7cd-c8ad11072b2d"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model uid: my-llm\n"]}]}, {"cell_type": "markdown", "source": ["Download model will take some time, just relax☕️.\n", "\n", "When the downloading and the lauching are finished, you will see a console tip of model uid, which is according your `-u` param value, this model uid will be used in model settings in **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_**.\n", "\n", "下载模型需要花一些时间，请放松等待☕️。\n", "\n", "当下载和启动步骤都完成之后，你将会看到一个关于`model uid`控制台提示信息，这个uid与你在命令中的`-u`参数设置一致，接下来我们会在**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_**框架的设置中使用到它。"], "metadata": {"id": "mvxgXHy5_0bx"}}, {"cell_type": "markdown", "source": ["## Step 4. Using **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** to access the model API and Start Your Development\n", "\n"], "metadata": {"id": "XcFDC-HjABtq"}}, {"cell_type": "markdown", "source": ["Then you will start your llms application development using **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** development framework with some simple settings.\n", "\n", "然后你就可以开始使用**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_**开发框架进行你的LLMs应用开发了~\n", "\n", "因为我们使用的是1.8B参数的小模型，模型对于结构化输出的遵从能力不足，下面的例子主要体现模型的常规问答能力。在学会本案例之后，你可以根据自己的需要选择适合当前场景的模型来驱动你的Agent实例，以完成不同复杂度的任务。"], "metadata": {"id": "vQ9W-iCVLFi1"}}, {"cell_type": "code", "source": ["import Agently\n", "\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"model.OpenAI.auth.api_key\", \"nothing\")\n", "        .set_settings(\"model.OpenAI.options\", {\"model\": \"my-llm\"})\n", "        .set_settings(\"model.OpenAI.url\", \"http://127.0.0.1:9997/v1\")\n", ")\n", "\n", "agent = agent_factory.create_agent()\n", "\n", "@agent.on_event(\"delta\")\n", "def handler(data):\n", "    print(data, end=\"\")\n", "\n", "result = (\n", "    agent\n", "        .input(\"How to wake up <PERSON><PERSON> in Apple iOS?\")\n", "        .start()\n", ")\n", "\n", "pass"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RW9a9u-AJQsH", "outputId": "91762e37-42d9-4981-e7b5-2aac23838479"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["To wake up <PERSON><PERSON> in Apple iOS, follow these steps:\n", "\n", "1. Tap on the multitasking bar at the bottom of your screen.\n", "2. Scroll down and tap on the \"Siri\" icon or press and hold it until you see a list of options.\n", "3. Choose the option that says \"Wake Me Up.\"\n", "4. If <PERSON><PERSON> is already active, you may need to swipe left from the multitasking bar to dismiss <PERSON><PERSON>'s display.\n", "\n", "Alternatively, you can also use voice commands to wake up <PERSON><PERSON> by saying \"Hey Sir<PERSON>\" when you turn on your device, or by using the Wake Up function on the Siri app on your iPhone. Here's how:\n", "\n", "- When you turn on your phone, say \"Hey Siri\" loudly from within the same app (e.g., Settings > <PERSON>i).\n", "- If <PERSON><PERSON> is already active, say \"Wake Up\" again to bring up her interface.\n", "\n", "Alternatively, if you're using an older version of iOS, you can also use the \"Hey Siri\" feature to wake up <PERSON><PERSON> directly from the lock screen. Follow these steps:\n", "\n", "- On your iPhone, open the lock screen.\n", "- Say \"Hey <PERSON><PERSON>\" aloud to wake up <PERSON><PERSON>.\n", "- Once <PERSON><PERSON> is activated, she will appear on your screen as usual.\n", "\n", "Keep in mind that <PERSON><PERSON>'s wake-up functionality may vary depending on your model of iPhone and iOS version. For example, newer devices may have a dedicated Wake Up button or gesture recognition to activate <PERSON><PERSON> more easily. It's always a good idea to check the documentation for your specific iPhone model to ensure that you know how to wake it up using <PERSON><PERSON>'s built-in features."]}]}, {"cell_type": "markdown", "source": ["## Extra Reading\n", "\n", "**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_**\n", "\n", "[Agently ShowCase Playground](https://github.com/Maplemx/Agently/tree/main/playground)\n", "\n", "[Agently Application Development Handbook](https://github.com/Maplemx/Agently/blob/main/docs/guidebook/application_development_handbook.ipynb)\n", "\n", "[Agently Step by Step开发指南-中文版](https://github.com/Maplemx/Agently/blob/main/docs/guidebook/Agently_step_by_step_guide.ipynb)\n", "\n", "**Xinference**\n", "\n", "[Xinference Welcome to Document](https://inference.readthedocs.io/en/latest/index.html)\n", "\n", "[Xinference Offical Quick Start - Colab Document](https://colab.research.google.com/github/xorbitsai/inference/blob/main/examples/Xinference_Quick_Start.ipynb)\n", "\n", "[Xinference Models Document](https://inference.readthedocs.io/en/latest/models/index.html)\n", "\n"], "metadata": {"id": "hC2YBY68V8Q6"}}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}