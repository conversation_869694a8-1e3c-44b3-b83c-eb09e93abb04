{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMt5iOkOM8VJ410hEYjRxbs", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/test_run_template.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["## **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_ 3.0 Test Run Template**"], "metadata": {"id": "xSv8xcNocskE"}}, {"cell_type": "markdown", "source": ["## Step 1. Runtime Package Installation\n", "\n", "⬇️ Run these installation commands first."], "metadata": {"id": "SnfVg7evc0I_"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2io053h7cbXx"}, "outputs": [], "source": ["!pip install Agently"]}, {"cell_type": "markdown", "source": ["## Step 2. Choose Your Model then Start Your Test\n", "\n", "ℹ️ Don't know how to start or what to code?\n", "\n", "- Read [📚 Agently Application Development Handbook](https://github.com/Maplemx/Agently/blob/main/docs/guidebook/application_development_handbook.ipynb)\n", "\n", "- Explore [💡 Agently Playground to Find Some Ideas](https://github.com/Maplemx/Agently/tree/main/playground)"], "metadata": {"id": "CwCNtHWhdquA"}}, {"cell_type": "code", "source": ["import Agently\n", "agent_factory = Agently.AgentFactory()\n", "\n", "# Model Settings\n", "\n", "## OpenAI - GPT 3.5-turbo | GPT 4 | GPT 4 Vision\n", "agent_factory\\\n", "    .set_settings(\"current_model\", \"OpenAI\")\\\n", "    .set_settings(\"model.OpenAI.auth\", { \"api_key\": \"\" })\n", "    #.set_settings(\"model.OpenAI.url\", \"https://redirect-service-provider/api/v1\")\\\n", "    ## optional, set request options followed OpenAI API document's instruction\n", "    #.set_settings(\"model.OpenAI.options\", { \"model\": \"gpt-4\" })\\\n", "    ## optional, important, set this if you want to use proxy!\n", "    ## if you are using Clash, VPN, V2Ray to visit OpenAI API, you must check your\n", "    ## client to find your proxy address, then set the address as value here.\n", "    #.set_proxy(\"http://127.0.0.1:7890\")\n", "\n", "## ZhipuAI - ChatGLM-turbo | CharacterGLM\n", "'''\n", "agent_factory\\\n", "    .set_settings(\"current_model\", \"ZhipuAI\")\\\n", "    .set_settings(\"model.ZhipuAI.auth\", { \"api_key\": \"\" })\n", "'''\n", "## Bai<PERSON> ERNIE (WenXin)\n", "'''\n", "agent_factory\\\n", "    .set_settings(\"current_model\", \"ERNIE\")\\\n", "    .set_settings(\"model.ERNIE.auth\", {\n", "        \"aistudio\": \"\",\n", "    })\n", "'''\n", "\n", "# Start Your Test\n", "## Turn on debug mode if you want\n", "#agent_factory.set_settings(\"is_debug\", True)\n", "\n", "## Your code goes here:\n", "agent = agent_factory.create_agent()\n", "agent.active_session()\n", "while True:\n", "    user_input = input(\"[USER]: \")\n", "    if user_input == \"#exit\":\n", "        break\n", "    print(\"[AGENT]: \", end=\"\")\n", "    agent\\\n", "        .input(user_input)\\\n", "        .on_delta(lambda data: print(data, end=\"\"))\\\n", "        .start()\n", "    print(\"\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SaAiE5-ceQrf", "outputId": "0ab28888-b5f0-435e-ac09-9dc8ea78015c"}, "execution_count": null, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[USER]: Hi, how's your day?\n", "[AGENT]: Hello! As an AI, I don't have feelings, but I'm here to assist you. How can I help you today?\n", "[USER]: Introduce Python then.\n", "[AGENT]: Python is a high-level, interpreted programming language known for its simplicity and readability. It was created by <PERSON> and released in 1991. Python emphasizes code readability by using a clear and intuitive syntax, making it easier to write and understand.\n", "\n", "Python is a versatile language that can be used for various types of programming tasks, including web development, data analysis, machine learning, artificial intelligence, scientific computing, game development, and more. Its extensive standard library and wide range of third-party modules make it highly versatile and popular among developers.\n", "\n", "One of the key advantages of Python is its strong community support. There are countless resources and libraries available, making it easy to find solutions and learn from others. Python also offers great integration with other languages and platforms, making it an excellent choice for building complex projects.\n", "\n", "Some notable features of Python include:\n", "\n", "1. Simple and readable syntax: Python code is easy to understand and write, making it suitable for both beginners and experienced developers.\n", "2. Interpreted language: Python doesn't require a separate compilation step, allowing for quick and easy development.\n", "3. Object-oriented programming: Python supports object-oriented programming principles such as inheritance, polymorphism, and encapsulation.\n", "4. Extensive standard library: Python comes with a vast collection of modules and libraries, providing efficient solutions for various programming tasks.\n", "5. Cross-platform compatibility: Python code can run on major operating systems like Windows, macOS, and Linux without modification.\n", "6. Easy integration: Python allows seamless integration with other programming languages such as C/C++, Java, and more.\n", "\n", "Overall, Python's simplicity, readability, versatility, and extensive community support have made it one of the most popular programming languages for a wide range of applications.\n", "[USER]: Can you speak Spanish?\n", "[AGENT]: <PERSON><PERSON>, puedo hablar español. ¿En qué puedo ayudarte? (Yes, I can speak Spanish. How can I assist you?)\n", "[USER]: What did we say?\n", "[AGENT]: You asked if I could speak Spanish, and I responded affirmatively and asked how I could help you.\n", "[USER]: #exit\n"]}]}]}