#!/usr/bin/env python3
"""
调试evalscope数据集问题的脚本
"""

import os
import json
import requests
from pathlib import Path

def check_dataset_file():
    """检查本地数据集文件"""
    print("=== 检查本地数据集文件 ===")
    
    # 常见的数据集缓存路径
    possible_paths = [
        "~/.cache/evalscope/datasets/open_qa.jsonl",
        "~/.cache/huggingface/datasets/open_qa.jsonl", 
        "./open_qa.jsonl",
        "./datasets/open_qa.jsonl",
        "~/.evalscope/datasets/open_qa.jsonl"
    ]
    
    for path_str in possible_paths:
        path = Path(path_str).expanduser()
        if path.exists():
            print(f"找到数据集文件: {path}")
            print(f"文件大小: {path.stat().st_size / (1024*1024):.2f} MB")
            
            # 读取前几行检查内容
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    lines = []
                    for i, line in enumerate(f):
                        if i >= 5:  # 只读前5行
                            break
                        lines.append(line.strip())
                    
                    print(f"文件前5行内容:")
                    for i, line in enumerate(lines):
                        if line:
                            try:
                                data = json.loads(line)
                                print(f"  行{i+1}: {json.dumps(data, ensure_ascii=False)[:200]}...")
                            except json.JSONDecodeError:
                                print(f"  行{i+1}: {line[:200]}...")
                        else:
                            print(f"  行{i+1}: (空行)")
                            
                # 统计总行数
                with open(path, 'r', encoding='utf-8') as f:
                    total_lines = sum(1 for line in f if line.strip())
                    print(f"总有效行数: {total_lines}")
                    
            except Exception as e:
                print(f"读取文件时出错: {e}")
            
            return path
    
    print("未找到本地数据集文件")
    return None

def test_evalscope_dataset_loading():
    """测试evalscope数据集加载"""
    print("\n=== 测试evalscope数据集加载 ===")
    
    try:
        from evalscope.perf.arguments import Arguments
        from evalscope.perf.benchmark import generate_requests_from_dataset
        
        # 创建测试配置
        test_cfg = Arguments(
            dataset='openqa',
            min_tokens=1,  # 最宽松的设置
            max_tokens=10000,
            min_prompt_length=1,
            max_prompt_length=10000,
            number=[5]  # 只测试5个请求
        )
        
        print("尝试加载数据集...")
        
        # 尝试生成请求
        request_count = 0
        async def count_requests():
            nonlocal request_count
            async for request in generate_requests_from_dataset(test_cfg, None):
                request_count += 1
                print(f"成功生成请求 {request_count}: {str(request)[:100]}...")
                if request_count >= 5:  # 只检查前5个
                    break
        
        import asyncio
        asyncio.run(count_requests())
        
        print(f"成功生成了 {request_count} 个请求")
        
    except Exception as e:
        print(f"测试evalscope数据集加载时出错: {e}")
        import traceback
        traceback.print_exc()

def download_openqa_manually():
    """手动下载openqa数据集"""
    print("\n=== 手动下载openqa数据集 ===")
    
    # OpenQA数据集的可能来源
    urls = [
        "https://huggingface.co/datasets/openqa/resolve/main/open_qa.jsonl",
        "https://raw.githubusercontent.com/openai/simple-evals/main/sampled_pdfs/openqa.jsonl"
    ]
    
    for url in urls:
        try:
            print(f"尝试从 {url} 下载...")
            response = requests.get(url, stream=True)
            
            if response.status_code == 200:
                filename = "open_qa_manual.jsonl"
                with open(filename, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                
                print(f"下载成功: {filename}")
                
                # 检查文件内容
                with open(filename, 'r', encoding='utf-8') as f:
                    lines = [line.strip() for line in f.readlines()[:3]]
                    print("文件前3行:")
                    for i, line in enumerate(lines):
                        if line:
                            print(f"  {i+1}: {line[:100]}...")
                
                return filename
                
            else:
                print(f"下载失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"下载出错: {e}")
    
    return None

def create_test_dataset():
    """创建测试数据集"""
    print("\n=== 创建测试数据集 ===")
    
    test_data = [
        {
            "question": "什么是人工智能？",
            "answer": "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。"
        },
        {
            "question": "Python是什么？", 
            "answer": "Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。"
        },
        {
            "question": "机器学习的基本概念是什么？",
            "answer": "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。"
        },
        {
            "question": "深度学习与传统机器学习有什么区别？",
            "answer": "深度学习使用多层神经网络来学习数据的复杂模式，而传统机器学习通常使用更简单的算法。"
        },
        {
            "question": "什么是自然语言处理？",
            "answer": "自然语言处理是人工智能的一个分支，专注于使计算机能够理解、解释和生成人类语言。"
        }
    ]
    
    filename = "test_openqa.jsonl"
    with open(filename, 'w', encoding='utf-8') as f:
        for item in test_data:
            # 转换为evalscope期望的格式
            formatted_item = {
                "prompt": item["question"],
                "response": item["answer"],
                "text": f"问题: {item['question']}\n答案: {item['answer']}"
            }
            f.write(json.dumps(formatted_item, ensure_ascii=False) + '\n')
    
    print(f"创建测试数据集: {filename}")
    print(f"包含 {len(test_data)} 条记录")
    
    return filename

def main():
    """主函数"""
    print("开始调试evalscope数据集问题...")
    
    # 1. 检查本地文件
    dataset_file = check_dataset_file()
    
    # 2. 测试evalscope加载
    test_evalscope_dataset_loading()
    
    # 3. 尝试手动下载
    if not dataset_file:
        manual_file = download_openqa_manually()
        if manual_file:
            dataset_file = manual_file
    
    # 4. 创建测试数据集
    test_file = create_test_dataset()
    
    print(f"\n=== 调试完成 ===")
    print("建议:")
    print("1. 检查数据集文件是否存在且格式正确")
    print("2. 降低min_tokens和min_prompt_length的限制")
    print("3. 使用测试数据集验证配置")
    print(f"4. 可以尝试使用创建的测试文件: {test_file}")

if __name__ == "__main__":
    main()