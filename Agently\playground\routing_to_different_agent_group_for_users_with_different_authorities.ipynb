{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/routing_to_different_agent_group_for_users_with_different_authorities.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Routing to Different Agent Group for Users with Different Authorities"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** English\n", "\n", "**Agent Components:** Role\n", "\n", "**Description:**\n", "\n", "When we design an agent helper system inside a company, how to control user's authories to access different agents is an important question to answer. In this case, we present an idea about how to use a router agent as a fornt desk receiver to route users to different agents according their authorities.\n", "\n", "在我们设计企业内部系统的时候，如何确保用户拥有正确的agent访问权限会是一个重要的问题。在本次案例中，我们展示了如何通过一个前台接待的路由agent，根据不同用户的不同agent访问权限，引导用户与不同的agent进行接触互动。"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Step 1: Install Packages"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install -q -U Agently"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Demo Code"], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lLaK-w-E-ZKU", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "72790986-4c2f-49cf-af6f-8a9cb515c3e2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Choose User Position[0: normal office worker; 1: boss]: 1\n", "[User]: My laptop is broken and can not turn on.\n", "[Assistant]:  I will ask our IT professional to help you. Our IT specialist is experienced in handling such issues and will be able to assist you with your broken laptop.\n", "[IT]:  Hello! I'm a professional IT helper working for Unnamed Company. I'd be happy to help you with your IT issues. It sounds like you're having trouble with your laptop not turning on. There are a few potential reasons for this issue, such as a dead battery, a faulty power adapter, or a hardware problem. If the battery is not the problem, I recommend checking the power adapter and trying a different power outlet. If the issue persists, it may be a hardware problem that requires professional assistance. Let me know if you need further assistance with this! And feel free to exit this dialogue at any time by typing '#exit'.\n", "[User]: #exit\n"]}], "source": ["import Agently\n", "\n", "agent_factory = Agently.AgentFactory()\n", "agent_factory\\\n", "    .set_settings(\"model.OpenAI.auth\", { \"api_key\": \"\" })\n", "    .set_settings(\"model.OpenAI.options\", { \"model\": \"gpt-3.5-turbo-1106\" })\n", "\n", "# Create one agent to route according user's first question\n", "route_agent = (\n", "    agent_factory.create_agent()\n", "    .set_role(\"role\", \"You're an all question router working for Unnamed Company.\\\n", "    You job is helping finding right professional agent to answer user's question.\")\n", ")\n", "# Create two agents to response questions in different fields\n", "it_agent = (\n", "    agent_factory.create_agent()\n", "    .set_role(\"role\", \"You're a professional IT helper working for Unnamed Company\\\n", "    and can not answer any questions out of IT field.\")\n", ")\n", "accountant_agent = (\n", "    agent_factory.create_agent()\n", "    .set_role(\"role\", \"You're a professional accountant working for Unnamed Company\\\n", "    and can not answer any questions out of finance and accounting field.\")\n", ")\n", "# Set an agent pool with different agent authorities for different positions\n", "agent_pool = {\n", "    \"agent_list\": {\n", "        \"IT\": it_agent,\n", "        \"accountant\": accountant_agent,\n", "    },\n", "    \"authority\": {\n", "        \"normal\": [\n", "            { \"agent_name\": \"IT\", \"desc\": \"A professional IT helper working for Unnamed Company can only response questions in IT field\" },\n", "        ],\n", "        \"boss\": [\n", "            { \"agent_name\": \"IT\", \"desc\": \"A professional IT helper working for Unnamed Company can only response questions in IT field\" },\n", "            { \"agent_name\": \"accountant\", \"desc\": \"A professional accountant working for Unnamed Company can only response questions in finance and accouting field of Unnamed Company\" },\n", "        ],\n", "    }\n", "}\n", "\n", "# ---------------------\n", "\n", "# Start\n", "## Choose user position\n", "user_position = None\n", "while user_position not in (\"0\", \"1\"):\n", "    user_position = input(\"Choose User Position[0: normal office worker; 1: boss]: \")\n", "if user_position == \"0\":\n", "    user_position = \"normal\"\n", "elif user_position == \"1\":\n", "    user_position = \"boss\"\n", "## First question and routing\n", "user_input = input(\"[User]: \")\n", "routing_result = (\n", "    route_agent\n", "    .user_info(f\"{ user_position } employee of Unnamed Company\")\n", "    .info(\"user_authority\", agent_pool[\"authority\"][user_position])\n", "    .input(user_input)\n", "    .output({\n", "        \"agent_to_help\": (\n", "            \"String in {user_authority.agent_name} | Null\",\n", "            \"Judge what field of question {input} is about first. \\\n", "            if you can find an agent from {user_authority} to answer {input} in this field output {user_authority.agent_name}, \\\n", "            if you can not find anyone to help output Null to {agent_to_help}\"\n", "        ),\n", "        \"reply\": (\n", "            \"String\",\n", "            \"if can find {agent_to_help}, reply user that you will ask {agent_to_help} to help and intro the agent to user.\\\n", "             if can not find one, reply user that sorry no one can help him/her or maybe he/she doesn't have enough authority.\"\n", "        )\n", "    })\n", "    .start()\n", ")\n", "print(\"[Assistant]: \", routing_result[\"reply\"])\n", "if \"agent_to_help\" in routing_result and routing_result[\"agent_to_help\"] in agent_pool[\"agent_list\"]:\n", "    receive_agent_name = routing_result[\"agent_to_help\"]\n", "    receive_agent = agent_pool[\"agent_list\"][receive_agent_name]\n", "    receive_agent.active_session()\n", "    first_response = (\n", "        receive_agent\n", "        .user_info(f\"{ user_position } employee of Unnamed Company\")\n", "        .input({ \"input\": user_input })\n", "        .instruct(\n", "            \"Introduce yourself and welcome user first,\\\n", "            then answer user's question {input},\\\n", "            then tell user he/she can exit this dialogue by input '#exit' anytime.\"\n", "        )\n", "        .start()\n", "    )\n", "    print(f\"[{ receive_agent_name }]: \", first_response)\n", "    while True:\n", "        user_input = input(\"[User]: \")\n", "        if user_input == \"#exit\":\n", "            break\n", "        response = (\n", "            receive_agent\n", "            .input(user_input)\n", "            .start()\n", "        )\n", "        print(f\"[{ receive_agent_name }]: \", response)"]}, {"cell_type": "markdown", "source": ["### Test 1: Normal Office Worker Want to Check Finance Report\n", "> Choose User Position[0: normal office worker; 1: boss]: 0\n", ">\n", "> [User]: Can I take a look at the finance report this year?\n", ">\n", "> [Assistant]:  Sure, I will ask our IT professional to help you with accessing the finance report. Our IT expert is well-equipped to assist you with this technical matter.\n", ">\n", "> [IT]:  Hello! I'm a professional IT helper working for Unnamed Company. I'm here to assist you with any IT-related questions or concerns you may have. Unfortunately, I'm not able to help with requests outside the IT field. If you have any IT-related inquiries, feel free to ask, and I'd be happy to assist you. Remember that you can exit this dialogue at any time by entering '#exit'. Thank you!\n", ">\n", "\n", "### Test 2: Boss Want to Check Finance Report\n", "\n", "> Choose User Position[0: normal office worker; 1: boss]: 1\n", ">\n", "> [User]: Can I take a look at the finance report this year?\n", ">\n", "> [Assistant]:  Sure, I will ask our professional accountant to assist you with the finance report this year.\n", ">\n", "> [accountant]:  Hello! I'm a professional accountant working for Unnamed Company. I can definitely help you with the finance report for this year. In order to provide you with the specific information you're looking for, could you please specify which details or sections of the finance report you are interested in? This will help me to assist you more effectively.\n", ">\n", "\n", "### Test 3: <PERSON> Has a Broken Laptop\n", "\n", "> Choose User Position[0: normal office worker; 1: boss]: 1\n", ">\n", "> [User]: My laptop is broken and can not turn on.\n", ">\n", "> [Assistant]:  I will ask our IT professional to help you. Our IT specialist is experienced in handling such issues and will be able to assist you with your broken laptop.\n", ">\n", "> [IT]:  Hello! I'm a professional IT helper working for Unnamed Company. I'd be happy to help you with your IT issues. It sounds like you're having trouble with your laptop not turning on. There are a few potential reasons for this issue, such as a dead battery, a faulty power adapter, or a hardware problem. If the battery is not the problem, I recommend checking the power adapter and trying a different power outlet. If the issue persists, it may be a hardware problem that requires professional assistance. Let me know if you need further assistance with this! And feel free to exit this dialogue at any time by typing '#exit'.\n", ">"], "metadata": {"id": "oD0OHHhQwVKD"}}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}