import cv2
import time
import os
from datetime import datetime


def capture_frames_from_rtsp(rtsp_url, interval=5, output_dir="frames"):
    """
    从RTSP摄像头每隔指定时间保存一帧

    Args:
        rtsp_url: RTSP流地址
        interval: 保存间隔（秒）
        output_dir: 输出目录
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 连接RTSP流
    cap = cv2.VideoCapture(rtsp_url)

    # 设置缓冲区大小，减少延迟
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

    if not cap.isOpened():
        print(f"无法连接到RTSP流: {rtsp_url}")
        return

    print(f"成功连接到RTSP流，开始每{interval}秒保存一帧...")

    frame_count = 0
    last_save_time = time.time()

    try:
        while True:
            ret, frame = cap.read()

            if not ret:
                print("无法读取帧，尝试重新连接...")
                cap.release()
                time.sleep(2)
                cap = cv2.VideoCapture(rtsp_url)
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                continue

            current_time = time.time()

            # 检查是否到了保存时间
            if current_time - last_save_time >= interval:
                # 生成文件名（包含时间戳）
                timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
                filename = f"frame_{frame_count}_{timestamp}.jpg"
                filepath = os.path.join(output_dir, filename)

                # 保存帧
                cv2.imwrite(filepath, frame)
                print(f"保存帧: {filepath}")

                frame_count += 1
                last_save_time = current_time

            # 显示实时视频（可选）
            cv2.imshow("RTSP Stream", frame)

            # 按'q'退出
            if cv2.waitKey(1) & 0xFF == ord("q"):
                break

    except KeyboardInterrupt:
        print("\n程序被用户中断")

    finally:
        cap.release()
        cv2.destroyAllWindows()
        print("资源已释放")


if __name__ == "__main__":
    # 海康摄像头RTSP地址格式示例
    # rtsp://用户名:密码@IP地址:端口/Streaming/Channels/通道号

    # 请根据你的摄像头信息修改以下参数
    RTSP_URL = "rtsp://admin:GZE9168168@**************/Streaming/Channels/1"
    SAVE_INTERVAL = 5  # 保存间隔（秒）
    OUTPUT_DIR = "captured_frames"  # 输出目录

    print("海康RTSP摄像头帧捕获程序")
    print("=" * 40)
    print(f"RTSP地址: {RTSP_URL}")
    print(f"保存间隔: {SAVE_INTERVAL}秒")
    print(f"输出目录: {OUTPUT_DIR}")
    print("按 'q' 键退出程序")
    print("=" * 40)

    capture_frames_from_rtsp(RTSP_URL, SAVE_INTERVAL, OUTPUT_DIR)
