import requests
import json

def list_datasets():
    """列出所有可用的数据集"""
    token = "fastgpt-gZHVXrSu6UqOmvmAKsYeHhtkxdywJBqkwuM8KuZ8ia3TxdGxJqQv6Ic53ctajPJf"
    
    # 尝试获取数据集列表的API端点
    url = 'http://192.168.10.251:3000/api/core/dataset/list'
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 常见的数据集列表请求参数
    data = {
        "offset": 0,
        "pageSize": 20
    }
    
    print("获取数据集列表...")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"数据集列表: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 提取数据集信息
            if 'data' in result and 'list' in result['data']:
                datasets = result['data']['list']
                print(f"\n找到 {len(datasets)} 个数据集:")
                for i, dataset in enumerate(datasets):
                    print(f"{i+1}. 名称: {dataset.get('name', 'N/A')}")
                    print(f"   ID: {dataset.get('_id', 'N/A')}")
                    print(f"   类型: {dataset.get('type', 'N/A')}")
                    print()
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_with_get_method():
    """尝试使用GET方法获取数据集"""
    token = "fastgpt-gZHVXrSu6UqOmvmAKsYeHhtkxdywJBqkwuM8KuZ8ia3TxdGxJqQv6Ic53ctajPJf"
    
    url = 'http://192.168.10.251:3000/api/core/dataset/list'
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("尝试GET方法...")
    
    try:
        response = requests.get(url, headers=headers)
        print(f"GET响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"GET数据集列表: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"GET错误响应: {response.text}")
            
    except Exception as e:
        print(f"GET请求异常: {e}")

if __name__ == "__main__":
    list_datasets()
    print("\n" + "="*50 + "\n")
    test_with_get_method()