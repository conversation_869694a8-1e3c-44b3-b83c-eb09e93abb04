#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版的商业计划生成脚本
"""

from crewai import Agent, Task, Crew, LLM
import os

# 方法1: 尝试使用更兼容的LLM配置
def create_llm_v1():
    return LLM(
        model="ep-20250122141838-8gz65",
        temperature=0.7,
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key="5dd64d02-d0b4-48fd-91f0-d4a91f359456"
    )

# 方法2: 尝试使用OpenAI兼容的配置
def create_llm_v2():
    # 设置环境变量
    os.environ["OPENAI_API_KEY"] = "5dd64d02-d0b4-48fd-91f0-d4a91f359456"
    os.environ["OPENAI_API_BASE"] = "https://ark.cn-beijing.volces.com/api/v3"
    
    return LLM(
        model="ep-20250122141838-8gz65",
        temperature=0.7
    )

# 方法3: 使用litellm格式
def create_llm_v3():
    return LLM(
        model="openai/ep-20250122141838-8gz65",
        api_base="https://ark.cn-beijing.volces.com/api/v3",
        api_key="5dd64d02-d0b4-48fd-91f0-d4a91f359456",
        temperature=0.7
    )

def test_crew_with_llm(llm_func, version_name):
    try:
        print(f"\n=== 测试 {version_name} ===")
        
        # 创建LLM
        llm = llm_func()
        print(f"LLM创建成功: {version_name}")
        
        # 创建一个简单的智能体
        agent = Agent(
            role='测试助手',
            goal='回答简单问题',
            backstory='你是一个测试助手。',
            verbose=True,
            llm=llm
        )
        
        # 创建一个简单任务
        task = Task(
            description='请简单介绍一下你自己。',
            agent=agent,
            expected_output="一段简短的自我介绍。"
        )
        
        # 创建Crew
        crew = Crew(
            agents=[agent],
            tasks=[task],
            verbose=True
        )
        
        print("开始执行测试任务...")
        result = crew.kickoff()
        print(f"✅ {version_name} 测试成功!")
        print(f"结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ {version_name} 测试失败: {str(e)}")
        return False

def main():
    print("开始测试不同的LLM配置方法...")
    
    # 测试方法1
    if test_crew_with_llm(create_llm_v1, "原始配置"):
        print("使用原始配置成功!")
        return
    
    # 测试方法2
    if test_crew_with_llm(create_llm_v2, "环境变量配置"):
        print("使用环境变量配置成功!")
        return
        
    # 测试方法3
    if test_crew_with_llm(create_llm_v3, "litellm配置"):
        print("使用litellm配置成功!")
        return
    
    print("所有配置方法都失败了，可能需要查看CrewAI文档或更换LLM提供商")

if __name__ == "__main__":
    main()
