{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/workflow_series_02_using_condition_to_branch_off_inbpy.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "dAzfqHDCAXZe"}, "source": ["# Using Condition to Branch Off `#Agently_Workflow_Showcase_Series - 02`"]}, {"cell_type": "markdown", "metadata": {"id": "kyLFmv_l-aIx"}, "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Series Link**:\n", "\n", "[Last Document]: [01 - Let's Get Started by Building a Multi-Round Chat with Agently Workflow!](https://github.com/Maplemx/Agently/blob/main/playground/workflow_series_01_building_a_multi_round_chat.ipynb)\n", "\n", "[Next Document]: [03 - Using Decorator to Create <PERSON><PERSON> Faster](https://github.com/Maplemx/Agently/blob/main/playground/workflow_series_03_using_decorator_to_create_chunks.ipynb)\n", "\n", "**Description:**\n", "\n", "In the first showcase, we demostrated how to use **Agently Workflow** to create a multi-round chat.\n", "\n", "You may notice that user can not exit the chat loop unless using the interupt command. That'll cause ugly error report and of course we can not use that in product.\n", "\n", "So, in this showcase, let me introduce the **condition** feature of Agently Workflow. We can use this feature to make a judgement according the data value from the output handle and branch off the connection to different input handles. Then we can put an end to the infinite chat loop.\n", "\n", "在第一个案例中，我们演示了如何使用框架v3.2版本**Agently Workflow**的新功能来创建一个多轮对话。你可能也注意到了，在上一个演示中，我们的用户除了使用强制终止按钮之外，没有别的办法可以退出多轮对话。而点击强制终止按钮，则会产生一堆报错信息，并且，我们也不可能在生产环境中让用户进行强制终止操作。\n", "\n", "所以，在这一个案例中，我们将介绍**condition**这个能力。我们可以使用这个能力对输出端点(output handle)输出的数据值进行判断，并根据判断结果，将输出端点连接到不同的下游输入端点（input handle）去，从而实现让用户能够退出这个无限的循环。"]}, {"cell_type": "markdown", "metadata": {"id": "nRsfMu4lAJZF"}, "source": ["## Step 1: Install Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nsst7pOAANlr"}, "outputs": [], "source": ["!pip install -q -U Agently"]}, {"cell_type": "markdown", "metadata": {"id": "diFJhvd3ORd1"}, "source": ["> ℹ️ Agently Workflow is a new feature in version >= `3.2.0`"]}, {"cell_type": "markdown", "metadata": {"id": "_-1gryYwASPM"}, "source": ["## Step 2: Create a Multi-Round Chat Workflow in the Basic Way"]}, {"cell_type": "markdown", "source": ["### Workflow Graph\n", "\n", "**Workflow in the Original Showcase:**\n", "\n", "<img width = \"640\" src = \"https://github.com/Maplemx/Agently/assets/4413155/6da2539d-afd8-4b57-9af8-0135e6afd61d\"></img>\n", "\n", "**Optimized Workflow in This Showcase:**\n", "\n", "<img width = \"640\" src = \"https://github.com/Maplemx/Agently/assets/4413155/2be63331-c119-4c93-aa14-fc138004976d\"></img>"], "metadata": {"id": "NxQAiPsTiZ4S"}}, {"cell_type": "markdown", "source": ["### Code"], "metadata": {"id": "eeM97ZUGitlr"}}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "f53e04b7-4aaa-4e95-e770-7f621ed5953c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["User Input\n", "User Input\n", "[User]: <PERSON>, I'm <PERSON>\n", "[Assistant]:  Hello <PERSON>, nice to meet you.\n", "[User]: What's your name?\n", "[Assistant]:  I am Gemini, a multimodal AI language model developed by Google.\n", "[User]: #exit\n", "Bye~👋\n"]}], "source": ["# SAME CODE FROM THE FIRST SHOWCASE\n", "# FROM HERE =======================\n", "import Agently\n", "import logging\n", "\n", "agent_factory = (\n", "        Agently.AgentFactory()\n", "        .set_settings(\"model.Google.auth.api_key\", \"\")\n", "        .set_settings(\"current_model\", \"Google\")\n", ")\n", "\n", "# Step 0. Create Workflow Instance and Agent Instance\n", "workflow = Agently.Workflow()\n", "# reset logger level to WARNING\n", "workflow.settings.set(\"logger\", logging.getLogger(\"Workflow\").setLevel(logging.WARNING))\n", "agent = agent_factory.create_agent()\n", "\n", "# Step 1. C<PERSON> <PERSON><PERSON>\n", "## Start Chunk\n", "start_chunk = workflow.schema.create_chunk(\n", "    title = \"Multi-Round Chat\",\n", "    type = \"Start\"\n", ")\n", "\n", "## User Input Chunk\n", "user_input_chunk = workflow.schema.create_chunk(\n", "    title = \"User Input\",\n", "    handles = {\n", "        \"outputs\": [{ \"handle\": \"user_input\" }],\n", "    },\n", "    executor = lambda inputs, storage: {\n", "        \"user_input\": input(\"[User]: \")\n", "    },\n", ")\n", "\n", "## Assistant <PERSON><PERSON>\n", "def assistant_reply_executor(inputs, storage):\n", "    chat_history = storage.get(\"chat_history\") or []\n", "    reply = (\n", "        agent\n", "            .chat_history(chat_history)\n", "            .input(inputs[\"user_input\"])\n", "            .start()\n", "    )\n", "    print(\"[Assistant]: \", reply)\n", "    return { \"assistant_reply\": reply }\n", "assistant_reply_chunk = workflow.schema.create_chunk(\n", "    title = \"Assistant Reply\",\n", "    handles = {\n", "        \"inputs\": [{ \"handle\": \"user_input\" }],\n", "        \"outputs\": [{ \"handle\": \"assistant_reply\" }],\n", "    },\n", "    executor = assistant_reply_executor,\n", ")\n", "\n", "## Update Chat History Chunk\n", "def update_chat_history_executor(inputs, storage):\n", "    chat_history = storage.get(\"chat_history\") or []\n", "    chat_history.append({ \"role\": \"user\", \"content\": inputs[\"user_input\"] })\n", "    chat_history.append({ \"role\": \"assistant\", \"content\": inputs[\"assistant_reply\"] })\n", "    storage.set(\"chat_history\", chat_history)\n", "    return\n", "update_chat_history_chunk = workflow.schema.create_chunk(\n", "    title = \"Update Chat History\",\n", "    handles = {\n", "        \"inputs\": [\n", "            { \"handle\": \"user_input\" },\n", "            { \"handle\": \"assistant_reply\" },\n", "        ],\n", "    },\n", "    executor = update_chat_history_executor,\n", ")\n", "\n", "# ======================= TO HERE\n", "\n", "## Add a new chunk named `Goodbye Chunk`\n", "goodbye_chunk = workflow.schema.create_chunk(\n", "    title = \"Goodbye Chunk\",\n", "    executor = lambda inputs, storage: print(\"Bye~👋\")\n", ")\n", "\n", "# Step 2. Connect Chunks in Orders\n", "start_chunk.connect_to(user_input_chunk)\n", "# Let's change the output handle of `user_input_chunk`\n", "# FROM:\n", "# user_input_chunk.handle(\"user_input\").connect_to(assistant_reply_chunk.handle(\"user_input\"))\n", "# TO:\n", "(\n", "    user_input_chunk.handle(\"user_input\")\n", "        .if_condition(lambda data: data == \"#exit\").connect_to(goodbye_chunk)\n", "        .else_condition().connect_to(assistant_reply_chunk.handle(\"user_input\"))\n", ")\n", "user_input_chunk.handle(\"user_input\").connect_to(update_chat_history_chunk.handle(\"user_input\"))\n", "assistant_reply_chunk.handle(\"assistant_reply\").connect_to(update_chat_history_chunk.handle(\"assistant_reply\"))\n", "update_chat_history_chunk.connect_to(user_input_chunk)\n", "\n", "# Step 3. Start Workflow\n", "workflow.start()"]}, {"cell_type": "markdown", "metadata": {"id": "IT3pSaO2NgkG"}, "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}