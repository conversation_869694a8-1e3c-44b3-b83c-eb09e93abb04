@echo off
chcp 65001 >nul
echo ==========================================
echo 图片HTTP服务器启动脚本
echo ==========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo 检查依赖包...
pip show fastapi >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 选择启动模式:
echo 1. 开发环境 (127.0.0.1:23333)
echo 2. 生产环境 (**************:23333)
echo 3. 测试服务器
echo.
set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 启动开发环境服务器...
    python start_server.py
) else if "%choice%"=="2" (
    echo 启动生产环境服务器...
    python production_start.py
) else if "%choice%"=="3" (
    echo 运行服务器测试...
    python test_server.py
) else (
    echo 无效选择，启动开发环境...
    python start_server.py
)

echo.
echo 服务器已停止
pause
