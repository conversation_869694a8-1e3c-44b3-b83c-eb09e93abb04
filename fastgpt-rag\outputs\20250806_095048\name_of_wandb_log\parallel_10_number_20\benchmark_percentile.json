[{"Percentiles": "10%", "TTFT (s)": 13.0522, "ITL (s)": 0.0145, "TPOT (s)": 0.026, "Latency (s)": 48.8498, "Input tokens": 21, "Output tokens": 489, "Output (tok/s)": 5.6259, "Total (tok/s)": 5.8864}, {"Percentiles": "25%", "TTFT (s)": 54.8486, "ITL (s)": 0.0232, "TPOT (s)": 0.0261, "Latency (s)": 84.7521, "Input tokens": 26, "Output tokens": 688, "Output (tok/s)": 6.744, "Total (tok/s)": 7.1687}, {"Percentiles": "50%", "TTFT (s)": 84.0105, "ITL (s)": 0.026, "TPOT (s)": 0.0266, "Latency (s)": 99.1897, "Input tokens": 28, "Output tokens": 824, "Output (tok/s)": 9.0942, "Total (tok/s)": 9.4892}, {"Percentiles": "66%", "TTFT (s)": 88.4926, "ITL (s)": 0.0274, "TPOT (s)": 0.0268, "Latency (s)": 107.0449, "Input tokens": 31, "Output tokens": 928, "Output (tok/s)": 13.3448, "Total (tok/s)": 13.687}, {"Percentiles": "75%", "TTFT (s)": 90.7467, "ITL (s)": 0.0284, "TPOT (s)": 0.0269, "Latency (s)": 114.7072, "Input tokens": 34, "Output tokens": 1127, "Output (tok/s)": 14.759, "Total (tok/s)": 15.4348}, {"Percentiles": "80%", "TTFT (s)": 92.8889, "ITL (s)": 0.0294, "TPOT (s)": 0.0269, "Latency (s)": 118.2659, "Input tokens": 37, "Output tokens": 1131, "Output (tok/s)": 17.8542, "Total (tok/s)": 18.0808}, {"Percentiles": "90%", "TTFT (s)": 104.5521, "ITL (s)": 0.0335, "TPOT (s)": 0.0272, "Latency (s)": 126.2934, "Input tokens": 41, "Output tokens": 1806, "Output (tok/s)": 36.9705, "Total (tok/s)": 37.4413}, {"Percentiles": "95%", "TTFT (s)": 105.413, "ITL (s)": 0.0405, "TPOT (s)": 0.0278, "Latency (s)": 127.8183, "Input tokens": 45, "Output tokens": 2048, "Output (tok/s)": 37.7243, "Total (tok/s)": 39.2672}, {"Percentiles": "98%", "TTFT (s)": 105.413, "ITL (s)": 0.0549, "TPOT (s)": 0.0278, "Latency (s)": 127.8183, "Input tokens": 45, "Output tokens": 2048, "Output (tok/s)": 37.7243, "Total (tok/s)": 39.2672}, {"Percentiles": "99%", "TTFT (s)": 105.413, "ITL (s)": 0.0763, "TPOT (s)": 0.0278, "Latency (s)": 127.8183, "Input tokens": 45, "Output tokens": 2048, "Output (tok/s)": 37.7243, "Total (tok/s)": 39.2672}]