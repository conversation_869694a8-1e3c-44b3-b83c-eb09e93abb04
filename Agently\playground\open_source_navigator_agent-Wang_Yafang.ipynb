{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/open_source_navigator_agent-<PERSON>_<PERSON>g.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "dAzfqHDCAXZe"}, "source": ["# Open Source Navigator Agent by <PERSON>"]}, {"cell_type": "markdown", "metadata": {"id": "kyLFmv_l-aIx"}, "source": ["## Demo Description\n", "\n", "**Author:**\n", "\n", "[@<PERSON>fan<PERSON>](https://github.com/yafangwang9) 王雅芳\n", "\n", "<PERSON>fang major in Electronic Information Engineering, with experience in target detection systems, sentiment analysis of minority languages. She is the operation manager of the Machine Heart open-source technology community.\n", "\n", "电子信息工程专业，做过目标检测系统、小语种情感识别，机器之心开源技术社区运营\n", "\n", "**Showcase Description:**\n", "\n", "OpenSource Navigator Agent is an intelligent assistant agents system I build for daily community operation and management, helping me interaction and communication with developers more efficiently.\n", "\n", "This system have 3 major agent:\n", "\n", "- **Tech Query Assistant**: When encountering technical issues in popular fields such as LLM, RAG, Agent, etc., it can automatically search Google and quickly find relevant reference content for the problems.\n", "\n", "- **Open Source Highlight Miner Assistant**: It provides a GitHub open-source project link for popular open-source projects, capable of extracting the highlights of the project and organizing them into content that developers can easily understand, thereby attracting developers' attention.\n", "\n", "- **Paper Retrieval Assistant**: In academic research, when there is a need to address specific research questions from papers, it can retrieve local paper PDFs and provide concise and clear answers.\n", "\n", "<img width=\"800\" alt=\"image\" src=\"https://github.com/Maplemx/Agently/assets/4413155/74e9a402-e352-4e08-bfce-759d1f1ecd91\">\n", "\n", "**Introduction Video**:\n", "\n", "Bilibili: https://www.bilibili.com/video/BV1PJ4m1v74g"]}, {"cell_type": "markdown", "metadata": {"id": "nRsfMu4lAJZF"}, "source": ["## Showcase Code"]}, {"cell_type": "markdown", "source": ["### Package Installation"], "metadata": {"id": "-egpOgKpTvl-"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nsst7pOAANlr"}, "outputs": [], "source": ["!pip install -q -U Agently langchain beautifulsoup4 PyPDF2"]}, {"cell_type": "markdown", "source": ["### Showcase Code"], "metadata": {"id": "eeM97ZUGitlr"}}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "fc9640f0-41fd-4bb2-93f6-ab3d72ce8ace"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Choose an agent [0: TechQuery; 1: ContentCreator; 2: RetrieveAssistant]: 1\n", "[User]: https://github.com/Maplemx/Agently\n", "[ContentCreator]:  Agently is an AI agent development framework designed to enable developers to quickly build native applications incorporating AI agents. This framework simplifies the process of integrating AI agents into business code, allowing developers to focus on logic rather than catering to language models. The Agently framework operates on the principle of \"Ask-Get Response,\" replacing traditional programming methods with a more natural language approach to problem-solving. Notably, Agently 3.0 introduces a plugin-to-enhance design, which simplifies the development of various agent components and allows for easier management and updates. Plugin developers can also share and install these components independently, facilitating a more modular and efficient development process. For a deeper dive into Agently, interested parties can explore the provided documentation and resources. [Agently](https://github.com/Maplemx/Agently) is an open-source project that welcomes contributions and feedback from the community.\n", "[User]: 翻译成中文\n", "[ContentCreator]:  Agently是一个帮助开发者快速构建AI代理原生应用程序的开发框架。该项目简化了将AI代理集成到业务代码中的过程，使开发者可以专注于逻辑而非迎合语言模型。Agently框架采用“提问-获取响应”的方式，取代了传统的“定义问题-编程-编码实现”的模式。在Agently框架中，开发者可以通过极少的代码创建AI代理实例，并以类似于调用函数的方式与其交互。\n", "\n", "该项目强调了AI代理在解决业务逻辑方面的优势，以及与传统软件开发方式的差异。Agently框架提供了与AI代理实例轻松交互的方式，使得应用模块的开发快速而简单。此外，Agently 3.0采用了插件增强设计，不仅解决了构建全新代理的问题，还让组件开发者可以专注于各自关心的目标，简化了代码并提高了开发效率。\n", "\n", "开发者可以通过 pip 安装 Agently 包，并开始使用。项目还提供了示例代码、Demo视频、Colab文档等资源，帮助开发者深入了解如何在不同领域构建AI代理。Agently团队鼓励开发者参与贡献，并通过GitHub仓库的 Issues 功能提交意见和建议。\n", "\n", "如果喜欢这个项目，可以通过[GitHub](https://github.com/Maplemx/Agently)进行星标，也可以通过电子邮件、Discord群组或微信扫码加入社区。该项目欢迎社区的贡献和反馈，致力于推动AI代理开发的发展。\n", "[User]: exit\n"]}], "source": ["import Agently\n", "import requests\n", "from bs4 import BeautifulSoup\n", "import json\n", "import os,json,yaml\n", "from PyPDF2 import PdfReader\n", "from langchain.text_splitter import CharacterTextSplitter\n", "from langchain_community.vectorstores import FAISS\n", "from langchain.chains.question_answering import load_qa_chain\n", "from langchain.llms.base import LLM\n", "from typing import List, Optional\n", "from langchain_community.embeddings import JinaEmbeddings\n", "\n", "# Settings\n", "GLM_API_KEY = \"\"# add your GLM api key\n", "GOOGLE_SEARCH_API_KEY = \"\"\n", "proxies = {'https': None, 'http': None} # add your http\n", "headers = {'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36'}\n", "\n", "# Main Code\n", "def get_opensource_info(url):\n", "    response = requests.get(url, headers=headers,proxies=proxies)\n", "    if response.status_code == 200:\n", "        readme_url = url + \"/blob/main/README.md\"\n", "        readme_response = requests.get(readme_url, headers=headers, proxies=proxies)\n", "        soup = BeautifulSoup(readme_response.content,'html.parser')\n", "        paragraphs = soup.find_all('p')\n", "        texts = [p.get_text() for p in paragraphs ]\n", "        result = ' '.join(texts)\n", "        return result\n", "    else:\n", "        print(\"开源项目信息获取失败\")\n", "\n", "def google_search(query):\n", "    url = 'https://google-api31.p.rapidapi.com/websearch'\n", "    headers = {\n", "        'content-type': 'application/json',\n", "        'X-RapidAPI-Key': GOOGLE_SEARCH_API_KEY,\n", "        'X-RapidAPI-Host': 'google-api31.p.rapidapi.com'\n", "    }\n", "    data = {\n", "        'text': query,\n", "        'safesearch': 'off',\n", "        'timelimit': '',\n", "        'region': 'wt-wt',\n", "        'max_results': 3  # 你可以根据需要调整结果的数量\n", "    }\n", "    response = requests.post(url, headers=headers, data=json.dumps(data))\n", "    if response.status_code == 200:\n", "        results = response.json()['result']\n", "        formatted_results = ''\n", "        for item in results:\n", "            formatted_results = formatted_results+item['title'] +'\\n'+ item['href'] +'\\n'+ item['body'] +'\\n'+'\\n'\n", "        return formatted_results\n", "    else:\n", "        return f\"Error: {response.status_code}\"\n", "\n", "\n", "def process_text(text):\n", "    # Split the text into chunks using Langchain's CharacterTextSplitter\n", "    text_splitter = CharacterTextSplitter(\n", "        separator=\"\\n\",\n", "        chunk_size=1000,\n", "        chunk_overlap=200,\n", "        length_function=len\n", "    )\n", "    chunks = text_splitter.split_text(text)\n", "\n", "    # Convert the chunks of text into embeddings to form a knowledge base\n", "    embeddings = <PERSON><PERSON>s(\n", "    jina_api_key=\"\", model_name=\"jina-embeddings-v2-base-en\") # add jina_api_key\n", "    knowledgeBase = FAISS.from_texts(chunks, embeddings)\n", "    return knowledgeBase\n", "\n", "def retrieve_pdf(query):\n", "    pdf_reader = PdfReader('2401.03462.pdf')\n", "    text = \"\"\n", "    for page in pdf_reader.pages:\n", "        text += page.extract_text()\n", "        # Create the knowledge base object\n", "    knowledgeBase = process_text(text)\n", "    docs = knowledgeBase.similarity_search(query)\n", "    return docs\n", "\n", "\n", "agent_factory = Agently.AgentFactory(is_debug = False)\n", "agent_factory\\\n", "    .set_settings(\"current_model\", \"ZhipuAI\")\\\n", "    .set_settings(\"model.ZhipuAI.auth\", { \"api_key\": GLM_API_KEY })\n", "\n", "\n", "# 注册get_opensource_info到全局工具管理器\n", "Agently.global_tool_manager.register(\n", "    tool_name=\"get_opensource_info\",\n", "    desc=\"Get information about an open-source project from GitHub\",\n", "    args={\"url\": \"string\"},\n", "    func=get_opensource_info\n", ")\n", "\n", "# 注册google_search到全局工具管理器\n", "Agently.global_tool_manager.register(\n", "    tool_name=\"google_search\",\n", "    desc=\"Perform a Google search and return formatted results\",\n", "    args={\"query\": \"string\"},\n", "    func=google_search\n", ")\n", "\n", "# 注册get_opensource_info到全局工具管理器\n", "Agently.global_tool_manager.register(\n", "    tool_name=\"retrieve_pdf\",\n", "    desc=\"Get information about an open-source project from GitHub\",\n", "    args={\"url\": \"string\"},\n", "    func=retrieve_pdf\n", ")\n", "\n", "\n", "# 创建各专业领域代理\n", "tech_query_agent = (\n", "    agent_factory.create_agent()\n", "    .use_public_tools([\"google_search\"])  # 使用google_search工具\n", "    .set_role(\"role\", \"在今后的对话中，您将作为我的技术问题智能助理，你会进行google_search，为大型语言模型（LLM）、知识检索增强RAG（Retrieval-Augmented Generation）、Agent等LLM热门应用领域的技术问题提供解决方案。\")\n", ")\n", "\n", "content_creator_agent = (\n", "    agent_factory.create_agent()\n", "    .use_public_tools([\"get_opensource_info\"])  # 使用我们刚注册的工具\n", "    .set_role(\"role\", \"在今后的对话中，您将作为我的人工智能写作助理，请根据我给出github链接的项目要点编写成一段摘要不要分段。在每次编写摘要时，请你参考下面的示例：阿卜杜拉国王科技大学开发的“角色扮演”（Role-Playing）的多Agent框架，可通过角色扮演提高Agent能力。CAMEL通过多个Agent之间进行对话和合作来完成分配的任务。具体来说，该框架采用两个Agent，并让Agent相互交谈，并使用启示式提示（Inception Prompt）来引导聊天Agent完成任务，并与人类意图保持一致。Agent会被赋予不同的角色，每个Agent都有自己的个性，并拥有相应角色的专业知识背景，从而利用其知识来找到满足共同任务的解决方案。\")\n", ")\n", "\n", "\n", "paper_retrieve_agent = (\n", "    agent_factory.create_agent()\n", "    .set_role(\"role\", \"在今后的对话中，您将作为我的论文检索助理，能够进行检索并解答我的问题\")\n", ")\n", "\n", "\n", "# 设置代理池\n", "agent_pool = {\n", "    \"TechQuery\": tech_query_agent,\n", "    \"ContentCreator\": content_creator_agent,\n", "    \"RetrieveAssistant\": paper_retrieve_agent\n", "}\n", "\n", "# ---------------------\n", "\n", "# 启动演示环境\n", "user_choice = None\n", "while user_choice not in (\"0\", \"1\", \"2\"):\n", "    user_choice = input(\"Choose an agent [0: TechQuery; 1: ContentCreator; 2: RetrieveAssistant]: \")\n", "\n", "agent_names = [\"TechQuery\", \"ContentCreator\", \"RetrieveAssistant\"]\n", "chosen_agent_name = agent_names[int(user_choice)]\n", "chosen_agent = agent_pool[chosen_agent_name]\n", "user_input = input(\"[User]: \")\n", "chosen_agent.active_session()\n", "\n", "response = (\n", "    chosen_agent\n", "    .user_info(\"An anonymous user\")\n", "    .input({\"input\": user_input})\n", "    .instruct(\n", "        \"Introduce yourself and welcome user first, \\\n", "        then answer user's question {input}, \\\n", "        then tell user he/she can exit this dialogue by input 'exit' anytime.\"\n", "    )\n", "    .start()\n", ")\n", "\n", "print(f\"[{chosen_agent_name}]: \", response)\n", "\n", "while True:\n", "    user_input = input(\"[User]: \")\n", "    if user_input == \"exit\":\n", "        break\n", "    response = (\n", "        chosen_agent\n", "        .input(user_input)\n", "        .start()\n", "    )\n", "    print(f\"[{chosen_agent_name}]: \", response)"]}, {"cell_type": "markdown", "metadata": {"id": "IT3pSaO2NgkG"}, "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}