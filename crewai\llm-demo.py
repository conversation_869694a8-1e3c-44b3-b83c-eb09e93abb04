from crewai import Agent, LLM
from crewai_tools import SerperDevTool

# Initialize a search tool
search_tool = SerperDevTool()

llm = LLM(
    model="deepseek-v3-250324",
    temperature=0.7,
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key="d210f6a1-7eff-4dd2-8778-ff3db4f8c54d"
)

agent = Agent(
    role='Customized LLM Expert',
    goal='Provide tailored responses',
    backstory="An AI assistant with custom LLM settings.",
    # tools=[search_tool],
    llm=llm
)
print(agent)