{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/writing_ad_copies_according_image.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Writing Ad Copies according Image"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** English\n", "\n", "**Agent Components:** None\n", "\n", "**Description:**\n", "\n", "This case shows how to let Agently agents to write ad copies according an image url. You can use a vision model to drive one agent to observe and use another language model to write ad copies according the observation. It's an agents' teamwork and it works perfect! You can input image URL and the ad copies examples you want to imitate and see how the job is done.\n", "\n", "⚠️: This show case required GPT-4-Vision API request authority to observe image.\n", "\n", "在本次演示中，我们展示了如何让Agent通过图片链接完成广告文案的生成。你可以使用一个被视觉模型驱动的Agent来对图片进行观察，并将观察结果告诉另一个文案仿写Agent，完成一次Agent之间的团队协作。本案例支持你自己输入想要观察的图片链接，以及给出想要仿写的文案样本，然后观察Agent们是如何帮助你完成这个任务的。\n", "\n", "⚠️: 这个演示案例需要拥有GPT-4-Vision模型的API请求权限，来使用GPT-4-V模型进行图片观测"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Step 1: Install Packages"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install Agently"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Demo Code"], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "03330c86-afca-4c33-8fa5-daca675e334e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Input image URL: https://img.alicdn.com/imgextra/i4/2212695910252/O1CN01LkktFc1DjShdDLcY6_!!2212695910252.jpg\n", "Add ad copy example (input nothing to quit): Upgrade your wardrobe with our Men's Casual Long Sleeve Shirts. Versatile, comfortable, and always in style. Make a statement with ease! 👕  🛒SHOP NOW👉 https://Dozint.com/shirts\n", "Add ad copy example (input nothing to quit): 🛍️ Transform your wardrobe! Embrace endless styling possibilities with our shirt extenders—a fashion game-changer for any ensemble.  🛒Get it👉 https://dozoly.com/shirt-extenders\n", "Add ad copy example (input nothing to quit): <PERSON>'s <PERSON><PERSON><PERSON> Shirt - <PERSON>'s Original Cayenne Hot Sauce T-Shirt #Tshirt #Shirt\n", "Add ad copy example (input nothing to quit): Summer Shirt zipo Size M-3xl Bei:20000 Simu:0692608572 #sokoletu #nipedili\n", "Add ad copy example (input nothing to quit): \n", "[Observation]\n", "```json\n", "{\n", "\t\"item_name\": \"Long Puffer Jacket\",\n", "\t\"item_features\": \"Knee-length, black, insulated, hooded, front button and zipper closure\",\n", "\t\"item_target_audience\": \"Young adults and middle-aged individuals seeking warmth and style in colder climates\",\n", "\t\"item_selling_points\": \"Provides excellent insulation against the cold, versatile for casual or slightly dressier occasions, easy to style with various outfits, durable material\"\n", "}\n", "```\n", "------\n", "[<PERSON>]\n", "[Version 1]\n", " Stay warm and stylish this winter with our Long Puffer Jacket! Knee-length, black, and insulated, it's perfect for young adults and middle-aged individuals seeking warmth and style in colder climates. Shop now!\n", "[Version 2]\n", " Upgrade your winter wardrobe with our Long Puffer Jacket! With excellent insulation against the cold, it's versatile for casual or slightly dressier occasions. Plus, the durable material ensures it will last for seasons to come. Order now!\n", "[Version 3]\n", " Don't let the cold weather hold you back from looking your best! Our Long Puffer Jacket provides excellent insulation against the cold, while also being easy to style with various outfits. Stay warm and stylish all winter long! Buy now!\n"]}], "source": ["import Agently\n", "\n", "agent_factory = Agently.AgentFactory()\n", "\n", "# Input\n", "image_url = input(\"Input image URL: \")\n", "ad_copy_examples = []\n", "while True:\n", "    ad_copy_example = input(\"Add ad copy example (input nothing to quit): \")\n", "    if ad_copy_example == \"\":\n", "        break\n", "    ad_copy_examples.append(ad_copy_example)\n", "\n", "# Step 1: create two agent for different tasks\n", "observe_agent = agent_factory.create_agent()\\\n", "    .set_settings(\"current_mode\", \"OpenAI\")\\\n", "    .set_settings(\"model.OpenAI.auth\", { \"api_key\": \"\" })\\\n", "    .set_settings(\"model.OpenAI.options\", { \"model\": \"gpt-4-vision-preview\" })\n", "\n", "ad_copy_writing_agent = agent_factory.create_agent()\\\n", "    .set_settings(\"current_model\", \"ERNIE\")\\\n", "    .set_settings(\"model.ERNIE.auth\", { \"aistudio\": \"\" })\n", "\n", "# Step 2: observe agent analyses the image\n", "print(\"[Observation]\")\n", "observe_result = observe_agent\\\n", "    .file(image_url)\\\n", "    .output({\n", "        \"item_name\": (\"String\", \"the name of the item in the image\"),\n", "        \"item_features\": (\"String\", \"key features of this item\"),\n", "        \"item_target_audience\": (\"String\", \"which audience will buy this item most likely?\"),\n", "        \"item_selling_points\": (\"String\", \"what're the selling points you want to introduce to {item_target_audience}?\"),\n", "    })\\\n", "    .on_delta(lambda data: print(data, end=\"\"))\\\n", "    .start(\"vision\")\n", "print(\"\\n------\")\n", "\n", "# Step 3: ad copy writing agent write ad copy according examples\n", "ad_copy_result = ad_copy_writing_agent\\\n", "    .input(observe_result)\\\n", "    .info(\"ad_copy_examples\", ad_copy_examples)\\\n", "    .output({\n", "        \"ad_copies\": [(\"String\", \"at least 3 version of your final ad copies.\")]\n", "    })\\\n", "    .start()\n", "print(\"[<PERSON> Co<PERSON>]\")\n", "counter = 1\n", "for ad_copy in ad_copy_result[\"ad_copies\"]:\n", "    print(f\"[Version { str(counter) }]\\n\", ad_copy)\n", "    counter += 1"]}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}