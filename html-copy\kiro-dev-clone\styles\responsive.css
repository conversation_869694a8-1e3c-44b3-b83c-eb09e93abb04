/* Responsive styles for different screen sizes */

/* Mobile First Approach */

/* Mobile Styles */
@media (max-width: 767px) {
  /* Hero Section Mobile */
  .hero {
    padding: var(--spacing-16) 0 var(--spacing-12);
  }

  .hero__title {
    font-size: var(--font-size-4xl);
  }

  .hero__subtitle {
    font-size: var(--font-size-lg);
  }

  .hero__actions {
    flex-direction: column;
    align-items: center;
  }

  .hero__actions .btn {
    width: 100%;
    max-width: 280px;
  }

  .hero__image-placeholder {
    height: 200px;
  }

  /* Mobile Navigation */
  .nav__menu {
    position: fixed;
    top: 4rem;
    left: 0;
    right: 0;
    background-color: var(--color-white);
    border-bottom: 1px solid var(--color-border);
    flex-direction: column;
    align-items: stretch;
    padding: var(--spacing-4);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-lg);
  }

  .nav__menu.nav__menu--open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav__list {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
  }

  .nav__link {
    padding: var(--spacing-3) var(--spacing-4);
    text-align: center;
  }

  .nav__actions {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .nav__toggle {
    display: flex;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Tablet styles */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-6);
  }

  .nav__content {
    padding: 0 var(--spacing-6);
  }

  .nav__list {
    gap: var(--spacing-8);
  }

  .nav__actions {
    gap: var(--spacing-4);
  }

  /* Hero Section Tablet */
  .hero__content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    text-align: left;
  }

  .hero__text {
    text-align: left;
  }

  .hero__actions {
    justify-content: flex-start;
  }

  .hero__subtitle {
    margin-left: 0;
    margin-right: 0;
  }
}

/* Desktop styles */
@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-8);
  }

  .nav__content {
    padding: 0 var(--spacing-8);
  }

  .nav__menu {
    gap: var(--spacing-12);
  }
}

/* Large desktop styles */
@media (min-width: 1440px) {
  .container {
    max-width: 1400px;
  }
}