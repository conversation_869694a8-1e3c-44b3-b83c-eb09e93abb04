from agentscope.agents import DialogAgent, UserAgent
import agentscope
from agentscope.models import OpenAIChatWrapper

# 加载模型配置
agentscope.init(
    model_configs=[
        {
            "config_name": "doubao",
            "model_type": "openai_chat",
            "model_name": "doubao-1-5-pro-32k-250115",
            "api_key": "5dd64d02-d0b4-48fd-91f0-d4a91f359456",
            "client_args": {"base_url": "https://ark.cn-beijing.volces.com/api/v3/"},
            "generate_args": {"temperature": 0.5},
        }
    ]
)

# 创建一个对话智能体和一个用户智能体
dialog_agent = DialogAgent(
    name="Friday", model_config_name="doubao", sys_prompt="你是一个名为Friday的助手"
)
user_agent = UserAgent(name="user")

# 显式构建工作流程/对话
x = None
while x is None or x.content != "exit":
    x = dialog_agent(x)
    x = user_agent(x)
