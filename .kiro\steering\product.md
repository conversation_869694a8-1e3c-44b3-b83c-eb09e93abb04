# Product Overview

This repository contains AI agent development projects and experiments, primarily focused on:

## Core Components

**Agently Framework**: A Python-based AI agent development framework that enables developers to build AI agent native applications quickly. The framework emphasizes plugin-based architecture for enhancing agent capabilities without rebuilding entire agents.

**CrewAI Integration**: Multi-agent collaboration examples using CrewAI framework for business planning, data analysis, and code generation tasks.

**MCP (Model Context Protocol) Testing**: Experimental implementations and testing of MCP servers and clients for enhanced AI model interactions.

**RAGFlow Integration**: Local LLM integration examples using RAGFlow for SQL query generation and conversational AI.

## Key Features

- Plugin-based agent enhancement system
- Multi-model support (OpenAI, local models, custom endpoints)
- Natural language to code/SQL generation
- Multi-agent collaboration workflows
- MCP protocol implementation and testing

## Target Use Cases

- AI agent application development
- Business process automation
- Data analysis and SQL generation
- Multi-agent collaborative systems
- LLM integration and testing