# 海康摄像头帧捕获程序

这个程序可以从海康威视RTSP摄像头每隔指定时间保存一帧图像。

## 文件说明

- `hikvision_capture.py` - 主程序
- `camera_config.py` - 配置文件
- `rtsp_frame_capture.py` - 简化版程序

## 使用步骤

### 1. 安装依赖
```bash
pip install opencv-python
```

### 2. 修改配置
编辑 `camera_config.py` 文件，修改以下参数：

```python
CAMERA_CONFIG = {
    "ip": "*************",      # 改为你的摄像头IP
    "username": "admin",        # 改为你的用户名
    "password": "password",     # 改为你的密码
    "channel": "101",           # 通道号：101=主码流，201=子码流
}

CAPTURE_CONFIG = {
    "interval": 5,              # 保存间隔（秒）
    "output_dir": "captured_frames",  # 输出目录
    "show_preview": True,       # 是否显示预览窗口
}
```

### 3. 运行程序
```bash
python hikvision_capture.py
```

## 常见问题

### 1. 连接失败
- 检查摄像头IP地址是否正确
- 检查用户名和密码
- 确保摄像头开启了RTSP服务
- 检查网络连接

### 2. RTSP地址格式
海康摄像头常用格式：
- 主码流：`rtsp://用户名:密码@IP:554/Streaming/Channels/101`
- 子码流：`rtsp://用户名:密码@IP:554/Streaming/Channels/201`

### 3. 通道号说明
- 101：主码流（高清，文件较大）
- 201：子码流（标清，文件较小）
- 301：第三码流（如果支持）

## 程序特性

- ✅ 自动重连机制
- ✅ 时间戳文件命名
- ✅ 实时预览（可选）
- ✅ 优雅退出（按q或Ctrl+C）
- ✅ 文件大小显示
- ✅ 连接状态提示

## 输出文件格式

保存的图片文件名格式：`frame_0001_2025_01_15_14_30_25.jpg`
- frame_0001：帧序号
- 2025_01_15_14_30_25：时间戳（年_月_日_时_分_秒）