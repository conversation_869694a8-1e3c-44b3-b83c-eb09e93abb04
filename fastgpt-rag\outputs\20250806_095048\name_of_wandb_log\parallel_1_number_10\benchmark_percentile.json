[{"Percentiles": "10%", "TTFT (s)": 0.1295, "ITL (s)": 0.0075, "TPOT (s)": 0.0112, "Latency (s)": 7.6873, "Input tokens": 20, "Output tokens": 683, "Output (tok/s)": 83.5179, "Total (tok/s)": 85.4806}, {"Percentiles": "25%", "TTFT (s)": 0.1426, "ITL (s)": 0.0101, "TPOT (s)": 0.0115, "Latency (s)": 8.214, "Input tokens": 23, "Output tokens": 703, "Output (tok/s)": 85.2541, "Total (tok/s)": 86.9834}, {"Percentiles": "50%", "TTFT (s)": 0.1519, "ITL (s)": 0.0115, "TPOT (s)": 0.0115, "Latency (s)": 10.2788, "Input tokens": 29, "Output tokens": 882, "Output (tok/s)": 85.8076, "Total (tok/s)": 88.419}, {"Percentiles": "66%", "TTFT (s)": 0.163, "ITL (s)": 0.0125, "TPOT (s)": 0.0115, "Latency (s)": 11.5654, "Input tokens": 31, "Output tokens": 986, "Output (tok/s)": 85.9415, "Total (tok/s)": 89.4072}, {"Percentiles": "75%", "TTFT (s)": 0.1647, "ITL (s)": 0.0132, "TPOT (s)": 0.0116, "Latency (s)": 14.7753, "Input tokens": 32, "Output tokens": 1234, "Output (tok/s)": 86.7981, "Total (tok/s)": 89.4811}, {"Percentiles": "80%", "TTFT (s)": 0.1665, "ITL (s)": 0.0138, "TPOT (s)": 0.0119, "Latency (s)": 16.6392, "Input tokens": 37, "Output tokens": 1430, "Output (tok/s)": 87.7281, "Total (tok/s)": 90.7713}, {"Percentiles": "90%", "TTFT (s)": 0.1667, "ITL (s)": 0.0164, "TPOT (s)": 0.012, "Latency (s)": 23.595, "Input tokens": 41, "Output tokens": 2048, "Output (tok/s)": 88.8478, "Total (tok/s)": 94.1813}, {"Percentiles": "95%", "TTFT (s)": 0.1667, "ITL (s)": 0.0201, "TPOT (s)": 0.012, "Latency (s)": 23.595, "Input tokens": 41, "Output tokens": 2048, "Output (tok/s)": 88.8478, "Total (tok/s)": 94.1813}, {"Percentiles": "98%", "TTFT (s)": 0.1667, "ITL (s)": 0.0255, "TPOT (s)": 0.012, "Latency (s)": 23.595, "Input tokens": 41, "Output tokens": 2048, "Output (tok/s)": 88.8478, "Total (tok/s)": 94.1813}, {"Percentiles": "99%", "TTFT (s)": 0.1667, "ITL (s)": 0.0316, "TPOT (s)": 0.012, "Latency (s)": 23.595, "Input tokens": 41, "Output tokens": 2048, "Output (tok/s)": 88.8478, "Total (tok/s)": 94.1813}]