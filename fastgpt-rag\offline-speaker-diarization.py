#!/usr/bin/env python3
# Copyright (c)  2024  Xiaomi Corporation

"""
此文件展示如何使用 sherpa-onnx Python API 进行
离线/非流式说话人分离。

使用方法：

步骤 1：下载说话人分割模型

请访问 https://github.com/k2-fsa/sherpa-onnx/releases/tag/speaker-segmentation-models
查看可用模型列表。以下是一个示例

  wget https://github.com/k2-fsa/sherpa-onnx/releases/download/speaker-segmentation-models/sherpa-onnx-pyannote-segmentation-3-0.tar.bz2
  tar xvf sherpa-onnx-pyannote-segmentation-3-0.tar.bz2
  rm sherpa-onnx-pyannote-segmentation-3-0.tar.bz2

步骤 2：下载说话人嵌入提取器模型

请访问 https://github.com/k2-fsa/sherpa-onnx/releases/tag/speaker-recongition-models
查看可用模型列表。以下是一个示例

  wget https://github.com/k2-fsa/sherpa-onnx/releases/download/speaker-recongition-models/3dspeaker_speech_eres2net_base_sv_zh-cn_3dspeaker_16k.onnx

步骤 3：下载测试音频文件

请访问 https://github.com/k2-fsa/sherpa-onnx/releases/tag/speaker-segmentation-models
查看可用测试音频文件列表。以下是一个示例

  wget https://github.com/k2-fsa/sherpa-onnx/releases/download/speaker-segmentation-models/0-four-speakers-zh.wav

步骤 4：运行程序

    python3 ./python-api-examples/offline-speaker-diarization.py

"""
from pathlib import Path

import sherpa_onnx
import soundfile as sf


def init_speaker_diarization(num_speakers: int = -1, cluster_threshold: float = 0.5):
    """
    参数:
      num_speakers:
        如果您知道音频文件中的实际说话人数量，请指定它。
        否则，保持为 -1
      cluster_threshold:
        如果 num_speakers 为 -1，则此阈值用于聚类。
        较小的 cluster_threshold 会产生更多聚类，即更多说话人。
        较大的 cluster_threshold 会产生更少聚类，即更少说话人。
    """
    segmentation_model = "./sherpa-onnx-pyannote-segmentation-3-0/model.onnx"
    embedding_extractor_model = (
        "./3dspeaker_speech_eres2net_base_sv_zh-cn_3dspeaker_16k.onnx"
    )

    config = sherpa_onnx.OfflineSpeakerDiarizationConfig(
        segmentation=sherpa_onnx.OfflineSpeakerSegmentationModelConfig(
            pyannote=sherpa_onnx.OfflineSpeakerSegmentationPyannoteModelConfig(
                model=segmentation_model
            ),
        ),
        embedding=sherpa_onnx.SpeakerEmbeddingExtractorConfig(
            model=embedding_extractor_model
        ),
        clustering=sherpa_onnx.FastClusteringConfig(
            num_clusters=num_speakers, threshold=cluster_threshold
        ),
        min_duration_on=0.3,
        min_duration_off=0.5,
    )
    if not config.validate():
        raise RuntimeError("请检查您的配置并确保所有必需的文件都存在")

    return sherpa_onnx.OfflineSpeakerDiarization(config)


def progress_callback(num_processed_chunk: int, num_total_chunks: int) -> int:
    progress = num_processed_chunk / num_total_chunks * 100
    print(f"进度: {progress:.3f}%")
    return 0


def main():
    wave_filename = "./0-four-speakers-zh.wav"
    if not Path(wave_filename).is_file():
        raise RuntimeError(f"{wave_filename} 不存在")

    audio, sample_rate = sf.read(wave_filename, dtype="float32", always_2d=True)
    audio = audio[:, 0]  # 只使用第一个声道

    # 由于我们知道上述测试音频文件中有4个说话人，所以这里使用
    # num_speakers 4
    sd = init_speaker_diarization(num_speakers=4)
    if sample_rate != sd.sample_rate:
        raise RuntimeError(f"期望的采样率: {sd.sample_rate}，实际给定: {sample_rate}")

    show_progress = True

    if show_progress:
        result = sd.process(audio, callback=progress_callback).sort_by_start_time()
    else:
        result = sd.process(audio).sort_by_start_time()

    for r in result:
        print(f"{r.start:.3f} -- {r.end:.3f} speaker_{r.speaker:02}")
        #  print(r) # 这种方式更简单


if __name__ == "__main__":
    main()
