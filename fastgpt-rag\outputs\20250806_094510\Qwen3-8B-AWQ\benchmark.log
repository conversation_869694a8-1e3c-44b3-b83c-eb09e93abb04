2025-08-06 09:45:10,681 - evalscope - INFO - Starting benchmark with args: 
2025-08-06 09:45:10,682 - evalscope - INFO - {
    "model": "Qwen3-8B-AWQ",
    "model_id": "Qwen3-8B-AWQ",
    "attn_implementation": null,
    "api": "openai",
    "tokenizer_path": null,
    "port": 8877,
    "url": "http://127.0.0.1:8001/v1/chat/completions",
    "headers": {},
    "connect_timeout": 600,
    "read_timeout": 600,
    "api_key": null,
    "no_test_connection": false,
    "number": 20,
    "parallel": 5,
    "rate": -1,
    "sleep_interval": 5,
    "log_every_n_query": 10,
    "debug": false,
    "wandb_api_key": null,
    "swanlab_api_key": null,
    "name": null,
    "outputs_dir": "outputs\\20250806_094510\\Qwen3-8B-AWQ",
    "max_prompt_length": 9223372036854775807,
    "min_prompt_length": 0,
    "prefix_length": 0,
    "prompt": null,
    "query_template": null,
    "apply_chat_template": true,
    "image_width": 224,
    "image_height": 224,
    "image_format": "RGB",
    "image_num": 1,
    "dataset": "openqa",
    "dataset_path": null,
    "frequency_penalty": null,
    "repetition_penalty": null,
    "logprobs": null,
    "max_tokens": 2048,
    "min_tokens": null,
    "n_choices": null,
    "seed": 0,
    "stop": null,
    "stop_token_ids": null,
    "stream": true,
    "temperature": 0.0,
    "top_p": null,
    "top_k": null,
    "extra_args": {}
}
2025-08-06 09:45:12,722 - evalscope - ERROR - Error in process_request: Cannot connect to host 127.0.0.1:8001 ssl:default [Connect call failed ('127.0.0.1', 8001)]
2025-08-06 09:45:12,723 - evalscope - WARNING - Retrying...  <None> Cannot connect to host 127.0.0.1:8001 ssl:default [Connect call failed ('127.0.0.1', 8001)]
