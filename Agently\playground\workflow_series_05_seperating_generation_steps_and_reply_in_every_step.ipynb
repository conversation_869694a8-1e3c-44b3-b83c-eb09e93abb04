{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/workflow_series_05_seperating_generation_steps_and_reply_in_every_step.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "dAzfqHDCAXZe"}, "source": ["# Seperating Generation Steps and Reply to User in Every Step`#Agently_Workflow_Showcase_Series - 05`"]}, {"cell_type": "markdown", "metadata": {"id": "kyLFmv_l-aIx"}, "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Series Link**:\n", "\n", "[Last Document]: [04 - Draw a Workflow Graph to Help You Observe the Workflow](https://github.com/Maplemx/Agently/blob/main/playground/workflow_series_04_draw_a_workflow_graph.ipynb)\n", "\n", "**Description:**\n", "\n", "Now you have already been familiar with the concept ideas and the basic usages of **Agently Workflow**. From this showcase, we will dive deeper to discuss how to use workflow to solve problems in more complex scene.\n", "\n", "As we all know, LLMs' abilities show different between languages. Those models which trained by raw materials in English will perform better when asked questions in English.\n", "\n", "So maybe we can try translating user's inputs into English and get English response from LLMs then translate the English response into user's original language. To ensure the questions and answers in unison between different languages, we reply every step's result to user. That also means **user will get more than one replies step by step after inputing one question**.\n", "\n", "This showcase will demostrate how to do that with **Agently Workflow**.\n", "\n", "读到本文档的时候，你应该已经非常熟悉**Agently Workflow**的思想理念和基础用法了。从本案例开始，我们将更深度地讨论如何在更为复杂的实践场景中使用workflow去解决问题。\n", "\n", "如我们所知，在使用不同的语种和模型交互时，语言模型展现出的回答能力是不一样的。那些更多使用英文素材进行训练的模型，在处理英文问题时，给出的回答可能会更加优质。\n", "\n", "所以，或许我们可以尝试构建一个工作流，将用户输入的问题转化为英文问题提交给模型，在获取模型的英文回复后，再将这个回复翻译为用户输入问题时所用的语言。同时，为了保证工作流中，翻译前后内容的一致性，我们将在工作的每一个阶段都将模型的处理结果回复呈现给用户。也就是说，**当用户输入一个问题之后，他将获得多于一次的分阶段回复**。\n", "\n", "这个案例将会向您演示我们如何使用**Agently Workflow**编排工作流程来做到这一点。"]}, {"cell_type": "markdown", "metadata": {"id": "nRsfMu4lAJZF"}, "source": ["## Step 1: Install Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nsst7pOAANlr"}, "outputs": [], "source": ["!pip install -U -q Agently>=3.2.2 mermaid-python"]}, {"cell_type": "markdown", "metadata": {"id": "_-1gryYwASPM"}, "source": ["## Step 2: Workflow Showcase"]}, {"cell_type": "markdown", "source": ["### Workflow Code"], "metadata": {"id": "eeM97ZUGitlr"}}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "d0b75c95-d9ba-426d-feb1-9f77b8fa9d90"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Ready to start!\n"]}], "source": ["import Agently\n", "agent_factory = (\n", "        Agently.AgentFactory()\n", "        .set_settings(\"model.Google.auth.api_key\", \"\")\n", "        .set_settings(\"current_model\", \"Google\")\n", ")\n", "\n", "workflow = Agently.Workflow()\n", "agent = agent_factory.create_agent()\n", "\n", "# Step 1: Define Workflow Chunks\n", "## Start Chunk\n", "@workflow.chunk(\n", "    chunk_id=\"Start\",\n", "    type = \"Start\"\n", ")\n", "\n", "## User Input Chunk\n", "@workflow.chunk(chunk_id=\"User Input\")\n", "def user_input_executor(inputs, storage):\n", "    return input(\"[User]: \")\n", "\n", "## <PERSON><PERSON>\n", "@workflow.chunk(chunk_id=\"Reply\")\n", "def reply_executor(inputs, storage):\n", "    print(\">>> \", inputs[\"input\"])\n", "\n", "## Translate To Chunk\n", "@workflow.chunk(chunk_id=\"Translate To\")\n", "def translate_to_executor(inputs, storage):\n", "    result = (\n", "        agent\n", "            .input(inputs[\"input\"])\n", "            .output({\n", "                \"original_language\": (\"str\", \"natural language name of the content in {input}\"),\n", "                \"translation\": (\"str\", \"translate {input} into English\"),\n", "            })\n", "            .start()\n", "    )\n", "    # save original language of user input into storage\n", "    storage.set(\"original_language\", result[\"original_language\"])\n", "    return result[\"translation\"]\n", "\n", "## Answer Chunk\n", "@workflow.chunk(chunk_id=\"Answer\")\n", "def answer_executor(inputs, storage):\n", "    return (\n", "        agent\n", "            .input(inputs[\"input\"])\n", "            .start()\n", "    )\n", "\n", "## Translate From Chunk\n", "@workflow.chunk(chunk_id=\"Translate From\")\n", "def translate_from_executor(inputs, storage):\n", "    return (\n", "        agent\n", "            .input(inputs[\"input\"])\n", "            # load original language of user input from storage\n", "            .output(f\"translate {{input}} into { storage.get('original_language') }\")\n", "            .start()\n", "    )\n", "\n", "# Step 2: Connect Chunks\n", "## Main Workflow\n", "workflow.chunks[\"Start\"].connect_to(workflow.chunks[\"User Input\"])\n", "workflow.chunks[\"User Input\"].connect_to(workflow.chunks[\"Translate To\"])\n", "workflow.chunks[\"Translate To\"].connect_to(workflow.chunks[\"Answer\"])\n", "workflow.chunks[\"Answer\"].connect_to(workflow.chunks[\"Translate From\"])\n", "## Replies\n", "workflow.chunks[\"Translate To\"].connect_to(workflow.chunks[\"Reply\"])\n", "workflow.chunks[\"Answer\"].connect_to(workflow.chunks[\"Reply\"])\n", "workflow.chunks[\"Translate From\"].connect_to(workflow.chunks[\"Reply\"])\n", "\n", "print(\"Ready to start!\")"]}, {"cell_type": "markdown", "source": ["### Workflow Graph"], "metadata": {"id": "q69ONOvQ_xlG"}}, {"cell_type": "code", "source": ["from mermaid import Mermaid\n", "Mermaid(workflow.draw())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 168}, "id": "AvqAojhhGlHC", "outputId": "9a95d2e8-340d-41d5-fc70-c17d6bf3b857"}, "execution_count": 4, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<mermaid.mermaid.Mermaid at 0x7f45596bf1c0>"], "text/html": ["\n", "        <div class=\"mermaid-aaffc93a-7d84-467d-9efa-8d7a1b87e416\"></div>\n", "        <script type=\"module\">\n", "            import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10.1.0/+esm'\n", "            const graphDefinition = '%%{ init: { \"flowchart\": { \"curve\": \"linear\" }, \"theme\": \"neutral\" } }%%\\n%% Rendered By Agently %%\\nflowchart LR\\n    4a5ffb9f-2cdc-44a3-bd12-799041047794(\"Start\") -.-> |\"output -->-- input\"| 2cc6a44d-7256-497c-a6e0-bdec2091b29f(\"User Input\")\\n    2cc6a44d-7256-497c-a6e0-bdec2091b29f(\"User Input\") -.-> |\"output -->-- input\"| 961cca86-d6c8-4c3f-aa3d-2bd6353e021a(\"Translate To\")\\n    961cca86-d6c8-4c3f-aa3d-2bd6353e021a(\"Translate To\") -.-> |\"output -->-- input\"| c75db32b-e26b-463b-9fbe-0e7380cf9c6b(\"Answer\")\\n    c75db32b-e26b-463b-9fbe-0e7380cf9c6b(\"Answer\") -.-> |\"output -->-- input\"| 55ad8fe5-2a80-41f6-9430-3caf2ff8e612(\"Translate From\")\\n    961cca86-d6c8-4c3f-aa3d-2bd6353e021a(\"Translate To\") -.-> |\"output -->-- input\"| 99128dfa-d7fc-41d5-beff-0e4f53516403(\"Reply\")\\n    c75db32b-e26b-463b-9fbe-0e7380cf9c6b(\"Answer\") -.-> |\"output -->-- input\"| 99128dfa-d7fc-41d5-beff-0e4f53516403(\"Reply\")\\n    55ad8fe5-2a80-41f6-9430-3caf2ff8e612(\"Translate From\") -.-> |\"output -->-- input\"| 99128dfa-d7fc-41d5-beff-0e4f53516403(\"Reply\")';\n", "            const element = document.querySelector('.mermaid-aaffc93a-7d84-467d-9efa-8d7a1b87e416');\n", "            const { svg } = await mermaid.render('graphDiv-aaffc93a-7d84-467d-9efa-8d7a1b87e416', graphDefinition);\n", "            element.innerHTML = svg;\n", "        </script>\n", "        "]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "markdown", "source": ["### Start"], "metadata": {"id": "eaPIWU5FWLLQ"}}, {"cell_type": "code", "source": ["workflow.start()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2mxgZC3wWICK", "outputId": "0a5886d8-d1da-4a61-e7e1-25b0e45bdf23"}, "execution_count": 36, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[User]: 什么是工作流？\n", ">>>  What is a workflow?\n", ">>>  **Definition:**\n", "\n", "A workflow is a sequence of tasks or steps that define a specific business process or activity. It automates the flow of work, ensuring that each task is executed in the correct order and by the intended person or system.\n", "\n", "**Key Features:**\n", "\n", "* **Automation:** Workflows remove manual intervention by automatically triggering tasks based on pre-defined conditions.\n", "* **Sequence:** Tasks are arranged in a logical order, ensuring that each step is completed before the next one begins.\n", "* **Assignment:** Workflows assign tasks to specific individuals or departments based on roles or responsibilities.\n", "* **Tracking:** Progress can be tracked throughout the workflow, providing visibility and accountability.\n", "* **Approval:** Workflows can include approval processes to ensure that tasks meet certain standards or criteria.\n", "\n", "**Benefits of Workflows:**\n", "\n", "* **Increased efficiency:** Automating tasks reduces manual labor and streamlines processes.\n", "* **Improved accuracy:** Automated workflows eliminate errors caused by human intervention.\n", "* **Enhanced collaboration:** Clear task assignments and communication mechanisms promote team coordination.\n", "* **Increased transparency:** Tracking capabilities provide visibility into the status of tasks and the overall workflow.\n", "* **Cost reduction:** Automating tasks saves time and labor costs, leading to cost savings.\n", "\n", "**Types of Workflows:**\n", "\n", "* **Linear Workflows:** A simple sequence of steps without any branching or loops.\n", "* **Conditional Workflows:** Tasks are selected based on the outcome of previous steps or conditions.\n", "* **Iterative Workflows:** Allow for repeated execution of certain tasks or loops.\n", "* **Parallel Workflows:** Tasks can execute concurrently, reducing overall workflow time.\n", "* **Hybrid Workflows:** Combine elements from different workflow types.\n", "\n", "**Uses of Workflows:**\n", "\n", "Workflows are used in various industries and applications, including:\n", "\n", "* Customer onboarding\n", "* Project management\n", "* Invoice processing\n", "* Sales automation\n", "* Employee onboarding and offboarding\n", ">>>  **定义：**\n", "\n", "工作流是一系列定义特定业务流程或活动的任务或步骤。它自动执行工作流程，确保每个任务按照正确的顺序由指定的人员或系统执行。\n", "\n", "**主要特征：**\n", "\n", "* **自动化：**工作流自动触发基于预定义条件的任务，从而消除了手动干预。\n", "* **序列：**任务按逻辑顺序排列，确保每个步骤在开始下一个步骤之前完成。\n", "* **分配：**根据角色或职责，工作流将任务分配给特定个人或部门。\n", "* **追踪：**可在整个工作流程中追踪进度，提供可见性和问责制。\n", "* **审批：**工作流可以包括审批流程，以确保任务符合某些标准或准则。\n", "\n", "**工作流的优势：**\n", "\n", "* **效率提高：**自动化任务减少了人工操作，并简化了流程。\n", "* **准确性提高：**自动化的工作流消除了因人为干预而导致的错误。\n", "* **协作增强：**明确的任务分配和沟通机制促进了团队协调。\n", "* **透明性增强：**追踪功能提供了任务状态和整体工作流的可见性。\n", "* **成本降低：**自动化任务节省了时间和人工成本，从而降低了成本。\n", "\n", "**工作流类型：**\n", "\n", "* **线性工作流：**一个简单的步骤序列，没有分支或循环。\n", "* **条件工作流：**根据前一步骤或条件的结果选择任务。\n", "* **迭代工作流：**允许重复执行某些任务或循环。\n", "* **并行工作流：**任务可以同时执行，从而减少了总体工作流时间。\n", "* **混合工作流：**组合了不同工作流类型的元素。\n", "\n", "**工作流的用途：**\n", "\n", "工作流用于各种行业和应用，包括：\n", "\n", "* 客户入职\n", "* 项目管理\n", "* 发票处理\n", "* 销售自动化\n", "* 员工入职和离职\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "IT3pSaO2NgkG"}, "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}