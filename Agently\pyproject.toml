[project]
name = "agently"
version = "*******"
description = "[GenAI Application Development Framework] - 🚀 Build GenAI application quick and easy 💬 Easy to interact with GenAI agent in code using structure data and chained-calls syntax 🧩 Use Agently Workflow to manage complex GenAI working logic 🔀 Switch to any model without rewrite application code"
authors = [
    {name = "Agently Team",email = "<EMAIL>"}
]
license = {text = "Apache-2.0"}
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "httpx (>=0.28.1,<0.29.0)",
    "pyreadline3 (>=3.5.4,<4.0.0)",
    "openai (>=1.61.1,<2.0.0)",
    "litellm (>=1.61.0,<2.0.0)",
    "pyyaml (>=6.0.2,<7.0.0)",
    "json5 (>=0.10.0,<0.11.0)",
    "duckduckgo-search (>=7.3.2,<8.0.0)",
    "beautifulsoup4 (>=4.13.3,<5.0.0)",
    "requests (>=2.32.3,<3.0.0)",
    "mcp (>=1.6.0,<2.0.0)",
    "fastapi (>=0.115.12,<0.116.0)",
    "boto3 (>=1.33.0,<2.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
