2025-07-15 18:12:06 | INFO     | agentscope.manager._model:load_model_configs:138 - Load configs for model wrapper: my_config
2025-07-15 18:12:06 | INFO     | agentscope.service.service_toolkit:add_mcp_servers:465 - Added tool functions from MCP server `amap-amap-sse`: maps_direction_bicycling, maps_direction_driving, maps_direction_transit_integrated, maps_direction_walking, maps_distance, maps_geo, maps_regeocode, maps_ip_location, maps_schema_personal_map, maps_around_search, maps_search_detail, maps_text_search, maps_schema_navi, maps_schema_take_taxi, maps_weather.
2025-07-15 18:12:07 | WARNING  | agentscope.models.openai_model:__init__:117 - Fail to get max_length for doubao-1-5-pro-32k-250115: 'Model [doubao-1-5-pro-32k-250115] not found in OPENAI_MAX_LENGTH. The last updated date is 20231212'
Friday: 好的，我已经了解上述工具信息。请你详细描述问题，我会根据需求调用合适的工具进行解答。 
user: 上海到北京的行程
Friday: 
[
    {
        "type": "tool_use",
        "id": "call_qx40h2lurkidhmqe5c9vz75b",
        "name": "maps_geo",
        "input": {
            "address": "上海",
            "city": "上海"
        }
    }
]
2025-07-15 18:12:29 | INFO     | agentscope.service.mcp_manager:execute_tool:268 - Executing maps_geo...
system: Execute function maps_geo:
[{'type': 'text', 'text': '{"results":[{"country":"中国","province":"上海市","city":"上海市","citycode":"021","district":[],"street":[],"number":[],"adcode":"310000","location":"121.473667,31.230525","level":"省"}]}', 'annotations': None, 'meta': None}]
2025-07-15 18:12:29 | INFO     | agentscope.service.mcp_manager:cleanup:205 - Clean up MCP Server `amap-amap-sse` finished.
