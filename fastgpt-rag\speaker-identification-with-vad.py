#!/usr/bin/env python3

"""
此脚本展示如何使用Python API进行带有麦克风和VAD模型的说话人识别

使用方法：

(1) 准备一个包含说话人相关文件的文本文件。

文本文件中的每一行包含两列。第一列是说话人姓名，第二列包含该说话人的音频文件。

如果文本文件中同一个说话人有多个音频文件，那么这些文件的嵌入向量会被平均化。

示例文本文件如下：

    张三 /path/to/a.wav
    李四 /path/to/b.wav
    张三 /path/to/c.wav
    王五 /path/to/d.wav

每个音频文件应该只包含单声道；采样格式应该是int16_t；采样率可以是任意的。

(2) 下载用于计算说话人嵌入向量的模型

请访问
https://github.com/k2-fsa/sherpa-onnx/releases/tag/speaker-recongition-models
下载模型。示例如下：

    wget https://github.com/k2-fsa/sherpa-onnx/releases/download/speaker-recongition-models/wespeaker_zh_cnceleb_resnet34.onnx

注意：`zh`表示中文，`en`表示英文。

(3) 下载VAD模型
请访问
https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/silero_vad.onnx
下载silero_vad.onnx

例如：

wget https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/silero_vad.onnx

(4) 运行此脚本

假设文本文件名为speaker.txt。

python3 ./python-api-examples/speaker-identification-with-vad-中文.py \
  --silero-vad-model=/path/to/silero_vad.onnx \
  --speaker-file ./speaker.txt \
  --model ./wespeaker_zh_cnceleb_resnet34.onnx
"""
import argparse
import sys
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Tuple

import numpy as np
import sherpa_onnx
import soundfile as sf

try:
    import sounddevice as sd
except ImportError:
    print("请先安装sounddevice。您可以使用")
    print()
    print("  pip install sounddevice")
    print()
    print("来安装它")
    sys.exit(-1)


def get_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--speaker-file",
        type=str,
        # required=True,
        default="sr-data/speaker.txt",
        help="""说话人文件的路径。请阅读此文件开头的帮助文档了解格式。""",
    )

    parser.add_argument(
        "--model",
        type=str,
        # required=True,
        default="model/3dspeaker_speech_eres2net_large_sv_zh-cn_3dspeaker_16k.onnx",
        help="说话人嵌入向量模型文件的路径。",
    )

    parser.add_argument(
        "--silero-vad-model",
        type=str,
        # required=True,
        default="model/silero_vad.onnx",
        help="silero_vad.onnx的路径",
    )

    parser.add_argument("--threshold", type=float, default=0.6)

    parser.add_argument(
        "--num-threads",
        type=int,
        default=1,
        help="神经网络计算的线程数",
    )

    parser.add_argument(
        "--debug",
        type=bool,
        default=True,
        help="设为True显示调试信息",
    )

    parser.add_argument(
        "--provider",
        type=str,
        default="cpu",
        help="有效值：cpu, cuda, coreml",
    )

    return parser.parse_args()


def load_speaker_embedding_model(args):
    config = sherpa_onnx.SpeakerEmbeddingExtractorConfig(
        model=args.model,
        num_threads=args.num_threads,
        debug=args.debug,
        provider=args.provider,
    )
    if not config.validate():
        raise ValueError(f"无效配置。{config}")
    extractor = sherpa_onnx.SpeakerEmbeddingExtractor(config)
    return extractor


def load_speaker_file(args) -> Dict[str, List[str]]:
    if not Path(args.speaker_file).is_file():
        raise ValueError(f"--speaker-file {args.speaker_file} 不存在")

    ans = defaultdict(list)
    with open(args.speaker_file, encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            fields = line.split()
            if len(fields) != 2:
                raise ValueError(f"无效行：{line}。字段：{fields}")

            speaker_name, filename = fields
            ans[speaker_name].append(filename)
    return ans


def load_audio(filename: str) -> Tuple[np.ndarray, int]:
    data, sample_rate = sf.read(
        filename,
        always_2d=True,
        dtype="float32",
    )
    data = data[:, 0]  # 只使用第一个声道
    samples = np.ascontiguousarray(data)
    return samples, sample_rate


def compute_speaker_embedding(
    filenames: List[str],
    extractor: sherpa_onnx.SpeakerEmbeddingExtractor,
) -> np.ndarray:
    assert len(filenames) > 0, "文件名列表为空"

    ans = None
    for filename in filenames:
        print(f"正在处理 {filename}")
        samples, sample_rate = load_audio(filename)
        stream = extractor.create_stream()
        stream.accept_waveform(sample_rate=sample_rate, waveform=samples)
        stream.input_finished()

        assert extractor.is_ready(stream)
        embedding = extractor.compute(stream)
        embedding = np.array(embedding)
        if ans is None:
            ans = embedding
        else:
            ans += embedding

    return ans / len(filenames)


g_sample_rate = 16000


def main():
    args = get_args()
    print(args)
    extractor = load_speaker_embedding_model(args)
    speaker_file = load_speaker_file(args)

    manager = sherpa_onnx.SpeakerEmbeddingManager(extractor.dim)
    for name, filename_list in speaker_file.items():
        embedding = compute_speaker_embedding(
            filenames=filename_list,
            extractor=extractor,
        )
        status = manager.add(name, embedding)
        if not status:
            raise RuntimeError(f"注册说话人 {name} 失败")

    print("正在初始化VAD模型...")
    vad_config = sherpa_onnx.VadModelConfig()
    vad_config.silero_vad.model = args.silero_vad_model
    vad_config.silero_vad.min_silence_duration = 0.25
    vad_config.silero_vad.min_speech_duration = 0.25
    vad_config.sample_rate = g_sample_rate

    print("正在创建VAD检测器...")
    window_size = vad_config.silero_vad.window_size
    vad = sherpa_onnx.VoiceActivityDetector(vad_config, buffer_size_in_seconds=100)
    print("VAD初始化完成")

    samples_per_read = int(0.1 * g_sample_rate)  # 0.1秒 = 100毫秒

    print("正在查询音频设备...")
    devices = sd.query_devices()
    if len(devices) == 0:
        print("未找到麦克风设备")
        sys.exit(0)

    print("可用音频设备:")
    print(devices)
    default_input_device_idx = sd.default.device[0]
    print(f'使用默认设备：{devices[default_input_device_idx]["name"]}')

    print("已启动！请开始说话")

    idx = 0
    buffer = []
    with sd.InputStream(channels=1, dtype="float32", samplerate=g_sample_rate) as s:
        while True:
            samples, _ = s.read(samples_per_read)  # 阻塞读取
            samples = samples.reshape(-1)
            buffer = np.concatenate([buffer, samples])
            while len(buffer) > window_size:
                vad.accept_waveform(buffer[:window_size])
                buffer = buffer[window_size:]

            while not vad.empty():
                if len(vad.front.samples) < 0.5 * g_sample_rate:
                    # 这个片段太短，跳过它
                    vad.pop()
                    continue
                stream = extractor.create_stream()
                stream.accept_waveform(
                    sample_rate=g_sample_rate, waveform=vad.front.samples
                )
                vad.pop()
                stream.input_finished()

                print("计算中", end="")
                embedding = extractor.compute(stream)
                embedding = np.array(embedding)
                name = manager.search(embedding, threshold=args.threshold)
                if not name:
                    name = "未知"
                print(f"\r{idx}: 预测姓名：{name}")
                idx += 1


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n捕获到Ctrl + C。正在退出")
