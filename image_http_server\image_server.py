#!/usr/bin/env python3
"""
图片HTTP服务器
使用FastAPI实现图片文件的HTTP访问服务
支持静态文件服务和RESTful API接口

作者: AI Assistant
日期: 2025-08-08
"""

import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
import mimetypes

import uvicorn
from fastapi import FastAPI, HTTPException, Response, status, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from config import get_config

# 获取配置
config = get_config()

# 配置日志
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Pydantic模型
class ImageInfo(BaseModel):
    """图片信息模型"""
    filename: str
    size: int
    created_time: Optional[str] = None
    modified_time: Optional[str] = None
    file_extension: Optional[str] = None
    exists: bool = True

class ApiResponse(BaseModel):
    """API响应模型"""
    success: bool
    message: str
    data: Optional[Any] = None

# 创建FastAPI应用
app = FastAPI(
    title="图片HTTP服务器",
    description="提供图片文件的HTTP访问服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "HEAD", "OPTIONS"],
    allow_headers=["*"],
)

# 添加请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = datetime.now()

    # 记录请求信息
    logger.info(f"请求: {request.method} {request.url.path} - 客户端: {request.client.host}")

    response = await call_next(request)

    # 计算处理时间
    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"响应: {response.status_code} - 处理时间: {process_time:.3f}s")

    return response

class ImageService:
    """图片服务类"""

    def __init__(self, images_directory: str = None):
        if images_directory is None:
            images_directory = config.IMAGES_DIR
        self.images_dir = Path(images_directory)
        self.ensure_directory_exists()
    
    def ensure_directory_exists(self):
        """确保图片目录存在"""
        if not self.images_dir.exists():
            logger.warning(f"图片目录不存在: {self.images_dir}")
            # 在Windows环境下创建测试目录
            test_dir = Path("./test_images")
            test_dir.mkdir(exist_ok=True)
            self.images_dir = test_dir
            logger.info(f"使用测试目录: {self.images_dir}")
    
    def get_image_path(self, filename: str) -> Path:
        """获取图片完整路径"""
        return self.images_dir / filename
    
    def image_exists(self, filename: str) -> bool:
        """检查图片是否存在"""
        return self.get_image_path(filename).exists()
    
    def get_image_info(self, filename: str) -> ImageInfo:
        """获取图片信息"""
        image_path = self.get_image_path(filename)
        
        if not image_path.exists():
            return ImageInfo(
                filename=filename,
                size=0,
                exists=False
            )
        
        stat = image_path.stat()
        return ImageInfo(
            filename=filename,
            size=stat.st_size,
            created_time=datetime.fromtimestamp(stat.st_ctime).isoformat(),
            modified_time=datetime.fromtimestamp(stat.st_mtime).isoformat(),
            file_extension=image_path.suffix.lower(),
            exists=True
        )
    
    def list_images(self) -> List[str]:
        """列出所有图片文件"""
        if not self.images_dir.exists():
            return []
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}
        images = []
        
        for file_path in self.images_dir.iterdir():
            if file_path.is_file():
                # 如果没有扩展名，也认为是图片（根据您的文件名格式）
                if not file_path.suffix or file_path.suffix.lower() in image_extensions:
                    images.append(file_path.name)
        
        return sorted(images)

# 创建图片服务实例
image_service = ImageService()

# 挂载静态文件服务
if image_service.images_dir.exists():
    app.mount("/static", StaticFiles(directory=str(image_service.images_dir)), name="static")

@app.get("/", response_model=ApiResponse)
async def root():
    """根路径，返回服务信息"""
    return ApiResponse(
        success=True,
        message="图片HTTP服务器运行中",
        data={
            "server": f"{config.HOST}:{config.PORT}",
            "images_directory": str(image_service.images_dir),
            "endpoints": {
                "get_image": "/images/{filename}",
                "get_image_info": "/api/images/{filename}",
                "list_images": "/api/images",
                "static_files": "/static/{filename}",
                "docs": "/docs"
            }
        }
    )

@app.get("/images/{filename}")
async def get_image(filename: str):
    """
    获取图片文件
    直接返回图片文件内容，支持浏览器直接显示
    """
    logger.info(f"请求图片: {filename}")
    
    if not image_service.image_exists(filename):
        logger.warning(f"图片不存在: {filename}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"图片文件不存在: {filename}"
        )
    
    image_path = image_service.get_image_path(filename)
    
    # 根据文件扩展名设置媒体类型
    media_type = "image/jpeg"  # 默认
    if image_path.suffix.lower() in ['.png']:
        media_type = "image/png"
    elif image_path.suffix.lower() in ['.gif']:
        media_type = "image/gif"
    elif image_path.suffix.lower() in ['.webp']:
        media_type = "image/webp"
    
    return FileResponse(
        path=str(image_path),
        media_type=media_type,
        filename=filename
    )

@app.get("/api/images/{filename}", response_model=ApiResponse)
async def get_image_info(filename: str):
    """
    获取图片信息
    返回图片的详细信息（大小、创建时间等）
    """
    logger.info(f"请求图片信息: {filename}")
    
    image_info = image_service.get_image_info(filename)
    
    if not image_info.exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"图片文件不存在: {filename}"
        )
    
    return ApiResponse(
        success=True,
        message="获取图片信息成功",
        data=image_info.model_dump()
    )

@app.get("/api/images", response_model=ApiResponse)
async def list_images():
    """
    列出所有可用的图片
    返回图片目录中所有图片文件的列表
    """
    logger.info("请求图片列表")
    
    images = image_service.list_images()
    
    return ApiResponse(
        success=True,
        message=f"找到 {len(images)} 张图片",
        data={
            "count": len(images),
            "images": images,
            "directory": str(image_service.images_dir)
        }
    )

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

if __name__ == "__main__":
    logger.info(f"启动图片HTTP服务器...")
    logger.info(f"服务地址: http://{config.HOST}:{config.PORT}")
    logger.info(f"图片目录: {image_service.images_dir}")
    logger.info(f"API文档: http://{config.HOST}:{config.PORT}/docs")

    uvicorn.run(
        app,
        host=config.HOST,
        port=config.PORT,
        log_level=config.LOG_LEVEL.lower(),
        access_log=True
    )
