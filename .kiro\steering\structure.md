# Project Structure

## Root Directory Organization

```
├── .kiro/                    # Kiro IDE configuration and steering rules
├── Agently/                  # Main Agently framework source code
├── agentscope/              # AgentScope related experiments
├── crewai/                  # CrewAI multi-agent examples and demos
├── html-copy/               # Web-related projects and clones
├── mcp/                     # Model Context Protocol implementations
├── runs/                    # Execution logs and run artifacts
├── tests/                   # Test files and demo scripts
└── ragflow-llm.py          # RAGFlow integration example
```

## Key Directories

### `/Agently/`
- **Core Framework**: Main Agently AI agent development framework
- **Structure**: Standard Python package with docs, examples, playground
- **Key Files**: 
  - `pyproject.toml` - Project configuration and dependencies
  - `README.md` - Framework documentation and examples
  - `playground/` - Interactive examples and demos

### `/crewai/`
- **Multi-Agent Examples**: CrewAI framework implementations
- **File Patterns**:
  - `*-agent.py` - Individual agent implementations
  - `business-plan.py` - Business planning workflow
  - `test_*.py` - Testing scripts
  - `Dockerfile` - Containerization setup

### `/mcp/`
- **MCP Protocol Testing**: Model Context Protocol servers and clients
- **Subdirectories**:
  - `mcp-fastapi/` - FastAPI-based MCP implementations
  - `mcp-fastmcp/` - FastMCP framework examples
  - `mcp-test/` - Testing scripts and server implementations

### `/tests/`
- **Demo Scripts**: Testing and demonstration code
- **Focus**: MCP integration and framework testing

### `/runs/`
- **Execution Artifacts**: Timestamped execution logs
- **Pattern**: `run_YYYYMMDD-HHMMSS_<id>/`
- **Purpose**: Historical execution tracking

## File Naming Conventions

- **Agent Files**: `*-agent.py` for individual agent implementations
- **Test Files**: `test_*.py` for testing scripts
- **Demo Files**: `*-demo.py` for demonstration code
- **MCP Files**: `*_mcp_server.py` for MCP server implementations

## Configuration Files

- **Poetry**: `pyproject.toml` for Python dependency management
- **Git**: `.gitignore` for version control exclusions
- **Docker**: `Dockerfile` for containerization
- **Kiro**: `.kiro/` directory for IDE-specific configurations

## Development Patterns

- Each major framework (Agently, CrewAI, MCP) has its own directory
- Examples and demos are co-located with their respective frameworks
- Test files are centralized in `/tests/` for cross-framework testing
- Configuration files follow standard Python project conventions