from crewai import LLM, Agent, Crew, Process, Task
from crewai_tools import CodeInterpreterTool
import os
from dotenv import load_dotenv
load_dotenv()

# api_key = os.getenv('SiliconCloud_API_KEY')
# base_url = os.getenv('SiliconCloud_API_BASE')
# model = os.getenv('SiliconCloud_MODEL_NAME', 'openai/Qwen/Qwen2.5-72B-Instruct')  # Provide a default model if not set

agent_llm = LLM(
    model="openai/ep-20250122141838-8gz65",
    api_base="https://ark.cn-beijing.volces.com/api/v3",
    api_key="5dd64d02-d0b4-48fd-91f0-d4a91f359456",
    temperature=0.5
	)

# Create an LLM with a temperature of 0 to ensure deterministic outputs
# llm = LLM(model="gpt-4o-mini", temperature=0)

# Create an agent with the knowledge store
agent = Agent(
    role="Tool User",
    goal="You know how to use the tool.",
    backstory="""
    You are a master at using the tool.
    使用代码执行工具时，如果没有使用库，则libraries_used参数为空列表，需要写成libraries_used=[]。
    """,
    verbose=True,
    allow_delegation=False,
    llm=agent_llm,
    tools=[CodeInterpreterTool()],
)
task = Task(
    description="""
    根据用户需求：{question}
    使用相应的工具。
    """,
    expected_output="获取本次工具输出的结果，无需其它内容。",
    agent=agent,
)

crew = Crew(
    agents=[agent],
    tasks=[task],
    verbose=True,
    process=Process.sequential,
)

result = crew.kickoff(
    inputs={
        "question": "画一个折线图，横坐标为1月、2月、3月，纵坐标为12、15、17,将结果保存到test.png。"
    }
)
