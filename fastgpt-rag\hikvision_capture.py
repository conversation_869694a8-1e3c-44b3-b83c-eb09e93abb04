import cv2
import time
import os
import sys
from datetime import datetime
from camera_config import CAMERA_CONFIG, CAPTURE_CONFIG, get_rtsp_url

class HikvisionCapture:
    def __init__(self):
        self.rtsp_url = get_rtsp_url()
        self.interval = CAPTURE_CONFIG['interval']
        self.output_dir = CAPTURE_CONFIG['output_dir']
        self.show_preview = CAPTURE_CONFIG['show_preview']
        self.image_quality = CAPTURE_CONFIG['image_quality']
        self.cap = None
        self.frame_count = 0
        
    def setup_output_dir(self):
        """创建输出目录"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"创建输出目录: {self.output_dir}")
    
    def connect_camera(self):
        """连接摄像头"""
        print(f"正在连接摄像头: {CAMERA_CONFIG['ip']}")
        
        self.cap = cv2.VideoCapture(self.rtsp_url)
        
        # 设置参数减少延迟
        self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        self.cap.set(cv2.CAP_PROP_FPS, 25)
        
        if not self.cap.isOpened():
            print(f"❌ 无法连接到摄像头")
            print(f"请检查:")
            print(f"  - IP地址: {CAMERA_CONFIG['ip']}")
            print(f"  - 用户名: {CAMERA_CONFIG['username']}")
            print(f"  - 密码: {CAMERA_CONFIG['password']}")
            print(f"  - 网络连接")
            return False
        
        print(f"✅ 摄像头连接成功")
        return True
    
    def save_frame(self, frame):
        """保存帧到文件"""
        timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        filename = f"frame_{self.frame_count:04d}_{timestamp}.jpg"
        filepath = os.path.join(self.output_dir, filename)
        
        # 设置JPEG压缩参数
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, self.image_quality]
        
        success = cv2.imwrite(filepath, frame, encode_params)
        
        if success:
            file_size = os.path.getsize(filepath) / 1024  # KB
            print(f"📸 保存帧 #{self.frame_count}: {filename} ({file_size:.1f}KB)")
            self.frame_count += 1
            return True
        else:
            print(f"❌ 保存失败: {filename}")
            return False
    
    def run(self):
        """主运行循环"""
        print("海康摄像头帧捕获程序")
        print("=" * 50)
        print(f"摄像头IP: {CAMERA_CONFIG['ip']}")
        print(f"保存间隔: {self.interval}秒")
        print(f"输出目录: {self.output_dir}")
        print(f"预览窗口: {'开启' if self.show_preview else '关闭'}")
        print("=" * 50)
        print("按 'q' 键或 Ctrl+C 退出程序")
        print()
        
        # 设置输出目录
        self.setup_output_dir()
        
        # 连接摄像头
        if not self.connect_camera():
            return
        
        last_save_time = time.time()
        
        try:
            while True:
                ret, frame = self.cap.read()
                
                if not ret:
                    print("⚠️  读取帧失败，尝试重新连接...")
                    self.cap.release()
                    time.sleep(2)
                    
                    if not self.connect_camera():
                        print("❌ 重连失败，程序退出")
                        break
                    continue
                
                current_time = time.time()
                
                # 检查是否到了保存时间
                if current_time - last_save_time >= self.interval:
                    self.save_frame(frame)
                    last_save_time = current_time
                
                # 显示预览（如果启用）
                if self.show_preview:
                    # 在图像上添加时间戳
                    timestamp_text = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    cv2.putText(frame, timestamp_text, (10, 30), 
                              cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                    
                    cv2.imshow('Hikvision RTSP Stream', frame)
                    
                    # 检查按键
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q'):
                        print("\n用户按 'q' 退出程序")
                        break
                else:
                    # 即使不显示预览，也要处理事件避免程序卡死
                    cv2.waitKey(1)
                    
        except KeyboardInterrupt:
            print("\n\n程序被用户中断 (Ctrl+C)")
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print(f"\n程序结束，共保存 {self.frame_count} 帧")
        print("资源已释放")

if __name__ == "__main__":
    capture = HikvisionCapture()
    capture.run()