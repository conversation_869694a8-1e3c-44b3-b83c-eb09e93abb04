#!/usr/bin/env python3
"""
图片服务器测试脚本
"""

import requests
import json
from pathlib import Path

# 服务器配置
SERVER_URL = "http://192.168.10.251:23333"

def test_server_health():
    """测试服务器健康状态"""
    try:
        response = requests.get(f"{SERVER_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器健康检查通过")
            return True
        else:
            print(f"✗ 服务器健康检查失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到服务器: {e}")
        return False

def test_root_endpoint():
    """测试根端点"""
    try:
        response = requests.get(f"{SERVER_URL}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✓ 根端点测试通过")
            print(f"  服务器信息: {data.get('data', {}).get('server', 'N/A')}")
            return True
        else:
            print(f"✗ 根端点测试失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 根端点请求失败: {e}")
        return False

def test_list_images():
    """测试图片列表接口"""
    try:
        response = requests.get(f"{SERVER_URL}/api/images", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✓ 图片列表接口测试通过")
            images = data.get('data', {}).get('images', [])
            print(f"  找到 {len(images)} 张图片")
            if images:
                print(f"  示例图片: {images[0]}")
            return images
        else:
            print(f"✗ 图片列表接口测试失败: {response.status_code}")
            return []
    except requests.exceptions.RequestException as e:
        print(f"✗ 图片列表请求失败: {e}")
        return []

def test_get_image_info(filename):
    """测试获取图片信息接口"""
    try:
        response = requests.get(f"{SERVER_URL}/api/images/{filename}", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 图片信息接口测试通过: {filename}")
            image_info = data.get('data', {})
            print(f"  文件大小: {image_info.get('size', 0)} 字节")
            return True
        elif response.status_code == 404:
            print(f"✗ 图片不存在: {filename}")
            return False
        else:
            print(f"✗ 图片信息接口测试失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 图片信息请求失败: {e}")
        return False

def test_get_image(filename):
    """测试获取图片文件接口"""
    try:
        response = requests.get(f"{SERVER_URL}/images/{filename}", timeout=10)
        if response.status_code == 200:
            print(f"✓ 图片文件接口测试通过: {filename}")
            print(f"  Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"  文件大小: {len(response.content)} 字节")
            return True
        elif response.status_code == 404:
            print(f"✗ 图片文件不存在: {filename}")
            return False
        else:
            print(f"✗ 图片文件接口测试失败: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 图片文件请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("图片服务器测试")
    print("=" * 50)
    print(f"测试服务器: {SERVER_URL}")
    print()
    
    # 测试服务器健康状态
    if not test_server_health():
        print("服务器不可用，请检查服务器是否启动")
        return
    
    print()
    
    # 测试根端点
    test_root_endpoint()
    print()
    
    # 测试图片列表
    images = test_list_images()
    print()
    
    # 如果有图片，测试图片相关接口
    if images:
        test_filename = images[0]
        test_get_image_info(test_filename)
        print()
        test_get_image(test_filename)
    else:
        # 测试不存在的图片
        test_filename = "02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c"
        print(f"测试不存在的图片: {test_filename}")
        test_get_image_info(test_filename)
        print()
        test_get_image(test_filename)
    
    print()
    print("=" * 50)
    print("测试完成")
    print("=" * 50)
    
    # 显示可用的URL
    print("\n可以在浏览器中测试以下URL:")
    print(f"  {SERVER_URL}/")
    print(f"  {SERVER_URL}/docs")
    print(f"  {SERVER_URL}/api/images")
    if images:
        print(f"  {SERVER_URL}/images/{images[0]}")

if __name__ == "__main__":
    main()
