from agentscope.agents import ReActAgentV2, UserAgent
from agentscope.service import ServiceToolkit, execute_python_code
import agentscope

# 加载模型配置
agentscope.init(
    model_configs=[
        {
            "config_name": "my_config",
            "model_type": "openai_chat",
            "model_name": "doubao-1-5-pro-32k-250115",
            "api_key": "5dd64d02-d0b4-48fd-91f0-d4a91f359456",
            "client_args": {"base_url": "https://ark.cn-beijing.volces.com/api/v3/"},
            "generate_args": {"temperature": 0.5},
        }
    ]
)

# 添加内置工具
toolkit = ServiceToolkit()
toolkit.add(execute_python_code)

# 连接到高德 MCP Server
toolkit.add_mcp_servers(
    {
        "mcpServers": {
            "amap-amap-sse": {
                "type": "sse",
                "url": "https://mcp.amap.com/sse?key=8ca154892e0cdf9a466ac15e1fa3c6a9",
            }
        }
    }
)

# 创建一个 ReAct 智能体
agent = ReActAgentV2(
    name="Friday",
    model_config_name="my_config",
    service_toolkit=toolkit,
    sys_prompt="你是一个名为Friday的AI助手。",
)
user_agent = UserAgent(name="user")

# 显式构建工作流程/对话
x = None
while x is None or x.content != "exit":
    x = agent(x)
    x = user_agent(x)
