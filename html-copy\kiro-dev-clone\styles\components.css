/* Component-specific styles */

/* Navigation Component */
.nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--color-border);
  z-index: 1000;
  transition: all var(--transition-fast);
}

.nav__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
  padding: 0 var(--spacing-4);
}

.nav__brand {
  display: flex;
  align-items: center;
}

.nav__logo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.nav__logo:hover {
  color: var(--color-primary);
}

.nav__logo-text {
  background: linear-gradient(135deg, var(--color-primary), var(--color-success));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav__menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-8);
}

.nav__list {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav__link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
}

.nav__link:hover {
  color: var(--color-text-primary);
  background-color: var(--color-gray-100);
}

.nav__link:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.nav__actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

/* Mobile Navigation Toggle */
.nav__toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.nav__toggle-line {
  width: 1.25rem;
  height: 2px;
  background-color: var(--color-text-primary);
  transition: all var(--transition-fast);
  margin: 2px 0;
}

.nav__toggle[aria-expanded="true"] .nav__toggle-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav__toggle[aria-expanded="true"] .nav__toggle-line:nth-child(2) {
  opacity: 0;
}

.nav__toggle[aria-expanded="true"] .nav__toggle-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Hero Section Component */
.hero {
  padding: var(--spacing-20) 0 var(--spacing-16);
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.05) 0%, 
    rgba(16, 185, 129, 0.05) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero__content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-12);
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero__text {
  text-align: center;
}

.hero__title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: 1.1;
  margin-bottom: var(--spacing-6);
  color: var(--color-text-primary);
}

.hero__title-highlight {
  background: linear-gradient(135deg, var(--color-primary), var(--color-success));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero__subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero__actions {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  flex-wrap: wrap;
}

.hero__visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero__image-placeholder {
  width: 100%;
  max-width: 500px;
  height: 300px;
  background: linear-gradient(135deg, 
    var(--color-gray-100) 0%, 
    var(--color-gray-200) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  animation: float 6s ease-in-out infinite;
  transform-style: preserve-3d;
  transition: transform 0.1s ease-out;
}

.hero__image-placeholder::before {
  content: '🚀';
  font-size: 4rem;
  opacity: 0.3;
  animation: rocket-float 4s ease-in-out infinite;
  transform-origin: center;
}

/* Floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotateX(0deg) rotateY(0deg);
  }
  25% {
    transform: translateY(-10px) rotateX(2deg) rotateY(-1deg);
  }
  50% {
    transform: translateY(-5px) rotateX(0deg) rotateY(2deg);
  }
  75% {
    transform: translateY(-15px) rotateX(-2deg) rotateY(-1deg);
  }
}

/* Rocket floating animation */
@keyframes rocket-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-5px) rotate(2deg);
  }
  50% {
    transform: translateY(-2px) rotate(-1deg);
  }
  75% {
    transform: translateY(-8px) rotate(1deg);
  }
}

/* Mouse interaction effects */
.hero__image-placeholder:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
}

/* Hero Animations */
.hero__title {
  animation: fadeInUp 0.8s ease-out;
}

.hero__subtitle {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.hero__actions {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hero__visual {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced button hover effects */
.hero .btn-primary {
  position: relative;
  overflow: hidden;
  transform: translateY(0);
  transition: all var(--transition-fast);
}

.hero .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.hero .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.hero .btn-primary:hover::before {
  left: 100%;
}

.hero .btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Ripple effect for buttons */
.btn {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Features Section Component */
.features {
  padding: var(--spacing-20) 0;
  background-color: var(--color-background);
}

.features__header {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.features__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-4);
  color: var(--color-text-primary);
}

.features__subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.features__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-8);
  margin-top: var(--spacing-12);
}

.feature-card {
  background: var(--color-white);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.feature-card__icon {
  width: 3rem;
  height: 3rem;
  margin: 0 auto var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary), var(--color-success));
  border-radius: var(--radius-lg);
  color: var(--color-white);
  transition: all var(--transition-normal);
}

.feature-card:hover .feature-card__icon {
  transform: scale(1.1);
}

.feature-icon {
  width: 1.5rem;
  height: 1.5rem;
}

.feature-card__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-3);
  color: var(--color-text-primary);
}

.feature-card__description {
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin-bottom: 0;
}

/* Feature card animations */
.feature-card--hidden {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.feature-card--visible {
  opacity: 1;
  transform: translateY(0);
}

.feature-card--focused {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* CTA Section */
.cta {
  padding: var(--spacing-20) 0;
  background: linear-gradient(135deg, 
    var(--color-gray-900) 0%, 
    var(--color-gray-800) 100%);
  color: var(--color-white);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.cta__content {
  position: relative;
  z-index: 1;
}

.cta__title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-4);
  color: var(--color-white);
}

.cta__subtitle {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta__actions {
  display: flex;
  gap: var(--spacing-4);
  justify-content: center;
  flex-wrap: wrap;
}

/* Footer Component */
.footer {
  background-color: var(--color-gray-900);
  color: var(--color-white);
  padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer__content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-12);
  margin-bottom: var(--spacing-12);
}

.footer__brand {
  display: flex;
  flex-direction: column;
}

.footer__logo {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-white);
  text-decoration: none;
  margin-bottom: var(--spacing-4);
}

.footer__logo-text {
  background: linear-gradient(135deg, var(--color-primary), var(--color-success));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer__description {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  max-width: 300px;
}

.footer__links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-8);
}

.footer__column-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-4);
  color: var(--color-white);
}

.footer__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer__list li {
  margin-bottom: var(--spacing-2);
}

.footer__link {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: color var(--transition-fast);
  font-size: var(--font-size-sm);
}

.footer__link:hover {
  color: var(--color-white);
}

.footer__bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.footer__copyright {
  color: rgba(255, 255, 255, 0.6);
  font-size: var(--font-size-sm);
}

.footer__social {
  display: flex;
  gap: var(--spacing-4);
}

.footer__social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  color: rgba(255, 255, 255, 0.7);
  transition: all var(--transition-fast);
}

.footer__social-link:hover {
  background-color: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
}