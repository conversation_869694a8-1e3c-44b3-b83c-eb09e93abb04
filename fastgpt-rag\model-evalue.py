from evalscope.perf.main import run_perf_benchmark
from evalscope.perf.arguments import Arguments

task_cfg = Arguments(
    parallel=[1, 10],
    number=[10, 20],
    model='modelscope.cn/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF',
    url='http://192.168.10.120:11434/v1/chat/completions',
    api='openai',
    dataset='openqa',
    min_tokens=50,  # 降低最小token要求
    max_tokens=2048,  # 增加最大token限制
    prefix_length=0,
    min_prompt_length=10,  # 大幅降低最小prompt长度
    max_prompt_length=2048,  # 增加最大prompt长度
    # tokenizer_path='modelscope.cn/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF',
    swanlab_api_key = 'TAQWFItPpzVdQpOuXQKCU',
    name = 'name_of_wandb_log',
    extra_args={'ignore_eos': True}
)
results = run_perf_benchmark(task_cfg)