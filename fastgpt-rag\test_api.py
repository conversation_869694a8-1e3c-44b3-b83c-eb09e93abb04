import requests
import json

# 测试API连接
def test_api_connection():
    token = "fastgpt-gZHVXrSu6UqOmvmAKsYeHhtkxdywJBqkwuM8KuZ8ia3TxdGxJqQv6Ic53ctajPJf"
    dataset_id = "6593e137231a2be9c5603ba7"
    
    url = 'http://192.168.10.251:3000/api/core/dataset/collection/listV2'
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "offset": 0,
        "pageSize": 10,
        "datasetId": dataset_id,
        "parentId": None,
        "searchText": ""
    }
    
    print("测试API连接...")
    print(f"URL: {url}")
    print(f"Headers: {headers}")
    print(f"Data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应内容: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_api_connection()