[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[project]
name = "streamingjson"
version = "0.0.4"
authors = [
  { name="<PERSON><PERSON><PERSON><PERSON>", email="<EMAIL>" },
]
description = "A streamlined, user-friendly JSON streaming preprocessor, crafted in Python."
readme = "README.md"
requires-python = ">=3.7"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

[project.urls]
Homepage = "https://github.com/karminski/streaming-json-py"
Issues = "https://github.com/karminski/streaming-json-py/issues"
