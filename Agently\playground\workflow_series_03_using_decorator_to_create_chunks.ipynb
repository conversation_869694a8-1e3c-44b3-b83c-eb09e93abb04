{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/workflow_series_03_using_decorator_to_create_chunks.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "dAzfqHDCAXZe"}, "source": ["# Using Decorator to Create <PERSON><PERSON> Faster `#Agently_Workflow_Showcase_Series - 03`"]}, {"cell_type": "markdown", "metadata": {"id": "kyLFmv_l-aIx"}, "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Series Link**:\n", "\n", "[Last Document]: [02 - Using Condition to Branch Off](https://github.com/Maplemx/Agently/blob/main/playground/workflow_series_02_using_condition_to_branch_off.ipynb)\n", "\n", "[Next Document]: [04 - Draw a Workflow Graph to Help You Observe the Workflow](https://github.com/Maplemx/Agently/blob/main/playground/workflow_series_04_draw_a_workflow_graph.ipynb)\n", "\n", "**Description:**\n", "\n", "Now you may have a basic concept about **Agently Workflow** like how to create chunks, how to connect chunks and how to start a workflow.\n", "\n", "But some may say \"It's still too complex to create a chunk.\" Can we just define a function and make it as a chunk? Because the main body of a chunk is basically the executor function.\n", "\n", "Sure can do. Using decorator `@workflow.chunk()` provided by Agently workflow instance can make the chunk creation much more easier.\n", "\n", "读到这个案例的时候，你可能已经对**Agently Workflow**有了很多基本的概念理解，比如：如何创建流程块（chunk），如何连接流程块，如何启动连接好的workflow等等。\n", "\n", "不过可能还是有人会觉得，创建流程块这件事情是不是太复杂了？我们就不能直接把一个函数定义成流程块吗？因为从定义的视角看，一个流程块的主要内容就是它的执行函数。\n", "\n", "当然没问题。我们在workflow实例里提供了`@workflow.chunk()`装饰器，使用它，就能更加连贯一体地结合函数定义创建流程块（chunk）了。"]}, {"cell_type": "markdown", "metadata": {"id": "nRsfMu4lAJZF"}, "source": ["## Step 1: Install Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nsst7pOAANlr"}, "outputs": [], "source": ["!pip install -q -U Agently"]}, {"cell_type": "markdown", "metadata": {"id": "diFJhvd3ORd1"}, "source": ["> ℹ️ Agently Workflow is a new feature in version >= `3.2.0`"]}, {"cell_type": "markdown", "metadata": {"id": "_-1gryYwASPM"}, "source": ["## Step 2: Rewrite Code in Showcase 02"]}, {"cell_type": "markdown", "source": ["### Code"], "metadata": {"id": "eeM97ZUGitlr"}}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "7909d1f6-e68a-49b7-8525-f38d61eeb1cd"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[User]: <PERSON>, I'm <PERSON>\n", "[Assistant]:  <PERSON>, I'm Bar<PERSON>, a large language model trained by Google. Nice to meet you!\n", "[User]: Yeah, tell me one tip about Rust\n", "[Assistant]:  One tip about Rust is to use the borrow checker to your advantage. The borrow checker is a tool that helps you to ensure that your code is memory-safe. By understanding how the borrow checker works, you can write code that is more efficient and less error-prone. For example, you can use the borrow checker to help you to avoid data races and dangling pointers.\n", "[User]: Tell me more about it\n", "[Assistant]:  The Rust borrow checker is a tool that helps to ensure that your code is memory-safe. It does this by tracking the ownership and borrowing of data in your program. This helps to prevent data races and dangling pointers, which are two common sources of memory errors.\n", "\n", "The borrow checker works by enforcing a set of rules about how data can be borrowed. For example, a value can only be borrowed once at a time, and a borrowed value cannot be modified. These rules help to ensure that your code is always in a valid state, even if multiple threads are accessing the same data.\n", "\n", "The borrow checker can be a bit strict at times, but it is very effective at preventing memory errors. By understanding how the borrow checker works, you can write code that is more efficient and less error-prone.\n", "\n", "Here are some tips for using the borrow checker to your advantage:\n", "\n", "* Use immutable data whenever possible. Immutable data cannot be modified, so it is always safe to borrow.\n", "* Avoid sharing mutable data between threads. Sharing mutable data between threads can lead to data races.\n", "* Use the `RefCell` and `Mutex` types to safely share mutable data between threads.\n", "* Use the `Cow` type to work with data that can be either borrowed or owned.\n", "\n", "By following these tips, you can write Rust code that is both safe and efficient.\n", "[User]: #exit\n", "Bye~👋\n"]}], "source": ["import Agently\n", "agent_factory = (\n", "        Agently.AgentFactory()\n", "        .set_settings(\"model.Google.auth.api_key\", \"\")\n", "        .set_settings(\"current_model\", \"Google\")\n", ")\n", "\n", "# Step 0. Create Workflow Instance and Agent Instance\n", "workflow = Agently.Workflow()\n", "# We updated logger level settings framework logic.\n", "# Now INFO log will not present unless you're in debug mode.\n", "# So you don't need to reset logger level manually anymore when version >= *******\n", "# workflow.settings.set(\"logger\", logging.getLogger(\"Workflow\").setLevel(logging.WARNING))\n", "agent = agent_factory.create_agent()\n", "\n", "# Step 1. C<PERSON> <PERSON><PERSON>\n", "## Start Chunk\n", "@workflow.chunk(\n", "    # in @workflow.chunk() decorator, `chunk_id` is required\n", "    chunk_id = \"Start\",\n", "    # you can pass same params as `workflow.schema.create_chunk()` required except executor\n", "    # if you did not state param `title`, it will using the value of `chunk_id` by default\n", "    type = \"Start\"\n", ")\n", "# You don't have to decorate a function when the chunk type is \"Start\".\n", "\n", "## User Input Chunk\n", "@workflow.chunk(\n", "    chunk_id = \"User Input\",\n", "    handles = {\n", "        \"outputs\": [{ \"handle\": \"user_input\" }],\n", "    }\n", ")\n", "def user_input_executor(inputs, storage):\n", "    return { \"user_input\": input(\"[User]: \") }\n", "\n", "## Assistant <PERSON><PERSON>\n", "@workflow.chunk(\n", "    chunk_id = \"Assistant Reply\",\n", "    handles = {\n", "        \"inputs\": [{ \"handle\": \"user_input\" }],\n", "        \"outputs\": [{ \"handle\": \"assistant_reply\" }],\n", "    }\n", ")\n", "def assistant_reply_executor(inputs, storage):\n", "    chat_history = storage.get(\"chat_history\") or []\n", "    reply = (\n", "        agent\n", "            .chat_history(chat_history)\n", "            .input(inputs[\"user_input\"])\n", "            .start()\n", "    )\n", "    print(\"[Assistant]: \", reply)\n", "    return { \"assistant_reply\": reply }\n", "\n", "## Update Chat History Chunk\n", "@workflow.chunk(\n", "    chunk_id = \"Update Chat History\",\n", "    handles = {\n", "        \"inputs\": [\n", "            { \"handle\": \"user_input\" },\n", "            { \"handle\": \"assistant_reply\" },\n", "        ],\n", "    },\n", ")\n", "def update_chat_history_executor(inputs, storage):\n", "    chat_history = storage.get(\"chat_history\") or []\n", "    chat_history.append({ \"role\": \"user\", \"content\": inputs[\"user_input\"] })\n", "    chat_history.append({ \"role\": \"assistant\", \"content\": inputs[\"assistant_reply\"] })\n", "    storage.set(\"chat_history\", chat_history)\n", "    return\n", "\n", "\n", "@workflow.chunk(\n", "    chunk_id = \"Goodbye\",\n", ")\n", "def goodbye_executor(inputs, storage):\n", "    print(\"Bye~👋\")\n", "    return\n", "\n", "# Step 2. Connect Chunks in Orders\n", "# using workflow.chunks[\"<chunk_id>\"] instead of chunk variable\n", "workflow.chunks[\"Start\"].connect_to(workflow.chunks[\"User Input\"])\n", "(\n", "    workflow.chunks[\"User Input\"].handle(\"user_input\")\n", "        .if_condition(lambda data: data == \"#exit\").connect_to(workflow.chunks[\"Goodbye\"])\n", "        .else_condition().connect_to(workflow.chunks[\"Assistant Reply\"].handle(\"user_input\"))\n", ")\n", "workflow.chunks[\"User Input\"].handle(\"user_input\").connect_to(workflow.chunks[\"Update Chat History\"].handle(\"user_input\"))\n", "workflow.chunks[\"Assistant Reply\"].handle(\"assistant_reply\").connect_to(workflow.chunks[\"Update Chat History\"].handle(\"assistant_reply\"))\n", "workflow.chunks[\"Update Chat History\"].connect_to(workflow.chunks[\"User Input\"])\n", "\n", "# Step 3. Start Workflow\n", "workflow.start()"]}, {"cell_type": "markdown", "source": ["As you can see, the code works just fine as the code in last showcase did.\n", "\n", "如您所见，上面这段修改过的代码和前一个案例中的代码运行效果一样好。"], "metadata": {"id": "q69ONOvQ_xlG"}}, {"cell_type": "markdown", "metadata": {"id": "IT3pSaO2NgkG"}, "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}