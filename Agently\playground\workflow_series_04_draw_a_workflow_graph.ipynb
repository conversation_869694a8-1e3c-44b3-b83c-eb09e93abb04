{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/workflow_series_04_draw_a_workflow_graph.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "dAzfqHDCAXZe"}, "source": ["# Draw a Workflow Graph to Help You Observe the Workflow`#Agently_Workflow_Showcase_Series - 04`"]}, {"cell_type": "markdown", "metadata": {"id": "kyLFmv_l-aIx"}, "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Series Link**:\n", "\n", "[Last Document]: [03 - Using Decorator to Create <PERSON><PERSON> Faster](https://github.com/Maplemx/Agently/blob/main/playground/workflow_series_03_using_decorator_to_create_chunks.ipynb)\n", "\n", "[Next Document]: [05 - Seperating Generation Steps and Reply to User in Every Step](https://github.com/Maplemx/Agently/blob/main/playground/workflow_series_05_seperating_generation_steps_and_reply_in_every_step.ipynb)\n", "\n", "**Description:**\n", "\n", "Now you have already learned how to use **Agently Workflow** to manage your LLMs application workflow. In this showcase, it is the one last trick.\n", "\n", "\"How can I check if the workflow I designed in code is correctly planned?\" To have a graph to help your observation can be much better then reading the code, especially when there're too many chunks and connections.\n", "\n", "No worried. **Agently Workflow** can generate mermaid code by using `workflow.draw()` method to help you observe your own workflow planning.\n", "\n", "现在你已经学会如何使用**Agently Workflow**来管理你的LLMs应用的工作流程了，只剩下最后一个小技巧。\n", "\n", "“我该怎么检查我在代码里实现的工作流逻辑是否正确？”尤其是当开发者在进行拥有大量流程块和连接关系的复杂工作流的时候，这个问题会更加突出。如果能够通过可视化图形的方式来帮助开发者来进行观察就好了。\n", "\n", "不用担心，**Agently Workflow**可以通过`workflow.draw()`的方法来生成一段mermaid语法的代码，再通过mermaid渲染工具，就可以帮助你用可视化图形的方式来观察自己的工作流规划啦。"]}, {"cell_type": "markdown", "metadata": {"id": "nRsfMu4lAJZF"}, "source": ["## Step 1: Install Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nsst7pOAANlr"}, "outputs": [], "source": ["!pip install -q -U Agently"]}, {"cell_type": "markdown", "metadata": {"id": "diFJhvd3ORd1"}, "source": ["> ℹ️ Agently Workflow is a new feature in version >= `3.2.0`"]}, {"cell_type": "markdown", "metadata": {"id": "_-1gryYwASPM"}, "source": ["## Step 2: Using `workflow.draw()` to generate mermaid code and visualize it!"]}, {"cell_type": "markdown", "source": ["### Same Code in Showcase 3 without `workflow.start()`"], "metadata": {"id": "eeM97ZUGitlr"}}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "b267bc25-e91a-484d-93d2-72c26ab770e3"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<Agently.Workflow.Chunk.SchemaChunk at 0x7ae70ec09e70>"]}, "metadata": {}, "execution_count": 9}], "source": ["import Agently\n", "agent_factory = (\n", "        Agently.AgentFactory()\n", "        .set_settings(\"model.Google.auth.api_key\", \"\")\n", "        .set_settings(\"current_model\", \"Google\")\n", ")\n", "\n", "# Step 0. Create Workflow Instance and Agent Instance\n", "workflow = Agently.Workflow()\n", "agent = agent_factory.create_agent()\n", "\n", "# Step 1. C<PERSON> <PERSON><PERSON>\n", "## Start Chunk\n", "@workflow.chunk(\n", "    chunk_id = \"Start\",\n", "    type = \"Start\"\n", ")\n", "\n", "## User Input Chunk\n", "@workflow.chunk(\n", "    chunk_id = \"User Input\",\n", "    handles = {\n", "        \"outputs\": [{ \"handle\": \"user_input\" }],\n", "    }\n", ")\n", "def user_input_executor(inputs, storage):\n", "    return { \"user_input\": input(\"[User]: \") }\n", "\n", "## Assistant <PERSON><PERSON>\n", "@workflow.chunk(\n", "    chunk_id = \"Assistant Reply\",\n", "    handles = {\n", "        \"inputs\": [{ \"handle\": \"user_input\" }],\n", "        \"outputs\": [{ \"handle\": \"assistant_reply\" }],\n", "    }\n", ")\n", "def assistant_reply_executor(inputs, storage):\n", "    chat_history = storage.get(\"chat_history\") or []\n", "    reply = (\n", "        agent\n", "            .chat_history(chat_history)\n", "            .input(inputs[\"user_input\"])\n", "            .start()\n", "    )\n", "    print(\"[Assistant]: \", reply)\n", "    return { \"assistant_reply\": reply }\n", "\n", "## Update Chat History Chunk\n", "@workflow.chunk(\n", "    chunk_id = \"Update Chat History\",\n", "    handles = {\n", "        \"inputs\": [\n", "            { \"handle\": \"user_input\" },\n", "            { \"handle\": \"assistant_reply\" },\n", "        ],\n", "    },\n", ")\n", "def update_chat_history_executor(inputs, storage):\n", "    chat_history = storage.get(\"chat_history\") or []\n", "    chat_history.append({ \"role\": \"user\", \"content\": inputs[\"user_input\"] })\n", "    chat_history.append({ \"role\": \"assistant\", \"content\": inputs[\"assistant_reply\"] })\n", "    storage.set(\"chat_history\", chat_history)\n", "    return\n", "\n", "\n", "@workflow.chunk(\n", "    chunk_id = \"Goodbye\",\n", ")\n", "def goodbye_executor(inputs, storage):\n", "    print(\"Bye~👋\")\n", "    return\n", "\n", "# Step 2. Connect Chunks in Orders\n", "workflow.chunks[\"Start\"].connect_to(workflow.chunks[\"User Input\"])\n", "(\n", "    workflow.chunks[\"User Input\"].handle(\"user_input\")\n", "        .if_condition(lambda data: data == \"#exit\").connect_to(workflow.chunks[\"Goodbye\"])\n", "        .else_condition().connect_to(workflow.chunks[\"Assistant Reply\"].handle(\"user_input\"))\n", ")\n", "workflow.chunks[\"User Input\"].handle(\"user_input\").connect_to(workflow.chunks[\"Update Chat History\"].handle(\"user_input\"))\n", "workflow.chunks[\"Assistant Reply\"].handle(\"assistant_reply\").connect_to(workflow.chunks[\"Update Chat History\"].handle(\"assistant_reply\"))\n", "workflow.chunks[\"Update Chat History\"].connect_to(workflow.chunks[\"User Input\"])"]}, {"cell_type": "markdown", "source": ["### Now let's generate mermaid code and visuallize it!"], "metadata": {"id": "q69ONOvQ_xlG"}}, {"cell_type": "code", "source": ["# We can use `mermaid-python` package to render mermaid code\n", "# So install the package first\n", "!pip install mermaid-python"], "metadata": {"id": "Yr7lwlsNGPRU"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Check the mermaid code of the workflow first\n", "mermaid_code = workflow.draw()\n", "print(\"[Mermaid Code]: \\n\", mermaid_code)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AvqAojhhGlHC", "outputId": "22a2fd59-b51f-4055-be90-5bf5855f7e74"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Mermaid Code]: \n", " %%{ init: { 'flowchart': { 'curve': 'linear' }, 'theme': 'neutral' } }%%\n", "%% Rendered By Agently %%\n", "flowchart LR\n", "    f9024ca5-18d9-44a8-9377-11f0e23c097a(\"Start\") -.-> |\"output -->-- input\"| a5971148-6f84-488f-821a-89653b5a6941(\"User Input\")\n", "    a5971148-6f84-488f-821a-89653b5a6941(\"User Input\") -.-> |\"user_input -- ◇ -- input\"| faf731fe-50f8-4074-adbf-7f3900812a68(\"Goodbye\")\n", "    a5971148-6f84-488f-821a-89653b5a6941(\"User Input\") -.-> |\"user_input -- ◇ -- user_input\"| f3540386-0f51-47da-8fde-5ec8d6b21bc7(\"Assistant Reply\")\n", "    a5971148-6f84-488f-821a-89653b5a6941(\"User Input\") -.-> |\"user_input -->-- user_input\"| 45500531-d427-4227-9cdd-e7b3f7f1aaaf(\"Update Chat History\")\n", "    f3540386-0f51-47da-8fde-5ec8d6b21bc7(\"Assistant Reply\") -.-> |\"assistant_reply -->-- assistant_reply\"| 45500531-d427-4227-9cdd-e7b3f7f1aaaf(\"Update Chat History\")\n", "    45500531-d427-4227-9cdd-e7b3f7f1aaaf(\"Update Chat History\") -.-> |\"output -->-- input\"| a5971148-6f84-488f-821a-89653b5a6941(\"User Input\")\n"]}]}, {"cell_type": "code", "source": ["# Using `mermain-python` to render the code\n", "from mermaid import Mermaid\n", "Mermaid(mermaid_code)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 236}, "id": "eJXAx-tVHCy0", "outputId": "1d86af41-d902-49fb-fb82-ea1cb8fb1f4d"}, "execution_count": 12, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<mermaid.mermaid.Mermaid at 0x7ae71ce66860>"], "text/html": ["\n", "        <div class=\"mermaid-83406b98-62ac-475d-bd91-363efb17994a\"></div>\n", "        <script type=\"module\">\n", "            import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10.1.0/+esm'\n", "            const graphDefinition = '%%{ init: { \"flowchart\": { \"curve\": \"linear\" }, \"theme\": \"neutral\" } }%%\\n%% Rendered By Agently %%\\nflowchart LR\\n    f9024ca5-18d9-44a8-9377-11f0e23c097a(\"Start\") -.-> |\"output -->-- input\"| a5971148-6f84-488f-821a-89653b5a6941(\"User Input\")\\n    a5971148-6f84-488f-821a-89653b5a6941(\"User Input\") -.-> |\"user_input -- ◇ -- input\"| faf731fe-50f8-4074-adbf-7f3900812a68(\"Goodbye\")\\n    a5971148-6f84-488f-821a-89653b5a6941(\"User Input\") -.-> |\"user_input -- ◇ -- user_input\"| f3540386-0f51-47da-8fde-5ec8d6b21bc7(\"Assistant Reply\")\\n    a5971148-6f84-488f-821a-89653b5a6941(\"User Input\") -.-> |\"user_input -->-- user_input\"| 45500531-d427-4227-9cdd-e7b3f7f1aaaf(\"Update Chat History\")\\n    f3540386-0f51-47da-8fde-5ec8d6b21bc7(\"Assistant Reply\") -.-> |\"assistant_reply -->-- assistant_reply\"| 45500531-d427-4227-9cdd-e7b3f7f1aaaf(\"Update Chat History\")\\n    45500531-d427-4227-9cdd-e7b3f7f1aaaf(\"Update Chat History\") -.-> |\"output -->-- input\"| a5971148-6f84-488f-821a-89653b5a6941(\"User Input\")';\n", "            const element = document.querySelector('.mermaid-83406b98-62ac-475d-bd91-363efb17994a');\n", "            const { svg } = await mermaid.render('graphDiv-83406b98-62ac-475d-bd91-363efb17994a', graphDefinition);\n", "            element.innerHTML = svg;\n", "        </script>\n", "        "]}, "metadata": {}, "execution_count": 12}]}, {"cell_type": "markdown", "metadata": {"id": "IT3pSaO2NgkG"}, "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}