"""
使用标准输入输出和HTTP两种方式连接MCP服务器

"""
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import asyncio
from mcp.client.streamable_http import streamablehttp_client

# Math server HTTP URL
math_server_url = "http://localhost:8000/mcp"

# Math Server Parameters for stdio
server_params = StdioServerParameters(
    command="python",
    args=["mcp/math_mcp_server.py"],
    env=None,
)

async def test_mcp_session(session):
    """测试MCP会话的通用函数"""
    await session.initialize()

    # List available prompts
    response = await session.list_prompts()
    print("\n==========//prompts============")
    for prompt in response.prompts:
        print(prompt)

    # List available resources
    response = await session.list_resources()
    print("\n==========//resources============")
    for resource in response.resources:
        print(resource)

    # List available resource templates
    response = await session.list_resource_templates()
    print("\n==========//resource_templates============")
    for resource_template in response.resourceTemplates:
        print(resource_template)

    # List available tools
    response = await session.list_tools()
    print("\n==========//tools============")
    for tool in response.tools:
        print(tool)

    # Get a prompt
    prompt = await session.get_prompt("example_prompt", arguments={"question": "what is 2+2"})
    print("\n==========//prompt============")
    print(prompt.messages[0].content.text)

    # Read a resource
    content, mime_type = await session.read_resource("greeting://Alice")
    print("\n==========//content============")
    print(mime_type[1][0].text)

    # Call a tool
    result = await session.call_tool("add", arguments={"a": 2, "b": 2})
    print("\n==========//result============")
    print(result.content[0].text)

async def main_stdio():
    """使用stdio方式连接MCP服务器"""
    print("=== 使用 STDIO 方式连接 MCP 服务器 ===")
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await test_mcp_session(session)

async def main_http():
    """使用HTTP方式连接MCP服务器"""
    print("=== 使用 HTTP 方式连接 MCP 服务器 ===")
    async with streamablehttp_client(math_server_url) as (read, write, _):
        async with ClientSession(read, write) as session:
            await test_mcp_session(session)

async def main():
    """主函数，可以选择使用stdio或http方式"""
    import sys
    input= '1'
    if input == '1':
    # if len(sys.argv) > 1 and sys.argv[1] == "http":
        await main_http()
    else:
        await main_stdio()

if __name__ == "__main__":
    asyncio.run(main())