# from mcp.server.fastmcp import FastMCP
from fastmcp import FastMCP
mcp = FastMCP("BMI")

# Tools
@mcp.tool()
def calculate_bmi(weight: int, height: int) -> str:
    """Calculate BMI"""
    return "BMI: "+str(weight/(height*height))

if __name__ == "__main__":
    mcp.run(transport="streamable-http", port=8001)
    
    
"""
客户端实现
只需一行代码（指定连接到服务端的方式），即可创建 MCP 客户端：
"""
# async def main():
#     # 测试 mcp 客户端的功能
#     async with Client("http://127.0.0.1:8001/sse") as mcp_client:
#         tools = await mcp_client.list_tools()
#         print(f"Available tools: {tools}")
#         result = await mcp_client.call_tool("add", {"a": 5, "b": 3})
#         print(f"Result: {result[0].text}")