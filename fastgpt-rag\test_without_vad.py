#!/usr/bin/env python3

"""
不使用VAD的说话人识别测试
"""
import argparse
import sys
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Tuple

import numpy as np
import sherpa_onnx
import soundfile as sf
import sounddevice as sd

def get_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--speaker-file", type=str, required=True)
    parser.add_argument("--model", type=str, required=True)
    parser.add_argument("--threshold", type=float, default=0.6)
    parser.add_argument("--num-threads", type=int, default=1)
    parser.add_argument("--debug", type=bool, default=False)
    parser.add_argument("--provider", type=str, default="cpu")
    return parser.parse_args()

def load_speaker_embedding_model(args):
    config = sherpa_onnx.SpeakerEmbeddingExtractorConfig(
        model=args.model,
        num_threads=args.num_threads,
        debug=args.debug,
        provider=args.provider,
    )
    if not config.validate():
        raise ValueError(f"Invalid config. {config}")
    extractor = sherpa_onnx.SpeakerEmbeddingExtractor(config)
    return extractor

def load_speaker_file(args) -> Dict[str, List[str]]:
    if not Path(args.speaker_file).is_file():
        raise ValueError(f"--speaker-file {args.speaker_file} does not exist")

    ans = defaultdict(list)
    with open(args.speaker_file, encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            fields = line.split()
            if len(fields) != 2:
                raise ValueError(f"Invalid line: {line}. Fields: {fields}")
            speaker_name, filename = fields
            ans[speaker_name].append(filename)
    return ans

def load_audio(filename: str) -> Tuple[np.ndarray, int]:
    data, sample_rate = sf.read(filename, always_2d=True, dtype="float32")
    data = data[:, 0]  # use only the first channel
    samples = np.ascontiguousarray(data)
    return samples, sample_rate

def compute_speaker_embedding(filenames: List[str], extractor: sherpa_onnx.SpeakerEmbeddingExtractor) -> np.ndarray:
    assert len(filenames) > 0, "filenames is empty"
    ans = None
    for filename in filenames:
        print(f"处理文件: {filename}")
        samples, sample_rate = load_audio(filename)
        stream = extractor.create_stream()
        stream.accept_waveform(sample_rate=sample_rate, waveform=samples)
        stream.input_finished()
        assert extractor.is_ready(stream)
        embedding = extractor.compute(stream)
        embedding = np.array(embedding)
        if ans is None:
            ans = embedding
        else:
            ans += embedding
    return ans / len(filenames)

def main():
    args = get_args()
    print("🚀 开始不使用VAD的说话人识别测试")
    
    # 加载模型
    print("🔄 加载说话人嵌入模型...")
    extractor = load_speaker_embedding_model(args)
    print("✓ 模型加载成功")
    
    # 加载说话人文件
    print("🔄 加载说话人文件...")
    speaker_file = load_speaker_file(args)
    print(f"✓ 加载了 {len(speaker_file)} 个说话人")
    
    # 注册说话人
    print("🔄 注册说话人...")
    manager = sherpa_onnx.SpeakerEmbeddingManager(extractor.dim)
    for name, filename_list in speaker_file.items():
        print(f"注册说话人: {name}")
        embedding = compute_speaker_embedding(filenames=filename_list, extractor=extractor)
        status = manager.add(name, embedding)
        if not status:
            raise RuntimeError(f"Failed to register speaker {name}")
        print(f"✓ {name} 注册成功")
    
    print("✓ 所有说话人注册完成")
    
    # 测试音频设备
    print("🔄 测试音频设备...")
    devices = sd.query_devices()
    print(f"✓ 找到 {len(devices)} 个音频设备")
    
    if len(devices) == 0:
        print("❌ 没有找到音频设备")
        return
    
    for i, device in enumerate(devices):
        input_channels = device.get('max_input_channels', 0)
        if input_channels > 0:
            print(f"  {i}: {device['name']} (输入通道: {input_channels})")
    
    default_input_device_idx = sd.default.device[0]
    print(f"🎤 使用默认设备: {devices[default_input_device_idx]['name']}")
    
    # 简单的录音和识别（不使用VAD）
    print("🎙️ 开始录音，每3秒识别一次，按Ctrl+C停止...")
    
    g_sample_rate = 16000
    duration = 3  # 每次录音3秒
    
    idx = 0
    try:
        while True:
            print(f"\n第 {idx + 1} 次录音，请说话...")
            
            # 录音3秒
            recording = sd.rec(int(duration * g_sample_rate), 
                             samplerate=g_sample_rate, 
                             channels=1, 
                             dtype='float32')
            sd.wait()  # 等待录音完成
            
            # 转换为一维数组
            samples = recording.reshape(-1)
            
            # 计算嵌入向量
            stream = extractor.create_stream()
            stream.accept_waveform(sample_rate=g_sample_rate, waveform=samples)
            stream.input_finished()
            
            if extractor.is_ready(stream):
                embedding = extractor.compute(stream)
                embedding = np.array(embedding)
                name = manager.search(embedding, threshold=args.threshold)
                if not name:
                    name = "未知说话人"
                print(f"识别结果: {name}")
            else:
                print("音频太短，无法识别")
            
            idx += 1
            
    except KeyboardInterrupt:
        print("\n👋 检测到 Ctrl+C，退出程序")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()