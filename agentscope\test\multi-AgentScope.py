from agentscope.agents import DialogAgent
from agentscope.message import Msg
from agentscope.pipelines import sequential_pipeline
from agentscope import msghub
import agentscope

# 加载模型配置
agentscope.init(
    model_configs=[
        {
            "config_name": "my_config",
            "model_type": "openai_chat",
            "model_name": "doubao-1-5-pro-32k-250115",
            "api_key": "5dd64d02-d0b4-48fd-91f0-d4a91f359456",
            "client_args": {"base_url": "https://ark.cn-beijing.volces.com/api/v3/"},
            "generate_args": {"temperature": 0.5},
        }
    ]
)

# 创建三个智能体
friday = DialogAgent(
    name="Friday", model_config_name="my_config", sys_prompt="你是一个名为Friday的助手"
)

saturday = DialogAgent(
    name="Saturday",
    model_config_name="my_config",
    sys_prompt="你是一个名为Saturday的助手",
)

sunday = DialogAgent(
    name="Sunday", model_config_name="my_config", sys_prompt="你是一个名为Sunday的助手"
)

# 通过msghub创建一个聊天室，智能体的消息会广播给所有参与者
with msghub(
    participants=[friday, saturday, sunday],
    announcement=Msg(
        "user", "从1开始数数，每次只报一个数字，不要说其他内容", "user"
    ),  # 一个问候消息
) as hub:
    # 按顺序发言
    sequential_pipeline([friday, saturday, sunday], x=None)
