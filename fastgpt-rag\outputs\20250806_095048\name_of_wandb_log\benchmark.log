2025-08-06 09:50:50,893 - evalscope - INFO - Starting benchmark with args: 
2025-08-06 09:50:50,893 - evalscope - INFO - {
    "model": "modelscope.cn/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF",
    "model_id": "Qwen3-30B-A3B-Instruct-2507-GGUF",
    "attn_implementation": null,
    "api": "openai",
    "tokenizer_path": null,
    "port": 8877,
    "url": "http://192.168.10.120:11434/v1/chat/completions",
    "headers": {},
    "connect_timeout": 600,
    "read_timeout": 600,
    "api_key": null,
    "no_test_connection": false,
    "number": 10,
    "parallel": 1,
    "rate": -1,
    "sleep_interval": 5,
    "log_every_n_query": 10,
    "debug": false,
    "wandb_api_key": null,
    "swanlab_api_key": "TAQWFItPpzVdQpOuXQKCU",
    "name": "name_of_wandb_log",
    "outputs_dir": "./outputs\\20250806_095048\\name_of_wandb_log\\parallel_1_number_10",
    "max_prompt_length": 2048,
    "min_prompt_length": 10,
    "prefix_length": 0,
    "prompt": null,
    "query_template": null,
    "apply_chat_template": true,
    "image_width": 224,
    "image_height": 224,
    "image_format": "RGB",
    "image_num": 1,
    "dataset": "openqa",
    "dataset_path": null,
    "frequency_penalty": null,
    "repetition_penalty": null,
    "logprobs": null,
    "max_tokens": 2048,
    "min_tokens": 50,
    "n_choices": null,
    "seed": 0,
    "stop": null,
    "stop_token_ids": null,
    "stream": true,
    "temperature": 0.0,
    "top_p": null,
    "top_k": null,
    "extra_args": {
        "ignore_eos": true
    }
}
2025-08-06 09:50:51,075 - evalscope - INFO - Test connection successful.
2025-08-06 09:50:52,481 - evalscope - INFO - Save the data base to: ./outputs\20250806_095048\name_of_wandb_log\parallel_1_number_10\benchmark_data.db
2025-08-06 09:52:52,482 - evalscope - INFO - {
  "Time taken for tests (s)": 119.9964,
  "Number of concurrency": 1,
  "Total requests": 10,
  "Succeed requests": 10,
  "Failed requests": 0,
  "Output token throughput (tok/s)": 85.7026,
  "Total token throughput (tok/s)": 88.111,
  "Request throughput (req/s)": 0.0833,
  "Average latency (s)": 11.9967,
  "Average time to first token (s)": 0.1515,
  "Average time per output token (s)": 0.0115,
  "Average inter-token latency (s)": 0.0119,
  "Average input tokens per request": 28.9,
  "Average output tokens per request": 1028.4
}
2025-08-06 09:52:52,569 - evalscope - INFO - 
Benchmarking summary:
+-----------------------------------+-----------+
| Key                               |     Value |
+===================================+===========+
| Time taken for tests (s)          |  119.996  |
+-----------------------------------+-----------+
| Number of concurrency             |    1      |
+-----------------------------------+-----------+
| Total requests                    |   10      |
+-----------------------------------+-----------+
| Succeed requests                  |   10      |
+-----------------------------------+-----------+
| Failed requests                   |    0      |
+-----------------------------------+-----------+
| Output token throughput (tok/s)   |   85.7026 |
+-----------------------------------+-----------+
| Total token throughput (tok/s)    |   88.111  |
+-----------------------------------+-----------+
| Request throughput (req/s)        |    0.0833 |
+-----------------------------------+-----------+
| Average latency (s)               |   11.9967 |
+-----------------------------------+-----------+
| Average time to first token (s)   |    0.1515 |
+-----------------------------------+-----------+
| Average time per output token (s) |    0.0115 |
+-----------------------------------+-----------+
| Average inter-token latency (s)   |    0.0119 |
+-----------------------------------+-----------+
| Average input tokens per request  |   28.9    |
+-----------------------------------+-----------+
| Average output tokens per request | 1028.4    |
+-----------------------------------+-----------+
2025-08-06 09:52:52,576 - evalscope - INFO - 
Percentile results:
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
| Percentiles | TTFT (s) | ITL (s) | TPOT (s) | Latency (s) | Input tokens | Output tokens | Output (tok/s) | Total (tok/s) |
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
|     10%     |  0.1295  | 0.0075  |  0.0112  |   7.6873    |      20      |      683      |    83.5179     |    85.4806    |
|     25%     |  0.1426  | 0.0101  |  0.0115  |    8.214    |      23      |      703      |    85.2541     |    86.9834    |
|     50%     |  0.1519  | 0.0115  |  0.0115  |   10.2788   |      29      |      882      |    85.8076     |    88.419     |
|     66%     |  0.163   | 0.0125  |  0.0115  |   11.5654   |      31      |      986      |    85.9415     |    89.4072    |
|     75%     |  0.1647  | 0.0132  |  0.0116  |   14.7753   |      32      |     1234      |    86.7981     |    89.4811    |
|     80%     |  0.1665  | 0.0138  |  0.0119  |   16.6392   |      37      |     1430      |    87.7281     |    90.7713    |
|     90%     |  0.1667  | 0.0164  |  0.012   |   23.595    |      41      |     2048      |    88.8478     |    94.1813    |
|     95%     |  0.1667  | 0.0201  |  0.012   |   23.595    |      41      |     2048      |    88.8478     |    94.1813    |
|     98%     |  0.1667  | 0.0255  |  0.012   |   23.595    |      41      |     2048      |    88.8478     |    94.1813    |
|     99%     |  0.1667  | 0.0316  |  0.012   |   23.595    |      41      |     2048      |    88.8478     |    94.1813    |
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
2025-08-06 09:52:52,578 - evalscope - INFO - Save the summary to: ./outputs\20250806_095048\name_of_wandb_log\parallel_1_number_10
2025-08-06 09:52:52,578 - evalscope - INFO - Sleeping for 5 seconds before the next run...
2025-08-06 09:52:57,590 - evalscope - INFO - Starting benchmark with args: 
2025-08-06 09:52:57,591 - evalscope - INFO - {
    "model": "modelscope.cn/unsloth/Qwen3-30B-A3B-Instruct-2507-GGUF",
    "model_id": "Qwen3-30B-A3B-Instruct-2507-GGUF",
    "attn_implementation": null,
    "api": "openai",
    "tokenizer_path": null,
    "port": 8877,
    "url": "http://192.168.10.120:11434/v1/chat/completions",
    "headers": {},
    "connect_timeout": 600,
    "read_timeout": 600,
    "api_key": null,
    "no_test_connection": false,
    "number": 20,
    "parallel": 10,
    "rate": -1,
    "sleep_interval": 5,
    "log_every_n_query": 10,
    "debug": false,
    "wandb_api_key": null,
    "swanlab_api_key": "TAQWFItPpzVdQpOuXQKCU",
    "name": "name_of_wandb_log",
    "outputs_dir": "./outputs\\20250806_095048\\name_of_wandb_log\\parallel_10_number_20",
    "max_prompt_length": 2048,
    "min_prompt_length": 10,
    "prefix_length": 0,
    "prompt": null,
    "query_template": null,
    "apply_chat_template": true,
    "image_width": 224,
    "image_height": 224,
    "image_format": "RGB",
    "image_num": 1,
    "dataset": "openqa",
    "dataset_path": "C:\\Users\\<USER>\\.cache\\modelscope\\hub\\datasets\\AI-ModelScope\\HC3-Chinese\\open_qa.jsonl",
    "frequency_penalty": null,
    "repetition_penalty": null,
    "logprobs": null,
    "max_tokens": 2048,
    "min_tokens": 50,
    "n_choices": null,
    "seed": 0,
    "stop": null,
    "stop_token_ids": null,
    "stream": true,
    "temperature": 0.0,
    "top_p": null,
    "top_k": null,
    "extra_args": {
        "ignore_eos": true
    }
}
2025-08-06 09:52:57,716 - evalscope - INFO - Test connection successful.
2025-08-06 09:52:57,740 - evalscope - INFO - Save the data base to: ./outputs\20250806_095048\name_of_wandb_log\parallel_10_number_20\benchmark_data.db
2025-08-06 09:55:00,572 - evalscope - INFO - {
  "Time taken for tests (s)": 122.827,
  "Number of concurrency": 10,
  "Total requests": 10,
  "Succeed requests": 10,
  "Failed requests": 0,
  "Output token throughput (tok/s)": 73.0865,
  "Total token throughput (tok/s)": 75.4394,
  "Request throughput (req/s)": 0.0814,
  "Average latency (s)": 75.4516,
  "Average time to first token (s)": 51.468,
  "Average time per output token (s)": 0.0267,
  "Average inter-token latency (s)": 0.0281,
  "Average input tokens per request": 28.9,
  "Average output tokens per request": 897.7
}
2025-08-06 09:56:55,285 - evalscope - INFO - {
  "Time taken for tests (s)": 237.5378,
  "Number of concurrency": 10,
  "Total requests": 20,
  "Succeed requests": 20,
  "Failed requests": 0,
  "Output token throughput (tok/s)": 76.1563,
  "Total token throughput (tok/s)": 78.6191,
  "Request throughput (req/s)": 0.0842,
  "Average latency (s)": 91.5427,
  "Average time to first token (s)": 68.9085,
  "Average time per output token (s)": 0.0259,
  "Average inter-token latency (s)": 0.0262,
  "Average input tokens per request": 29.25,
  "Average output tokens per request": 904.5
}
2025-08-06 09:56:55,385 - evalscope - INFO - 
Benchmarking summary:
+-----------------------------------+----------+
| Key                               |    Value |
+===================================+==========+
| Time taken for tests (s)          | 237.538  |
+-----------------------------------+----------+
| Number of concurrency             |  10      |
+-----------------------------------+----------+
| Total requests                    |  20      |
+-----------------------------------+----------+
| Succeed requests                  |  20      |
+-----------------------------------+----------+
| Failed requests                   |   0      |
+-----------------------------------+----------+
| Output token throughput (tok/s)   |  76.1563 |
+-----------------------------------+----------+
| Total token throughput (tok/s)    |  78.6191 |
+-----------------------------------+----------+
| Request throughput (req/s)        |   0.0842 |
+-----------------------------------+----------+
| Average latency (s)               |  91.5427 |
+-----------------------------------+----------+
| Average time to first token (s)   |  68.9085 |
+-----------------------------------+----------+
| Average time per output token (s) |   0.0259 |
+-----------------------------------+----------+
| Average inter-token latency (s)   |   0.0262 |
+-----------------------------------+----------+
| Average input tokens per request  |  29.25   |
+-----------------------------------+----------+
| Average output tokens per request | 904.5    |
+-----------------------------------+----------+
2025-08-06 09:56:55,395 - evalscope - INFO - 
Percentile results:
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
| Percentiles | TTFT (s) | ITL (s) | TPOT (s) | Latency (s) | Input tokens | Output tokens | Output (tok/s) | Total (tok/s) |
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
|     10%     | 13.0522  | 0.0145  |  0.026   |   48.8498   |      21      |      489      |     5.6259     |    5.8864     |
|     25%     | 54.8486  | 0.0232  |  0.0261  |   84.7521   |      26      |      688      |     6.744      |    7.1687     |
|     50%     | 84.0105  |  0.026  |  0.0266  |   99.1897   |      28      |      824      |     9.0942     |    9.4892     |
|     66%     | 88.4926  | 0.0274  |  0.0268  |  107.0449   |      31      |      928      |    13.3448     |    13.687     |
|     75%     | 90.7467  | 0.0284  |  0.0269  |  114.7072   |      34      |     1127      |     14.759     |    15.4348    |
|     80%     | 92.8889  | 0.0294  |  0.0269  |  118.2659   |      37      |     1131      |    17.8542     |    18.0808    |
|     90%     | 104.5521 | 0.0335  |  0.0272  |  126.2934   |      41      |     1806      |    36.9705     |    37.4413    |
|     95%     | 105.413  | 0.0405  |  0.0278  |  127.8183   |      45      |     2048      |    37.7243     |    39.2672    |
|     98%     | 105.413  | 0.0549  |  0.0278  |  127.8183   |      45      |     2048      |    37.7243     |    39.2672    |
|     99%     | 105.413  | 0.0763  |  0.0278  |  127.8183   |      45      |     2048      |    37.7243     |    39.2672    |
+-------------+----------+---------+----------+-------------+--------------+---------------+----------------+---------------+
2025-08-06 09:56:55,396 - evalscope - INFO - Save the summary to: ./outputs\20250806_095048\name_of_wandb_log\parallel_10_number_20
