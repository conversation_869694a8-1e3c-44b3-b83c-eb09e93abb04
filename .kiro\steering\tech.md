# Technology Stack

## Primary Language
- **Python 3.10+** - Main development language for all components

## Core Frameworks & Libraries

### Agently Framework
- **Agently 3.5.1.2** - Core AI agent development framework
- **OpenAI SDK** - LLM integration
- **LiteLLM** - Multi-model support
- **httpx** - HTTP client for API calls
- **PyYAML** - Configuration management
- **json5** - Enhanced JSON parsing

### CrewAI Stack
- **CrewAI** - Multi-agent collaboration framework
- **Lang<PERSON>hain** - LLM application framework
- **FastAPI** - Web framework for API services

### MCP (Model Context Protocol)
- **mcp** - Model Context Protocol implementation
- **FastAPI** - Server framework for MCP services

### Additional Dependencies
- **boto3** - AWS SDK integration
- **requests** - HTTP library
- **beautifulsoup4** - Web scraping
- **duckduckgo-search** - Search functionality

## Build System
- **Poetry** - Dependency management and packaging
- **pyproject.toml** - Project configuration

## Common Commands

### Environment Setup
```bash
# Install dependencies
pip install -U agently
pip install crewai

# For development
poetry install
```

### Running Examples
```bash
# Run Agently examples
python tests/mcp-demo.py

# Run CrewAI examples
python crewai/business-plan.py
python crewai/code-agent.py

# Run MCP servers
python mcp/mcp-test/mcp-server.py
```

### Testing MCP
```bash
# Start MCP server
python mcp/mcp-test/bmi_mcp_server.py

# Test MCP client
python mcp/mcp-test/mcp-client.py
```

## Configuration Notes
- API keys should be set via environment variables
- Model endpoints are configurable via settings
- MCP servers run on localhost with custom ports