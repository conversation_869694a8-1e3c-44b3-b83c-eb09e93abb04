{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/workflow_series_01_building_a_multi_round_chat.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "dAzfqHDCAXZe"}, "source": ["# Let's Get Started by Building a Multi-Round Chat with Agently Workflow! `#Agently_Workflow_Showcase_Series - 01`"]}, {"cell_type": "markdown", "metadata": {"id": "kyLFmv_l-aIx"}, "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Series Link**:\n", "\n", "[Next Document]: [02 - Using Condition to Branch Off](https://github.com/Maplemx/Agently/blob/main/playground/workflow_series_02_using_condition_to_branch_off.ipynb)\n", "\n", "**Description:**\n", "\n", "We're glad to introduce a brand new feature of **_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** v3.2 to you all: **`Agently Workflow`**!\n", "\n", "With this new feature, you can arrange and manage your LLMs based application workflow **in just 3 steps, simple and easy:**\n", "\n", "1. Define and program your application logic into different `workflow chunks`;\n", "2. Connect chunks using `chunk.connect_to()` in orders; (Loop and condition judugment supported)\n", "3. Start the workflow using `workflow.start()`.\n", "\n", "This showcase is the first one of the **`Agently Workflow Showcase Series`**, more showcases will coming soon.\n", "\n", "In this case, we will show you how to use **`Agently Workflow`** to create a multi-round chat workflow:\n", "\n", "我们非常高兴能够向您介绍**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** v3.2版本推出的全新功能：**`Agently Workflow`**！\n", "\n", "使用这项全新的功能，只需要一二三步走，您就能够轻松惬意地编排管理您的语言模型应用工作流了：\n", "\n", "1. 在`workflow chunks`切块中编写您的应用工作流中的单块工作逻辑（如输入、判断、请求执行、数据存取等）；\n", "2. 使用`chunk.connect_to()`方法将切块按您想要的工作顺序进行连接（支持环状连接、条件分支等复杂连接关系）；\n", "3. 通过`workflow.start()`开始运行工作流。\n", "\n", "本案例是**`Agently Workflow系列案例`**的第一个案例，后续我们还将更新更多相关案例。\n", "\n", "在本案例中，我们将向您演示如何使用**Agently Workflow**功能创建一个多轮对话的工作流："]}, {"cell_type": "markdown", "metadata": {"id": "nRsfMu4lAJZF"}, "source": ["## Step 1: Install Packages"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nsst7pOAANlr"}, "outputs": [], "source": ["!pip install -q -U Agently"]}, {"cell_type": "markdown", "metadata": {"id": "diFJhvd3ORd1"}, "source": ["> ℹ️ Agently Workflow is a new feature in version >= `3.2.0`"]}, {"cell_type": "markdown", "metadata": {"id": "_-1gryYwASPM"}, "source": ["## Step 2: Create a Multi-Round Chat Workflow in the Basic Way"]}, {"cell_type": "markdown", "source": ["### Workflow Graph\n", "\n", "<img width = \"640\" src = \"https://github.com/Maplemx/Agently/assets/4413155/6da2539d-afd8-4b57-9af8-0135e6afd61d\"></img>"], "metadata": {"id": "NxQAiPsTiZ4S"}}, {"cell_type": "markdown", "source": ["### Code"], "metadata": {"id": "eeM97ZUGitlr"}}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "59483f72-ea6f-4cfd-d82b-4092cba81cf1"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["User Input\n", "User Input\n", "[User]: <PERSON>, my name is <PERSON>\n", "[Assistant]:  <PERSON>, how can I help you today?\n", "[User]: Tell me one tip of Python\n", "[Assistant]:  **Tip:** Use a type hinting system to improve code readability and maintainability.\n", "\n", "Python supports type hinting through the `typing` module, allowing you to specify the expected types of function arguments and return values. This helps catch potential type errors at compile time and improves collaboration by making it clear what data types are expected. Example:\n", "\n", "```python\n", "from typing import List\n", "\n", "def my_function(numbers: List[int]) -> int:\n", "    \"\"\"\n", "    Calculate the sum of a list of integers.\n", "\n", "    Args:\n", "        numbers (List[int]): The list of integers to sum.\n", "\n", "    Returns:\n", "        int: The sum of the integers in the list.\n", "    \"\"\"\n", "    return sum(numbers)\n", "```\n", "[User]: Tell me one tip of JAVA\n", "[Assistant]:  **Tip:** Utilize the `switch` statement for cleaner and more concise conditional logic.\n", "\n", "Java 14 introduced the `switch` expression, which allows you to write more readable and maintainable code when dealing with multiple conditions. Instead of using a series of `if-else` statements, you can use a `switch` statement to evaluate a variable against multiple cases and execute the corresponding code block. Example:\n", "\n", "```java\n", "String result;\n", "switch (fruit) {\n", "    case \"apple\":\n", "        result = \"An apple a day keeps the doctor away.\";\n", "        break;\n", "    case \"banana\":\n", "        result = \"Bananas are a good source of potassium.\";\n", "        break;\n", "    case \"orange\":\n", "        result = \"Oranges are rich in vitamin C.\";\n", "        break;\n", "    default:\n", "        result = \"I don't know anything about that fruit.\";\n", "}\n", "```\n", "[User]: what did we talk about?\n", "[Assistant]:  We talked about programming tips in Python and Java:\n", "\n", "**Python:**\n", "\n", "* Use type hinting to improve code readability and maintainability.\n", "\n", "**Java:**\n", "\n", "* Utilize the `switch` statement for cleaner and more concise conditional logic.\n", "\n", "Is there anything else I can help you with today?\n"]}], "source": ["import Agently\n", "import logging\n", "\n", "agent_factory = (\n", "        Agently.AgentFactory()\n", "        .set_settings(\"model.Google.auth.api_key\", \"\")\n", "        .set_settings(\"current_model\", \"Google\")\n", ")\n", "\n", "# Step 0. Create Workflow Instance and Agent Instance\n", "workflow = Agently.Workflow()\n", "# reset logger level to WARNING\n", "workflow.settings.set(\"logger\", logging.getLogger(\"Workflow\").setLevel(logging.WARNING))\n", "agent = agent_factory.create_agent()\n", "\n", "# Step 1. C<PERSON> <PERSON><PERSON>\n", "## Start Chunk\n", "start_chunk = workflow.schema.create_chunk(\n", "    title = \"Multi-Round Chat\",\n", "    type = \"Start\"\n", ")\n", "\n", "## User Input Chunk\n", "user_input_chunk = workflow.schema.create_chunk(\n", "    title = \"User Input\",\n", "    handles = {\n", "        \"outputs\": [{ \"handle\": \"user_input\" }],\n", "    },\n", "    executor = lambda inputs, storage: {\n", "        \"user_input\": input(\"[User]: \")#<- You can change it to an API/WS request\n", "    },# You can use a lambda function as an executor\n", ")\n", "\n", "## Assistant <PERSON><PERSON>\n", "# You can also define an normal executor function to handle more complex logic\n", "# get data from input handles by `inputs[\"<handle name>\"]`\n", "# get data from cache storage by `storage.get(\"<key>\")`\n", "# save data to cache storage by `storage.save(\"<key>\", <value>)`\n", "def assistant_reply_executor(inputs, storage):\n", "    chat_history = storage.get(\"chat_history\") or []\n", "    reply = (\n", "        agent\n", "            .chat_history(chat_history)\n", "            .input(inputs[\"user_input\"])\n", "            .start()\n", "    )\n", "    print(\"[Assistant]: \", reply)\n", "    return { \"assistant_reply\": reply }\n", "assistant_reply_chunk = workflow.schema.create_chunk(\n", "    title = \"Assistant Reply\",\n", "    handles = {\n", "        \"inputs\": [{ \"handle\": \"user_input\" }],\n", "        \"outputs\": [{ \"handle\": \"assistant_reply\" }],\n", "    },\n", "    executor = assistant_reply_executor,\n", ")\n", "\n", "## Update Chat History Chunk\n", "def update_chat_history_executor(inputs, storage):\n", "    chat_history = storage.get(\"chat_history\") or []\n", "    chat_history.append({ \"role\": \"user\", \"content\": inputs[\"user_input\"] })\n", "    chat_history.append({ \"role\": \"assistant\", \"content\": inputs[\"assistant_reply\"] })\n", "    storage.set(\"chat_history\", chat_history)\n", "    return\n", "update_chat_history_chunk = workflow.schema.create_chunk(\n", "    title = \"Update Chat History\",\n", "    handles = {\n", "        \"inputs\": [\n", "            { \"handle\": \"user_input\" },\n", "            { \"handle\": \"assistant_reply\" },\n", "        ],\n", "    },\n", "    executor = update_chat_history_executor,\n", ")\n", "\n", "# Step 2. Connect Chunks in Orders\n", "start_chunk.connect_to(user_input_chunk)\n", "user_input_chunk.handle(\"user_input\").connect_to(assistant_reply_chunk.handle(\"user_input\"))\n", "user_input_chunk.handle(\"user_input\").connect_to(update_chat_history_chunk.handle(\"user_input\"))\n", "assistant_reply_chunk.handle(\"assistant_reply\").connect_to(update_chat_history_chunk.handle(\"assistant_reply\"))\n", "## Created a loop by connect tail chunk to head chunk\n", "update_chat_history_chunk.connect_to(user_input_chunk)\n", "\n", "# Step 3. Start Workflow\n", "workflow.start()"]}, {"cell_type": "markdown", "metadata": {"id": "IT3pSaO2NgkG"}, "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"]}], "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}