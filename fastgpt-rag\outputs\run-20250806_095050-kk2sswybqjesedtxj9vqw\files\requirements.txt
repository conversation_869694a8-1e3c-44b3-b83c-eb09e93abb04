absl-py==2.3.1
accelerate==1.9.0
addict==2.4.0
agentscope==0.1.6
agentUniverse==0.0.18
aiofiles==23.2.1
aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiolimiter==1.2.1
aiosignal==1.4.0
alabaster==0.7.16
annotated-types==0.7.0
anthropic==0.26.1
antlr4-python3-runtime==4.9.3
anyio==4.9.0
argon2-cffi==25.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asgiref==3.9.1
asttokens==3.0.0
async-lru==2.0.5
async-timeout==4.0.3
attrs==25.3.0
Authlib==1.6.1
babel==2.17.0
backoff==2.2.1
bce-python-sdk==0.9.39
bcrypt==4.3.0
beautifulsoup4==4.13.4
bidict==0.23.1
black==25.1.0
bleach==6.2.0
blinker==1.9.0
blis==0.7.11
bm25s==0.1.10
boto3==1.40.3
botocore==1.40.3
build==1.2.2.post1
cachetools==5.5.2
catalogue==2.0.10
certifi==2022.12.7
cffi==1.17.1
charset-normalizer==2.1.1
chroma-hnswlib==0.7.3
chromadb==0.4.24
click==8.2.1
cloudpathlib==0.21.1
cloudpickle==3.1.1
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
confection==0.1.5
contourpy==1.3.2
cryptography==45.0.5
cycler==0.12.1
cyclopts==3.22.2
cymem==2.0.11
dashscope==1.23.9
dataclasses-json==0.6.7
datasets==3.2.0
debugpy==1.8.15
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docling==2.42.1
docling-core==2.43.0
docstring_parser==0.16
docutils==0.20.1
docx2txt==0.9
dotenv==0.9.9
duckduckgo_search==6.3.5
durationpy==0.10
EbookLib==0.19
einops==0.8.1
email_validator==2.2.0
et_xmlfile==2.0.0
eval_type_backport==0.2.2
evalscope==0.17.1
exa-py==1.14.18
exceptiongroup==1.3.0
executing==2.2.0
fastapi==0.116.1
fastjsonschema==2.21.1
fastmcp==2.10.6
ffmpy==0.6.0
filelock==3.13.1
flake8==7.3.0
Flask==2.3.3
Flask-Cors==4.0.0
Flask-SQLAlchemy==3.1.1
flatbuffers==25.2.10
fonttools==4.58.0
fqdn==1.5.1
frozenlist==1.7.0
fsspec==2024.9.0
future==1.0.0
google-auth==2.40.3
googleapis-common-protos==1.70.0
gradio==5.4.0
gradio_client==1.4.2
greenlet==3.2.3
groovy==0.1.2
grpcio==1.63.0
gunicorn==22.0.0
h11==0.16.0
html2text==2025.4.15
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.1
huggingface-hub==0.33.4
humanfriendly==10.0
idna==3.4
imagesize==1.4.1
immutabledict==4.2.1
importlib_metadata==8.4.0
importlib_resources==6.5.2
inputimeout==1.0.4
ipykernel==6.30.0
ipython==8.37.0
ipywidgets==8.1.7
isoduration==20.11.0
itsdangerous==2.2.0
jedi==0.19.2
jieba==0.42.1
Jinja2==3.1.4
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
json5==0.9.28
jsonlines==4.0.0
jsonpatch==1.33
jsonpointer==3.0.0
jsonref==1.1.0
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter==1.1.1
jupyter_client==8.6.3
jupyter-console==6.6.3
jupyter_core==5.8.1
jupyter-events==0.12.0
jupyter-lsp==2.2.6
jupyter_server==2.16.0
jupyter_server_terminals==0.5.3
jupyterlab==4.4.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.15
kiwisolver==1.4.8
kubernetes==33.1.0
langchain==0.1.20
langchain-anthropic==0.1.13
langchain-community==0.0.38
langchain-core==0.1.52
langchain-openai==0.3.28
langchain-text-splitters==0.0.2
langcodes==3.5.0
langdetect==1.0.9
langgraph==0.5.3
langgraph-checkpoint==2.1.1
langgraph-prebuilt==0.5.2
langgraph-sdk==0.1.73
langsmith==0.1.147
language_data==1.3.0
latex2mathml==3.78.0
latex2sympy2_extended==1.10.2
lazyllm==0.5.3
loguru==0.7.2
lxml==6.0.0
marisa-trie==1.2.1
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.26.1
matplotlib==3.10.3
matplotlib-inline==0.1.7
mccabe==0.7.0
mcp==1.9.4
mdit-py-plugins==0.4.2
mdurl==0.1.2
mistune==3.1.3
mmh3==5.1.0
modelscope==1.28.0
modelscope_studio==1.1.7
mpire==2.10.2
mpmath==1.3.0
ms-agent==1.0.0
multidict==6.6.3
multiprocess==0.70.16
murmurhash==1.0.13
mypy_extensions==1.1.0
myst-parser==2.0.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.3
nltk==3.9.1
notebook==7.4.4
notebook_shim==0.2.4
numpy==1.26.4
nvidia-ml-py==12.575.51
oauthlib==3.3.1
olefile==0.47
ollama==0.2.1
omegaconf==2.3.0
onnxruntime==1.22.1
openai==1.55.3
openapi-pydantic==0.5.1
openpyxl==3.1.5
opentelemetry-api==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-exporter-otlp-proto-http==1.27.0
opentelemetry-instrumentation==0.48b0
opentelemetry-instrumentation-asgi==0.48b0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.48b0
opentracing==2.4.0
orjson==3.11.0
ormsgpack==1.10.0
overrides==7.7.0
packaging==23.2
pandas==2.3.1
pandocfilters==1.5.1
parso==0.8.4
pathspec==0.12.1
pdfminer.six==20250506
pdfplumber==0.11.7
pillow==10.4.0
pip==25.1
platformdirs==4.3.8
plotly==5.24.1
portalocker==3.2.0
posthog==6.1.1
preshed==3.0.10
prettytable==3.16.0
primp==0.6.5
prometheus_client==0.22.1
prompt_toolkit==3.0.51
propcache==0.3.2
protobuf==4.25.8
psutil==6.1.1
psycopg2==2.9.10
psycopg2-binary==2.9.10
pulsar-client==3.8.0
pure_eval==0.2.3
pyarrow==16.1.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.14.0
pycparser==2.22
pycryptodome==3.23.0
pydantic==2.11.7
pydantic_core==2.33.2
pydantic-settings==2.10.1
pydub==0.25.1
pyecharts==2.0.8
pyflakes==3.4.0
Pygments==2.19.2
PyJWT==2.10.1
pymilvus==2.5.13
pynvml==12.0.0
pyparsing==3.2.3
pypdf==5.8.0
pypdfium2==4.30.0
pyperclip==1.9.0
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
PyStemmer==*******
python-dateutil==2.9.0.post0
python-docx==1.2.0
python-dotenv==1.1.1
python-engineio==4.12.2
python-json-logger==3.3.0
python-multipart==0.0.12
python-pptx==1.0.2
python-socketio==5.13.0
pytz==2025.2
pywin32==311
pywinpty==2.0.15
PyYAML==6.0.2
pyzmq==27.0.0
qianfan==0.3.18
qwen-agent==0.0.27
rank-bm25==0.2.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.9.4
rich-rst==1.3.1
rouge-chinese==1.0.3
rouge_score==0.1.2
rpds-py==0.26.0
rsa==4.9.1
ruff==0.12.4
s3transfer==0.13.1
sacrebleu==2.5.1
safehttpx==0.1.6
safetensors==0.5.3
scikit-learn==1.7.1
scipy==1.15.3
seaborn==0.13.2
semantic-version==2.10.0
semchunk==2.2.2
Send2Trash==1.8.3
sentencepiece==0.2.0
setuptools==78.1.1
shellingham==1.5.4
shortuuid==1.0.13
simple-websocket==1.1.0
simplejson==3.20.1
six==1.17.0
smart_open==7.3.0.post1
sniffio==1.3.1
snowballstemmer==3.0.1
sortedcontainers==2.4.0
soupsieve==2.7
spacy==3.7.5
spacy-legacy==3.0.12
spacy-loggers==1.0.5
Sphinx==7.4.7
sphinx-rtd-theme==2.0.0
sphinxcontrib-applehelp==2.0.0
sphinxcontrib-devhelp==2.0.0
sphinxcontrib-htmlhelp==2.1.0
sphinxcontrib-jquery==4.1
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==2.0.0
sphinxcontrib-serializinghtml==2.0.0
SQLAlchemy==2.0.25
srsly==2.5.1
sse-starlette==2.4.1
stack-data==0.6.3
starlette==0.47.1
swankit==0.2.4
swanlab==0.6.8
sympy==1.13.3
tabulate==0.9.0
tenacity==8.5.0
terminado==0.18.1
thinc==8.2.5
threadpoolctl==3.6.0
tiktoken==0.9.0
tinycss2==1.4.0
tokenizers==0.21.2
toml==0.10.2
tomli==2.2.1
tomlkit==0.12.0
torch==2.0.0+cpu
torchaudio==2.0.1+cpu
torchvision==0.15.1+cpu
tornado==6.5.1
tqdm==4.67.1
traitlets==5.14.3
transformers==4.53.3
typer==0.16.0
types-python-dateutil==2.9.0.20250708
typing_extensions==4.12.2
typing-inspect==0.9.0
typing-inspection==0.4.1
tzdata==2025.2
ujson==5.10.0
uri-template==1.3.0
urllib3==2.5.0
uvicorn==0.23.2
wasabi==1.1.3
watchfiles==1.1.0
wcwidth==0.2.13
weasel==0.4.1
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==12.0
Werkzeug==3.1.3
wheel==0.45.1
widgetsnbextension==4.0.14
wikipedia==1.4.0
win32_setctime==1.2.0
word2number==1.1
wrapt==1.17.2
wsproto==1.2.0
xlsxwriter==3.2.5
xxhash==3.5.0
yarl==1.20.1
zipp==3.23.0
zstandard==0.23.0
