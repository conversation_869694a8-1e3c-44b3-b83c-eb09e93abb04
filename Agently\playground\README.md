## **_<font color = "red">Agent</font><font color = "blue">ly</font>_ Playground**

We'll present latest Agently framework powered application showcases here and you're more than welcome to share your showcases with us. [[Share You Case Here](https://github.com/AgentEra/Agently/issues/new)]

### Read Development Handbook

[[Agently 3.0 Application Development Handbook](https://github.com/AgentEra/Agently/blob/main/docs/guidebook/application_development_handbook.ipynb)]

### Start Your Own Test Run

You can quickly start your own test using these jupyter document template:

[[Quick Start Template in English](https://github.com/AgentEra/Agently/blob/main/playground/test_run_template.ipynb)] | [[中文版快速试用模板](https://github.com/AgentEra/Agently/blob/main/playground/%E7%BA%BF%E4%B8%8A%E5%BF%AB%E9%80%9F%E8%AF%95%E7%94%A8%E6%A8%A1%E6%9D%BF.ipynb)]

### Latest Show Cases
- **Agently Workflow Series**:
  - [01 - Let's Get Started by Building a Multi-Round Chat with Agently Workflow!](https://github.com/AgentEra/Agently/blob/main/playground/workflow_series_01_building_a_multi_round_chat.ipynb)
  - [02 - Using Condition to Branch Off](https://github.com/AgentEra/Agently/blob/main/playground/workflow_series_02_using_condition_to_branch_off.ipynb)
  - [03 - Using Decorator to Create Chunks Faster](https://github.com/AgentEra/Agently/blob/main/playground/workflow_series_03_using_decorator_to_create_chunks.ipynb)
  - [04 - Draw a Workflow Graph to Help You Observe the Workflow](https://github.com/AgentEra/Agently/blob/main/playground/workflow_series_04_draw_a_workflow_graph.ipynb)
  - [05 - Seperating Generation Steps and Reply to User in Every Step](https://github.com/AgentEra/Agently/blob/main/playground/workflow_series_05_seperating_generation_steps_and_reply_in_every_step.ipynb)
- **[How to launch local open-source model to drive agent? ](https://github.com/AgentEra/Agently/blob/main/playground/using_local_open_source_model_to_drive_agents.ipynb)_<font color = "red">Agent</font><font color = "blue">ly</font>_ x Xinference**`🆕`
- **"一句话就能..."Showcase合集** [[飞桨社区中文版](https://aistudio.baidu.com/projectdetail/7439200)]
- **[How to create event listener functions with alias or decorator? `using ZhipuAI GLM-4!`](https://github.com/AgentEra/Agently/blob/main/playground/create_event_listeners_with_alias_or_decorator.ipynb)** 
- **[Summon a Genie 🧞‍♂️ (Function Decorator) to Generate Agent Powered Function in Runtime](https://github.com/AgentEra/Agently/blob/main/playground/generate_agent_powered_function_in_runtime_using_decorator.ipynb)** `💪 New Feature in v3.1.4`
- **[How to let your agents use tools to enhance themselves?](https://github.com/AgentEra/Agently/blob/main/playground/using_tools_to_enhance_your_agent.ipynb)** `💪 New Feature in v3.1`
- **[How to use AsyncIO and Agently to Manage Complex Process with Concurrency and Asynchronous Dependencies](https://github.com/AgentEra/Agently/blob/main/playground/concurrency_and_asynchornous_dependency.ipynb)**
- **[Prediction according Given Data Set: GPT-3.5-turbo-1106 vs Gemini Pro](https://github.com/AgentEra/Agently/blob/main/playground/predict_data_according_given_data_set.ipynb)** 
- **[How to use GOOGLE GEMINI to generate line and choices for NPC in game](https://github.com/AgentEra/Agently/blob/main/playground/NPC_in_game_generate_choices_using_google_gemini.ipynb)**
- **[How to route user to different agents according different Authorities](https://github.com/AgentEra/Agently/blob/main/playground/routing_to_different_agent_group_for_users_with_different_authorities.ipynb)**
- **Agently快速上手案例集** [[飞桨社区合作中文版](https://aistudio.baidu.com/projectdetail/7178289)] `🔥 HOT`
- **[How to find connectable pairs from data collection of text headers and tails](https://github.com/AgentEra/Agently/blob/main/playground/finding_connectable_pairs_from_text_tailers_and_headers.ipynb)** 
- **[How to create a LLM powered character that can change behaviors according mood status in chat](https://github.com/AgentEra/Agently/blob/main/playground/character_change_behaviours_according_mood_status.ipynb)**
- **[How to create an agent help you to write ad copies according an image](https://github.com/AgentEra/Agently/blob/main/playground/writing_ad_copies_according_image.ipynb)**
- **[How to create an agent to interview customer according survey form](https://github.com/AgentEra/Agently/blob/main/playground/survey_agent_asks_questions_according_form.ipynb)**
- **[How to recheck, confirm and rewrite LLM agent's response before them sent to user?](https://github.com/AgentEra/Agently/blob/main/playground/human_step_in_before_reply.ipynb)**
- **[How to convert long text to question-answer pairs?](https://github.com/AgentEra/Agently/blob/main/playground/long_text_to_qa_pairs.ipynb)**
- **[How to create an agent help you transform natural language to SQL?](https://github.com/AgentEra/Agently/blob/main/playground/sql_generator.ipynb)** `🔥 HOT`

---

**_<font color = "red">Agent</font><font color = "blue">ly</font>_ Framework** - Speed Up Your AI Agent Native Application Development
