#!/usr/bin/env python3
"""
图片服务器启动脚本
"""

import sys
import logging
from pathlib import Path

try:
    import uvicorn
    from fastapi import FastAPI
except ImportError:
    print("错误: 缺少必要的依赖包")
    print("请运行: pip install -r requirements.txt")
    sys.exit(1)

from config import get_config
from image_server import app, image_service

def setup_logging(config):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def create_test_images():
    """创建测试图片（如果需要）"""
    test_dir = Path("./test_images")
    test_dir.mkdir(exist_ok=True)
    
    # 创建一个简单的测试图片
    try:
        from PIL import Image
        
        # 创建一个简单的测试图片
        test_image_path = test_dir / "test_image.png"
        if not test_image_path.exists():
            img = Image.new('RGB', (200, 200), color='lightblue')
            img.save(test_image_path)
            print(f"创建测试图片: {test_image_path}")
        
        # 创建一个模拟您格式的文件
        hash_image_path = test_dir / "02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c"
        if not hash_image_path.exists():
            img = Image.new('RGB', (300, 300), color='lightgreen')
            img.save(hash_image_path, 'JPEG')
            print(f"创建哈希格式测试图片: {hash_image_path}")
            
    except ImportError:
        print("警告: 无法导入PIL，跳过创建测试图片")

def main():
    """主函数"""
    config = get_config()
    
    try:
        config.validate_config()
    except ValueError as e:
        print(f"配置错误: {e}")
        sys.exit(1)
    
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    # 检查图片目录
    images_dir = config.get_images_directory()
    if not images_dir.exists():
        logger.warning(f"图片目录不存在: {images_dir}")
        create_test_images()
    
    logger.info("=" * 50)
    logger.info("图片HTTP服务器启动")
    logger.info("=" * 50)
    logger.info(f"服务地址: http://{config.HOST}:{config.PORT}")
    logger.info(f"图片目录: {images_dir}")
    logger.info(f"API文档: http://{config.HOST}:{config.PORT}/docs")
    logger.info(f"健康检查: http://{config.HOST}:{config.PORT}/health")
    logger.info("=" * 50)
    
    # 显示可用的API端点
    print("\n可用的API端点:")
    print(f"  GET  /                     - 服务信息")
    print(f"  GET  /images/{{filename}}   - 获取图片文件")
    print(f"  GET  /api/images/{{filename}} - 获取图片信息")
    print(f"  GET  /api/images           - 列出所有图片")
    print(f"  GET  /static/{{filename}}   - 静态文件访问")
    print(f"  GET  /health               - 健康检查")
    print(f"  GET  /docs                 - API文档")
    
    # 显示示例URL
    print(f"\n示例访问URL:")
    print(f"  http://{config.HOST}:{config.PORT}/images/02b9abc858aa34b1e8b2af78396781de7530980bb2d19736308befb26e58239c")
    print(f"  http://{config.HOST}:{config.PORT}/api/images")
    print(f"  http://{config.HOST}:{config.PORT}/docs")
    
    try:
        uvicorn.run(
            "image_server:app",
            host=config.HOST,
            port=config.PORT,
            log_level=config.LOG_LEVEL.lower(),
            access_log=True,
            reload=False
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
