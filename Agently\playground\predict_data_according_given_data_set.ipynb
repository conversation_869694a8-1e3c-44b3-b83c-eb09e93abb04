{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNJwr1Hne65sp7GUJ3kyYDI", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/predict_data_according_given_data_set.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Predict Data according Given Data Set"], "metadata": {"id": "P3ofE6eDIu7L"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** English\n", "\n", "**Agent Components:** None\n", "\n", "**Description:**\n", "\n", "As we know, LLMs are based on transformer algorithm, which predict next token according all tokens in context no matter they were given by input or generated during the output process. So we have a wondering about can LLM predict new data pairs according given data set?\n", "\n", "Here's the experiment we designed to compare the performance of GPT-3.5-turbo-1106 and Gemini Pro in predicting data pairs using Agently framework. (Of course you can try other models by changing settings)\n", "\n", "我们知道，语言模型基于transformer算法，这种算法会根据上下文给出的信息去推断下一个token，这里所说的上下文既包括原始输入的内容，也包括在输出过程中产生的内容。这样的概率计算过程，让我不禁好奇，它能不能对具有一定模式的数据集里的数据对进行预测呢？\n", "\n", "于是我们设计了下面这个试验，来比较GPT-3.5-turbo-1106和Gemini-Pro在数据对预测上的能力。当然，因为使用Agently框架进行支持，这个过程非常简单，你也可以通过修改Agently设置，去快速切换和试验更多其他模型。"], "metadata": {"id": "Ju7_KD5v_u34"}}, {"cell_type": "markdown", "source": ["## Step 0. Install Agently Package\n"], "metadata": {"id": "DuO11IcGDNx1"}}, {"cell_type": "code", "source": ["!pip install -U -q Agently"], "metadata": {"id": "EbVzREbzvCHM"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 1. Prepare Data Set"], "metadata": {"id": "b66fTP79CYVT"}}, {"cell_type": "markdown", "source": ["At the first Step, we generated a data set of quadratic equations with noise interference using random numbers.\n", "\n", "首先，我们随机生成一个带有噪音干扰的一元二次方程数据集。"], "metadata": {"id": "pq4gnIv7CdSx"}}, {"cell_type": "code", "source": ["# Prepare Data Set\n", "import numpy as np\n", "\n", "# y = w1 * x^2 + w2 * x + b + noise\n", "num = 20\n", "w1 = 2.5\n", "w2 = 3.4\n", "b = 5.2\n", "x_range = (-30.0, 30.0)\n", "noise_range = (-1, 1)\n", "def generate_data_set(num, w1, w2, b, x_range, noise_range):\n", "    data_set = []\n", "    for _ in range(num):\n", "        x = np.random.normal(x_range[0], x_range[1])\n", "        noise = np.random.normal(noise_range[0], noise_range[1])\n", "        y = pow(x, 2) * w1 + x * w2 + b + noise\n", "        data_set.append([round(x, 3), round(y, 3)])\n", "    return data_set\n", "\n", "data_set = generate_data_set(num, w1, w2, b, x_range, noise_range)\n", "print(data_set)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A_nJy17u3eRr", "outputId": "af49c9f7-618d-4207-c071-adf21e9655f8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[-35.532, 3038.298], [-17.121, 680.106], [8.396, 208.122], [3.274, 42.058], [-26.864, 1716.939], [13.493, 504.117], [-83.563, 17176.494], [-32.002, 2455.563], [-40.589, 3985.282], [-100.041, 24683.111], [-7.932, 133.835], [-7.828, 129.65], [-42.116, 4294.806], [0.536, 7.818], [-49.47, 5954.044], [-23.008, 1250.231], [-54.995, 7377.244], [-37.655, 3422.295], [-14.601, 487.485], [-9.397, 194.43]]\n"]}]}, {"cell_type": "markdown", "source": ["## Step 2. GPT-3.5-turbo-1106 Test"], "metadata": {"id": "yWdBb_nFDZ1x"}}, {"cell_type": "markdown", "source": ["### 2.1 Create Agent"], "metadata": {"id": "ytsSN_wwDnn8"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "itQE4v-iUh5C"}, "outputs": [], "source": ["import Agently\n", "\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"model.OpenAI.auth.api_key\", \"\")\n", "        .set_settings(\"model.OpenAI.options\", { \"model\": \"gpt-3.5-turbo-1106\" })\n", ")\n", "\n", "agent = agent_factory.create_agent()"]}, {"cell_type": "markdown", "source": ["### 2.2 Generate Prediction"], "metadata": {"id": "ib9PnPrODtUc"}}, {"cell_type": "code", "source": ["# Predict\n", "result = (\n", "    agent\n", "        .input({ \"data_set\": data_set })\n", "        .output({\n", "            \"prediction\": (\n", "                [(\"list\", \"number pair.\")],\n", "                \"predict 10 OTHER possible number pairs according {input.data_set}'s pattern\"\n", "            )\n", "        })\n", "        .start()\n", ")\n", "prediction = result[\"prediction\"]\n", "print(prediction)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zszM1B64vvgI", "outputId": "859ebb36-89ca-4dde-ff33-459c01ff09d8"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[-100.678, 25619.105], [-93.259, 18604.163], [-61.356, 7839.939], [-50.907, 8209.86], [-47.112, 4709.604], [-20.681, 1956.488], [-5.439, 90.569], [10.658, 669.728], [24.538, 1457.046], [54.731, 9167.92]]\n"]}]}, {"cell_type": "markdown", "source": ["### 2.3 Draw Plot"], "metadata": {"id": "kF1qtKqUD-Bk"}}, {"cell_type": "code", "source": ["# Draw\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(22, 15))\n", "plt.title(\"result\")\n", "plt.xlabel(\"X\")\n", "plt.ylabel(\"Y\")\n", "data_set_x, data_set_y = zip(*data_set)\n", "plt.scatter(data_set_x, data_set_y, c=\"green\")\n", "pred_x, pred_y = zip(*prediction)\n", "plt.scatter(pred_x, pred_y, c=\"blue\")\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 618}, "id": "F7TwIwQ46gu3", "outputId": "12a8624f-d338-4996-84c8-c6f4decbde37"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 2200x1500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["### 2.4 <PERSON><PERSON><PERSON>"], "metadata": {"id": "i21zhNMeEGkm"}}, {"cell_type": "code", "source": ["# <PERSON><PERSON>\n", "result = (\n", "    agent\n", "        .input({\n", "            \"origin_data_set\": data_set,\n", "            \"your_prediction\": prediction,\n", "        })\n", "        .output({\n", "            \"patterns\": (\"String\", \"Explain patterns you discovered from {origin_data_set}\")\n", "        })\n", "        .start()\n", ")\n", "print(\"[Patterns]\", result[\"patterns\"])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bhzORMOk8KPl", "outputId": "e061bf95-eafe-43c2-c0e4-9fc3abab1965"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Patterns] The origin_data_set shows a negative correlation between the first and second values, with the second value increasing as the first value decreases.\n"]}]}, {"cell_type": "markdown", "source": ["## Step 3. Gemini Pro Test"], "metadata": {"id": "4zC6ZnunEODP"}}, {"cell_type": "markdown", "source": ["### 3.1 Create Agent"], "metadata": {"id": "ho0NhE9YEqNK"}}, {"cell_type": "code", "source": ["import Agently\n", "\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"current_model\", \"Google\")\n", "        .set_settings(\"model.Google.auth.api_key\", \"\")\n", ")\n", "\n", "agent = agent_factory.create_agent()"], "metadata": {"id": "9Vm_lgzgEUay"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["### 3.2 Generate Prediction"], "metadata": {"id": "NGMLtxXcEu2c"}}, {"cell_type": "code", "source": ["# Predict\n", "result = (\n", "    agent\n", "        .input({ \"data_set\": data_set })\n", "        .output({\n", "            \"prediction\": (\n", "                [(\"list\", \"number pair.\")],\n", "                \"predict 10 OTHER possible number pairs according {input.data_set}'s pattern\"\n", "            )\n", "        })\n", "        .start()\n", ")\n", "prediction = result[\"prediction\"]\n", "print(prediction)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qi6YYm34E0r7", "outputId": "eebedd5c-ea3e-41e5-dec5-2933d2e63738"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[[-2.608, 29.023], [-29.07, 1995.173], [-57.224, 8008.143], [-45.604, 5146.876], [-75.811, 13371.15], [-14.295, 465.285], [-1.021, 10.873], [-3.746, 47.945], [-52.155, 6542.789], [-86.795, 18427.52]]\n"]}]}, {"cell_type": "markdown", "source": ["### 3.3 Draw Plot"], "metadata": {"id": "Eh1FJh9zE1pd"}}, {"cell_type": "code", "source": ["# Draw\n", "import matplotlib.pyplot as plt\n", "\n", "plt.figure(figsize=(22, 15))\n", "plt.title(\"result\")\n", "plt.xlabel(\"X\")\n", "plt.ylabel(\"Y\")\n", "data_set_x, data_set_y = zip(*data_set)\n", "plt.scatter(data_set_x, data_set_y, c=\"green\")\n", "pred_x, pred_y = zip(*prediction)\n", "plt.scatter(pred_x, pred_y, c=\"blue\")\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 618}, "id": "h3kkPO3rE4qp", "outputId": "23689a41-014c-4b57-ccee-975639f0c9de"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 2200x1500 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAABwYAAATYCAYAAAAVjKOHAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABtWklEQVR4nOzdfZTWdZ3/8dfFEHjHjJoBgzOm601qkW5qyGnHn24cIK1kRzLTSktzbcEFKVPLzM7a2uJpF1zvTvtHtLvZqpzRVk1aji4yJd5kcszbrY4K6KCmMaNU3sxcvz84XDmCCTrMNfB5PM65DnN9v2+u631d/z7P93tVqtVqNQAAAAAAAMA2bVi9FwAAAAAAAAC2PGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAICt1oIFC1KpVPL444/XexUAAIAhTxgEAABgm3LFFVdkwYIF9V4DAABgyBEGAQAA2KYIgwAAABsnDAIAAPC2rV27tt4rAAAA8CaEQQAAADbLhRdemEqlkoceeignnnhidtlll/zVX/1VkuQ///M/c8ghh2T77bfPrrvumhNOOCErV67s9/9/9atf5bjjjsvYsWOz3XbbpaWlJSeccEK6u7uTJI8//ngqlcpGr/qrVCq58MIL33C3PffcMw8++GBuv/32VCqVVCqVHHnkkQP10QEAALZqw+u9AAAAAFunT3ziE9l3333zj//4j6lWq/nWt76Vr3/96zn++ONz2mmn5dlnn82//uu/5ogjjsh9992XnXfeOS+//HKmTJmSl156KWeeeWbGjh2bJ598MjfddFPWrFmTpqamt7XTvHnzcuaZZ2annXbK1772tSTJmDFjBuLjAgAAbPWEQQAAAN6Sgw46KFdffXWS5Iknnsjee++diy66KF/96ldrM+3t7fnLv/zLXHHFFfnqV7+ahx56KI899liuu+66TJ8+vTZ3wQUXDMhO06ZNy/nnn5/ddtstn/70pwfkNQEAALYVbiUKAADAW3LGGWfU/u7o6EhfX1+OP/74/Pa3v609xo4dm3333Tf/+7//myS1KwJ/8pOf5Pe//31d9gYAACiVKwYBAAB4S/baa6/a37/61a9SrVaz7777bnT2He94R+3/zJkzJ//8z/+cH/zgB2lra8vHP/7xfPrTn37btxEFAADgzxMGAQAAeEu233772t99fX2pVCq55ZZb0tDQsMHsTjvtVPv7O9/5Tk455ZT86Ec/yv/8z//k7//+73PxxRfnzjvvTEtLSyqVykbfr7e3d+A/BAAAQEGEQQAAAN62vffeO9VqNXvttVf222+/N50fP358xo8fn/PPPz933HFHPvShD+Wqq67KRRddlF122SVJsmbNmn7/54knntikXd4oLAIAAJTObwwCAADwtrW3t6ehoSHf/OY3U61W+52rVqt57rnnkiQ9PT159dVX+50fP358hg0blpdeeilJ0tjYmN122y1Lly7tN3fFFVds0i477rjjBlERAAAAVwwCAAAwAPbee+9cdNFFOe+88/L4449n2rRpGTVqVB577LFcf/31Of300/PlL385t912W2bOnJlPfOIT2W+//fLqq6/mP/7jP9LQ0JDjjjuu9nqnnXZavv3tb+e0007LoYcemqVLl+b//u//NmmXQw45JFdeeWUuuuii7LPPPhk9enT++q//ekt9dAAAgK2GMAgAAMCAOPfcc7PffvvlX/7lX/LNb34zSdLa2prJkyfn4x//eJLkoIMOypQpU3LjjTfmySefzA477JCDDjoot9xySw4//PDaa11wwQV59tlns3Dhwlx77bX5yEc+kltuuSWjR49+0z0uuOCCPPHEE5k7d25eeOGF/L//9/+EQQAAgCSV6uvv8QIAAAAAAABsc/zGIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACjA8HovsK3o6+vLU089lVGjRqVSqdR7HQAAAAAAAApQrVbzwgsvZNy4cRk27M9fEygMDpCnnnoqra2t9V4DAAAAAACAAq1cuTItLS1/dkYYHCCjRo1Ksu5Lb2xsrPM2AAAAAAAAlKCnpyetra21VvXnCIMDZP3tQxsbG4VBAAAAAAAABtWm/NTdn7/RKAAAAAAAALBNEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKEBdw+DFF1+cww47LKNGjcro0aMzbdq0PProo/1mjjzyyFQqlX6PM844o9/MihUrcswxx2SHHXbI6NGjc/bZZ+fVV1/tN7NkyZJ84AMfyMiRI7PPPvtkwYIFG+xz+eWXZ88998x2222XCRMm5O677x7wzwwAAAAAAAD1UNcwePvtt2fGjBm58847s3jx4rzyyiuZPHly1q5d22/uC1/4Qrq6umqPuXPn1s719vbmmGOOycsvv5w77rgj3//+97NgwYJccMEFtZnHHnssxxxzTI466qgsX748s2fPzmmnnZaf/OQntZlrrrkmc+bMyTe+8Y384he/yEEHHZQpU6bkmWee2fJfBAAAAAAAAGxhlWq1Wq33Eus9++yzGT16dG6//fYcccQRSdZdMXjwwQdn3rx5G/0/t9xySz760Y/mqaeeypgxY5IkV111Vc4555w8++yzGTFiRM4555zcfPPNeeCBB2r/74QTTsiaNWuyaNGiJMmECRNy2GGH5bLLLkuS9PX1pbW1NWeeeWbOPffcN929p6cnTU1N6e7uTmNj49v5GgAAAAAAAGCTbE6jGlK/Mdjd3Z0k2XXXXfsd/8EPfpDddtst73vf+3Leeefl97//fe3csmXLMn78+FoUTJIpU6akp6cnDz74YG1m0qRJ/V5zypQpWbZsWZLk5Zdfzr333ttvZtiwYZk0aVJt5vVeeuml9PT09HsAAAAAAADAUDW83gus19fXl9mzZ+dDH/pQ3ve+99WOn3jiiXn3u9+dcePG5f77788555yTRx99NB0dHUmS1atX94uCSWrPV69e/Wdnenp68oc//CG/+93v0tvbu9GZRx55ZKP7XnzxxfnmN7/59j40AAAAAAAADJIhEwZnzJiRBx54ID/96U/7HT/99NNrf48fPz7Nzc358Ic/nN/85jfZe++9B3vNmvPOOy9z5sypPe/p6Ulra2vd9gEAAAAAAIA/Z0iEwZkzZ+amm27K0qVL09LS8mdnJ0yYkCT59a9/nb333jtjx47N3Xff3W/m6aefTpKMHTu29u/6Y6+daWxszPbbb5+GhoY0NDRsdGb9a7zeyJEjM3LkyE3/kAAAAAAAAFBHdf2NwWq1mpkzZ+b666/Pbbfdlr322utN/8/y5cuTJM3NzUmSiRMn5pe//GWeeeaZ2szixYvT2NiYAw88sDZz66239nudxYsXZ+LEiUmSESNG5JBDDuk309fXl1tvvbU2AwAAAAAAAFuzul4xOGPGjFx99dX50Y9+lFGjRtV+E7CpqSnbb799fvOb3+Tqq6/O0UcfnXe+8525//77c9ZZZ+WII47I+9///iTJ5MmTc+CBB+Yzn/lM5s6dm9WrV+f888/PjBkzalf0nXHGGbnsssvyla98JZ///Odz22235dprr83NN99c22XOnDk5+eSTc+ihh+aDH/xg5s2bl7Vr1+Zzn/vc4H8xAAAAAAAAMMAq1Wq1Wrc3r1Q2evx73/teTjnllKxcuTKf/vSn88ADD2Tt2rVpbW3N3/zN3+T8889PY2Njbf6JJ57IF7/4xSxZsiQ77rhjTj755Hz729/O8OF/6p5LlizJWWedlYceeigtLS35+te/nlNOOaXf+1522WW55JJLsnr16hx88MG59NJLa7cufTM9PT1pampKd3d3v90AAAAAAABgS9mcRlXXMLgtEQYBAAAAAAAYbJvTqOr6G4MAAAAAAADA4KjrbwxStt6+3nSu6EzXC11pHtWctj3a0jCsod5rAQAAAAAAbJOEQeqi4+GOzFo0K6t6VtWOtTS2ZP7U+Wk/oL2OmwEAAAAAAGyb3EqUQdfxcEemXzu9XxRMkid7nsz0a6en4+GOOm0GAAAAAACw7RIGGVS9fb2ZtWhWqqlucG79sdmLZqe3r3ewVwMAAAAAANimCYMMqs4VnRtcKfha1VSzsmdlOld0DuJWAAAAAAAA2z5hkEHV9ULXgM4BAAAAAACwaYRBBlXzqOYBnQMAAAAAAGDTCIMMqrY92tLS2JJKKhs9X0klrY2tadujbZA3AwAAAAAA2LYJgwyqhmENmT91fpJsEAfXP583dV4ahjUM+m4AAAAAAADbMmGQQdd+QHsWHr8wuzfu3u94S2NLFh6/MO0HtNdpMwAAAAAAgG1XpVqtVuu9xLagp6cnTU1N6e7uTmNjY73X2Sr09vWmc0Vnul7oSvOo5rTt0eZKQQAAAAAAgM2wOY1q+CDtBBtoGNaQI/c8st5rAAAAAAAAFMGtRAEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKUNcwePHFF+ewww7LqFGjMnr06EybNi2PPvpov5k//vGPmTFjRt75zndmp512ynHHHZenn36638yKFStyzDHHZIcddsjo0aNz9tln59VXX+03s2TJknzgAx/IyJEjs88++2TBggUb7HP55Zdnzz33zHbbbZcJEybk7rvvHvDPDAAAAAAAAPVQ1zB4++23Z8aMGbnzzjuzePHivPLKK5k8eXLWrl1bmznrrLNy44035rrrrsvtt9+ep556Ku3t7bXzvb29OeaYY/Lyyy/njjvuyPe///0sWLAgF1xwQW3mscceyzHHHJOjjjoqy5cvz+zZs3PaaaflJz/5SW3mmmuuyZw5c/KNb3wjv/jFL3LQQQdlypQpeeaZZwbnywAAAAAAAIAtqFKtVqv1XmK9Z599NqNHj87tt9+eI444It3d3XnXu96Vq6++OtOnT0+SPPLIIznggAOybNmyHH744bnlllvy0Y9+NE899VTGjBmTJLnqqqtyzjnn5Nlnn82IESNyzjnn5Oabb84DDzxQe68TTjgha9asyaJFi5IkEyZMyGGHHZbLLrssSdLX15fW1taceeaZOffcc990956enjQ1NaW7uzuNjY0D/dUAAAAAAADABjanUQ2p3xjs7u5Okuy6665JknvvvTevvPJKJk2aVJvZf//9s8cee2TZsmVJkmXLlmX8+PG1KJgkU6ZMSU9PTx588MHazGtfY/3M+td4+eWXc++99/abGTZsWCZNmlSbeb2XXnopPT09/R4AAAAAAAAwVA2ZMNjX15fZs2fnQx/6UN73vvclSVavXp0RI0Zk55137jc7ZsyYrF69ujbz2ii4/vz6c39upqenJ3/4wx/y29/+Nr29vRudWf8ar3fxxRenqamp9mhtbX1rHxwAAAAAAAAGwZAJgzNmzMgDDzyQ//qv/6r3KpvkvPPOS3d3d+2xcuXKeq8EAAAAAAAAb2h4vRdIkpkzZ+amm27K0qVL09LSUjs+duzYvPzyy1mzZk2/qwaffvrpjB07tjZz991393u9p59+unZu/b/rj712prGxMdtvv30aGhrS0NCw0Zn1r/F6I0eOzMiRI9/aBwYAAAAAAIBBVtcrBqvVambOnJnrr78+t912W/baa69+5w855JC84x3vyK233lo79uijj2bFihWZOHFikmTixIn55S9/mWeeeaY2s3jx4jQ2NubAAw+szbz2NdbPrH+NESNG5JBDDuk309fXl1tvvbU2AwAAAAAAAFuzul4xOGPGjFx99dX50Y9+lFGjRtV+z6+pqSnbb799mpqacuqpp2bOnDnZdddd09jYmDPPPDMTJ07M4YcfniSZPHlyDjzwwHzmM5/J3Llzs3r16px//vmZMWNG7Yq+M844I5dddlm+8pWv5POf/3xuu+22XHvttbn55ptru8yZMycnn3xyDj300Hzwgx/MvHnzsnbt2nzuc58b/C8GAAAAAAAABlilWq1W6/bmlcpGj3/ve9/LKaeckiT54x//mC996Uv54Q9/mJdeeilTpkzJFVdc0e8Wn0888US++MUvZsmSJdlxxx1z8skn59vf/naGD/9T91yyZEnOOuusPPTQQ2lpacnXv/712nusd9lll+WSSy7J6tWrc/DBB+fSSy/NhAkTNumz9PT0pKmpKd3d3WlsbNy8LwIAAAAAAADegs1pVHUNg9sSYRAAAAAAAIDBtjmNqq6/MQgAAAAAAAAMDmEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMPrvQDAtqC3N+nsTLq6kubmpK0taWio91YAAAAAAPAnwiDA29TRkcyalaxa9adjLS3J/PlJe3v99gIAAAAAgNdyK1GAt6GjI5k+vX8UTJInn1x3vKOjPnsBAAAAAMDrCYMAb1Fv77orBavVDc+tPzZ79ro5AAAAAACoN2EQ4C3q7NzwSsHXqlaTlSvXzQEAAAAAQL0JgwBvUVfXwM4BAAAAAMCWJAwCvEXNzQM7BwAAAAAAW5IwCPAWtbUlLS1JpbLx85VK0tq6bg4AAAAAAOpNGAR4ixoakvnz1/39+ji4/vm8eevmAAAAAACg3oRBgLehvT1ZuDDZfff+x1ta1h1vb6/PXgAAAAAA8HrD670AwNauvT059tikszPp6lr3m4Jtba4UBAAAAABgaBEGAQZAQ0Ny5JH13gIAAAAAAN6YW4kCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACjA8HovAEB99Pb1pnNFZ7pe6ErzqOa07dGWhmEN9V4LAAAAAIAtRBgEKFDHwx2ZtWhWVvWsqh1raWzJ/Knz035Aex03AwAAAABgS3ErUYDCdDzckenXTu8XBZPkyZ4nM/3a6el4uKNOmwEAAAAAsCUJgwAF6e3rzaxFs1JNdYNz64/NXjQ7vX29g70aAAAAAABbmDAIUJDOFZ0bXCn4WtVUs7JnZTpXdA7iVgAAAAAADAZhEKAgXS90DegcAAAAAABbD2EQoCDNo5oHdA4AAAAAgK2HMAhQkLY92tLS2JJKKhs9X0klrY2tadujbZA3AwAAAABgSxMGAQrSMKwh86fOT5IN4uD65/OmzkvDsIZB3w0AAAAAgC1LGAQoTPsB7Vl4/MLs3rh7v+MtjS1ZePzCtB/QXqfNAAAAAADYkirVarVa7yW2BT09PWlqakp3d3caGxvrvQ7Am+rt603nis50vdCV5lHNadujzZWCAAAAAABbmc1pVMMHaScAhpiGYQ05cs8j670GAAAAAACDxK1EAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAHUNg0uXLs3HPvaxjBs3LpVKJTfccEO/86ecckoqlUq/x9SpU/vNPP/88znppJPS2NiYnXfeOaeeempefPHFfjP3339/2trast1226W1tTVz587dYJfrrrsu+++/f7bbbruMHz8+P/7xjwf88wIAAAAAAEC91DUMrl27NgcddFAuv/zyN5yZOnVqurq6ao8f/vCH/c6fdNJJefDBB7N48eLcdNNNWbp0aU4//fTa+Z6enkyePDnvfve7c++99+aSSy7JhRdemO9+97u1mTvuuCOf+tSncuqpp+a+++7LtGnTMm3atDzwwAMD/6EBAAAAAACgDirVarVa7yWSpFKp5Prrr8+0adNqx0455ZSsWbNmgysJ13v44Ydz4IEH5p577smhhx6aJFm0aFGOPvrorFq1KuPGjcuVV16Zr33ta1m9enVGjBiRJDn33HNzww035JFHHkmSfPKTn8zatWtz00031V778MMPz8EHH5yrrrpqk/bv6elJU1NTuru709jY+Ba+AQAAAAAAANg8m9OohvxvDC5ZsiSjR4/Oe97znnzxi1/Mc889Vzu3bNmy7LzzzrUomCSTJk3KsGHDctddd9VmjjjiiFoUTJIpU6bk0Ucfze9+97vazKRJk/q975QpU7Js2bI33Oull15KT09PvwcAAAAAAAAMVUM6DE6dOjX//u//nltvvTX/9E//lNtvvz0f+chH0tvbmyRZvXp1Ro8e3e//DB8+PLvuumtWr15dmxkzZky/mfXP32xm/fmNufjii9PU1FR7tLa2vr0PCwAAAAAAAFvQ8Hov8OeccMIJtb/Hjx+f97///dl7772zZMmSfPjDH67jZsl5552XOXPm1J739PSIgwAAAAAAAAxZQ/qKwdf7i7/4i+y222759a9/nSQZO3ZsnnnmmX4zr776ap5//vmMHTu2NvP000/3m1n//M1m1p/fmJEjR6axsbHfAwAAAAAAAIaqrSoMrlq1Ks8991yam5uTJBMnTsyaNWty77331mZuu+229PX1ZcKECbWZpUuX5pVXXqnNLF68OO95z3uyyy671GZuvfXWfu+1ePHiTJw4cUt/JAAAAAAAABgUdQ2DL774YpYvX57ly5cnSR577LEsX748K1asyIsvvpizzz47d955Zx5//PHceuutOfbYY7PPPvtkypQpSZIDDjggU6dOzRe+8IXcfffd+dnPfpaZM2fmhBNOyLhx45IkJ554YkaMGJFTTz01Dz74YK655prMnz+/321AZ82alUWLFuU73/lOHnnkkVx44YX5+c9/npkzZw76dwIAAAAAAABbQqVarVbr9eZLlizJUUcdtcHxk08+OVdeeWWmTZuW++67L2vWrMm4ceMyefLk/MM//EPGjBlTm33++eczc+bM3HjjjRk2bFiOO+64XHrppdlpp51qM/fff39mzJiRe+65J7vttlvOPPPMnHPOOf3e87rrrsv555+fxx9/PPvuu2/mzp2bo48+epM/S09PT5qamtLd3e22ogAAAAAAAAyKzWlUdQ2D2xJhEAAAAAAAgMG2OY1qq/qNQQAAAAAAAOCtEQYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAw+u9AACwcb29SWdn0tWVNDcnbW1JQ0O9twIAAAAAtlbCIAAMQR0dyaxZyapVfzrW0pLMn5+0t9dvLwAAAABg6+VWogAwxHR0JNOn94+CSfLkk+uOd3TUZy8AAAAAYOsmDALAENLbu+5KwWp1w3Prj82evW4OAAAAAGBzCIMAMIR0dm54peBrVavJypXr5gAAAAAANocwCABDSFfXwM4BAAAAAKwnDALAENLcPLBzAAAAAADrCYMAMIS0tSUtLUmlsvHzlUrS2rpuDgAAAABgcwiDADCENDQk8+ev+/v1cXD983nz1s0BAAAAAGwOYRAAhpj29mThwmT33fsfb2lZd7y9vT57AQAAAABbt+H1XgAA2FB7e3LssUlnZ9LVte43BdvaXCkIAAAAALx1wiAADFENDcmRR9Z7CwAAAABgW+FWogAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAmxyGHzqqae25B4AAAAAAADAFrTJYfC9731vrr766i25CwAAAAAAALCFbHIY/Na3vpW//du/zSc+8Yk8//zzW3InAAAAAAAAYIBtchj8u7/7u9x///157rnncuCBB+bGG2/cknsBAAAAAAAAA2j45gzvtddeue2223LZZZelvb09BxxwQIYP7/8Sv/jFLwZ0QQAAAAAAAODt26wwmCRPPPFEOjo6sssuu+TYY4/dIAwCAAAAAAAAQ89mVb1/+7d/y5e+9KVMmjQpDz74YN71rndtqb0AAAAAAACAAbTJYXDq1Km5++67c9lll+Wzn/3sltwJAAAAAAAAGGCbHAZ7e3tz//33p6WlZUvuAwAAAAAAAGwBmxwGFy9evCX3AAAAAAAAALagYfVeAAAAAAAAANjyhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKEBdw+DSpUvzsY99LOPGjUulUskNN9zQ73y1Ws0FF1yQ5ubmbL/99pk0aVJ+9atf9Zt5/vnnc9JJJ6WxsTE777xzTj311Lz44ov9Zu6///60tbVlu+22S2tra+bOnbvBLtddd13233//bLfddhk/fnx+/OMfD/jnBQAAAAAAgHqpaxhcu3ZtDjrooFx++eUbPT937txceumlueqqq3LXXXdlxx13zJQpU/LHP/6xNnPSSSflwQcfzOLFi3PTTTdl6dKlOf3002vne3p6Mnny5Lz73e/Ovffem0suuSQXXnhhvvvd79Zm7rjjjnzqU5/Kqaeemvvuuy/Tpk3LtGnT8sADD2y5Dw8AAAAAAACDqFKtVqv1XiJJKpVKrr/++kybNi3JuqsFx40bly996Uv58pe/nCTp7u7OmDFjsmDBgpxwwgl5+OGHc+CBB+aee+7JoYcemiRZtGhRjj766KxatSrjxo3LlVdema997WtZvXp1RowYkSQ599xzc8MNN+SRRx5Jknzyk5/M2rVrc9NNN9X2Ofzww3PwwQfnqquu2qT9e3p60tTUlO7u7jQ2Ng7U1wIAAAAAAABvaHMa1ZD9jcHHHnssq1evzqRJk2rHmpqaMmHChCxbtixJsmzZsuy88861KJgkkyZNyrBhw3LXXXfVZo444ohaFEySKVOm5NFHH83vfve72sxr32f9zPr32ZiXXnopPT09/R4AAAAAAAAwVA3ZMLh69eokyZgxY/odHzNmTO3c6tWrM3r06H7nhw8fnl133bXfzMZe47Xv8UYz689vzMUXX5ympqbao7W1dXM/IgAAAAAAAAyaIRsGh7rzzjsv3d3dtcfKlSvrvRIAAAAAAAC8oSEbBseOHZskefrpp/sdf/rpp2vnxo4dm2eeeabf+VdffTXPP/98v5mNvcZr3+ONZtaf35iRI0emsbGx3wMAAAAAAACGqiEbBvfaa6+MHTs2t956a+1YT09P7rrrrkycODFJMnHixKxZsyb33ntvbea2225LX19fJkyYUJtZunRpXnnlldrM4sWL8573vCe77LJLbea177N+Zv37AAAAAAAAwNaurmHwxRdfzPLly7N8+fIkyWOPPZbly5dnxYoVqVQqmT17di666KL893//d375y1/ms5/9bMaNG5dp06YlSQ444IBMnTo1X/jCF3L33XfnZz/7WWbOnJkTTjgh48aNS5KceOKJGTFiRE499dQ8+OCDueaaazJ//vzMmTOntsesWbOyaNGifOc738kjjzySCy+8MD//+c8zc+bMwf5KAAAAAAAAYIuoVKvVar3efMmSJTnqqKM2OH7yySdnwYIFqVar+cY3vpHvfve7WbNmTf7qr/4qV1xxRfbbb7/a7PPPP5+ZM2fmxhtvzLBhw3Lcccfl0ksvzU477VSbuf/++zNjxozcc8892W233XLmmWfmnHPO6fee1113Xc4///w8/vjj2XfffTN37twcffTRm/xZenp60tTUlO7ubrcVBQAAAAAAYFBsTqOqaxjclgiDAAAAAAAADLbNaVRD9jcGAQAAAAAAgIEjDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAowvN4LAAAA6/T2Jp2dSVdX0tyctLUlDQ313goAAADYVgiDAAAwBHR0JLNmJatW/elYS0syf37S3l6/vQAAAIBth1uJAgBAnXV0JNOn94+CSfLkk+uOd3TUZy8AAABg2yIMAgBAHfX2rrtSsFrd8Nz6Y7Nnr5sDAAAAeDuEQQAAqKPOzg2vFHytajVZuXLdHAAAAMDbIQwCAEAddXUN7BwAAADAGxEGAQCgjpqbB3YOAAAA4I0IgwAAUEdtbUlLS1KpbPx8pZK0tq6bAwAAAHg7hEEAAKijhoZk/vx1f78+Dq5/Pm/eujkAAACAt0MYBACAOmtvTxYuTHbfvf/xlpZ1x9vb67MXAAAAsG0ZXu8FAACAdfHv2GOTzs6kq2vdbwq2tblSEAAAABg4wiAAAAwRDQ3JkUfWewsAAABgW+VWogAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRgeL0XAAAA6q+3rzedKzrT9UJXmkc1p22PtjQMa6j3WgAAAMAAEgYBAKBwHQ93ZNaiWVnVs6p2rKWxJfOnzk/7Ae113AwAAAAYSG4lCgAABet4uCPTr53eLwomyZM9T2b6tdPT8XBHnTYDAAAABpowCAAAhert682sRbNSTXWDc+uPzV40O719vYO9GgAAALAFCIMAAFCozhWdG1wp+FrVVLOyZ2U6V3QO4lYAAADAliIMAgBAobpe6BrQOQAAAGBoEwYBAKBQzaOaB3QOAAAAGNqEQQAAKFTbHm1paWxJJZWNnq+kktbG1rTt0TbImwEAAABbgjAIAACFahjWkPlT5yfJBnFw/fN5U+elYVjDoO8GAAAADDxhEAAACtZ+QHsWHr8wuzfu3u94S2NLFh6/MO0HtNdpMwAAAGCgVarVarXeS2wLenp60tTUlO7u7jQ2NtZ7HQAA2Cy9fb3pXNGZrhe60jyqOW17tLlSEAAAALYCm9Oohg/STgAAwBDWMKwhR+55ZL3XAAAAALYgtxIFAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAw+u9AAAAwBvp7U06O5OurqS5OWlrSxoa6r0VAAAAbJ2EQQAAYEjq6EhmzUpWrfrTsZaWZP78pL29fnsBAADA1sqtRAEAgCGnoyOZPr1/FEySJ59cd7yjoz57AQAAwNZMGAQAAIaU3t51VwpWqxueW39s9ux1cwAAAMCmEwYBAIAhpbNzwysFX6taTVauXDcHAAAAbDphEAAAGFK6ugZ2DgAAAFhHGAQAAIaU5uaBnQMAAADWEQYBAIAhpa0taWlJKpWNn69UktbWdXMAAADAphMGAQCAIaWhIZk/f93fr4+D65/Pm7duDgAAANh0wiAAADDktLcnCxcmu+/e/3hLy7rj7e312QsAAAC2ZsPrvQAAAMDGtLcnxx6bdHYmXV3rflOwrc2VggAAAPBWCYMAAMCQ1dCQHHlkvbcAAACAbYNbiQIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACiAMAgAAAAAAAAFEAYBAAAAAACgAMIgAAAAAAAAFGB4vRcAAADYmvT29aZzRWe6XuhK86jmtO3RloZhDfVeCwAAAN6UMAgAALCJOh7uyKxFs7KqZ1XtWEtjS+ZPnZ/2A9rruBkAAAC8ObcSBQAA2AQdD3dk+rXT+0XBJHmy58lMv3Z6Oh7uqNNmAAAAsGmEQQAAgDfR29ebWYtmpZrqBufWH5u9aHZ6+3oHezUAAADYZMIgAADAm+hc0bnBlYKvVU01K3tWpnNF5yBuBQAAAJtHGAQAAHgTXS90DegcAAAA1IMwCAAA8CaaRzUP6BwAAADUgzAIAADwJtr2aEtLY0sqqWz0fCWVtDa2pm2PtkHeDAAAADadMAgAAPAmGoY1ZP7U+UmyQRxc/3ze1HlpGNYw6LsBAADAphIGAQAANkH7Ae1ZePzC7N64e7/jLY0tWXj8wrQf0F6nzQAAAGDTVKrVarXeS2wLenp60tTUlO7u7jQ2NtZ7HQAAYAvp7etN54rOdL3QleZRzWnbo82VggAAANTN5jSq4YO0EwAAwDahYVhDjtzzyHqvAQAAAJvNrUQBAAAAAACgAMIgAAAAAAAAFEAYBAAAAAAAgAIIgwAAAAAAAFAAYRAAAAAAAAAKIAwCAAAAAABAAYRBAAAAAAAAKIAwCAAAAAAAAAUQBgEAAAAAAKAAwiAAAAAAAAAUQBgEAAAAAACAAgiDAAAAAAAAUABhEAAAAAAAAAogDAIAAAAAAEABhEEAAAAAAAAogDAIAAAAAAAABRAGAQAAAAAAoADCIAAAAAAAABRAGAQAAAAAAIACCIMAAAAAAABQAGEQAAAAAAAACiAMAgAAAAAAQAGEQQAAAAAAACjA8HovAAAAQH319iadnUlXV9LcnLS1JQ0N9d4KAACAgSYMAgAAFKyjI5k1K1m16k/HWlqS+fOT9vb67QUAAMDAcytRAACAQnV0JNOn94+CSfLkk+uOd3TUZy8AAAC2DGEQAACgQL29664UrFY3PLf+2OzZ6+YAAADYNgiDAAAABers3PBKwdeqVpOVK9fNAQAAsG0QBgEAAArU1TWwcwAAAAx9wiAAAECBmpsHdg4AAIChb0iHwQsvvDCVSqXfY//996+d/+Mf/5gZM2bkne98Z3baaaccd9xxefrpp/u9xooVK3LMMcdkhx12yOjRo3P22Wfn1Vdf7TezZMmSfOADH8jIkSOzzz77ZMGCBYPx8QAAAOqmrS1paUkqlY2fr1SS1tZ1cwAAAGwbhnQYTJL3vve96erqqj1++tOf1s6dddZZufHGG3Pdddfl9ttvz1NPPZX2/9/e3QfZWRZmA7/OnkgE35yVlq81Z9eofbXBUmixxjicNrSMiQMd7bpQP8YCg76FQWcDVYFC+ejo2CkdzYoiaGuhznSGdN3xD9FFVLBnSjooCCpNsNbQTZYNYCt74sfwcfa8f8Rds2wSNmSzZ3ef328mQ8793OfkWoa9gefa+356e6euN5vNnHnmmXn66adzzz335NZbb80tt9ySq6++emrO9u3bc+aZZ+b000/PAw88kI0bN+Y973lP7rjjjnn9OgEAAOZTuZwMDOz5/XPLwcnXmzbtmQcAAMDSUGq1Wq12h9ifa6+9Nl/84hfzwAMPzLg2Pj6eY489Nv/8z/+cvr6+JMm2bduyevXqbNmyJW94wxvyla98JWeddVYeffTRHH/88UmSm266KZdddlmeeOKJHHHEEbnsssty++235/vf//7UZ7/97W/Pk08+meHh4VlnbTQa6ezszPj4eCqVyqF94QAAAPNkaCjp70927vzVWHf3nlJwr5+7BAAAYIE6mI5qwe8Y/M///M+87GUvyytf+cq8613vysjISJLkvvvuyzPPPJMzzjhjau5v/uZvpqenJ1u2bEmSbNmyJSeddNJUKZgk69evT6PRyEMPPTQ1Z+/PmJwz+Rn789RTT6XRaEz7BQAAsNj09iaPPJLcdVfyz/+856/btysFAQAAlqJl7Q5wIGvWrMktt9yS17zmNRkbG8t1112XWq2W73//+9m1a1eOOOKIvPSlL532nuOPPz67du1KkuzatWtaKTh5ffLageY0Go384he/yJFHHrnPbB/96Edz3XXXzcWXCQAA0FblcrJuXbtTAAAAcLgt6GLwzW9+89Tvf/u3fztr1qzJy1/+8mzevHm/hd18ueKKK3LppZdOvW40Gunu7m5jIgAAAAAAANi/BX+U6N5e+tKX5tWvfnV++MMf5oQTTsjTTz+dJ598ctqcxx57LCeccEKS5IQTTshjjz024/rktQPNqVQqBywfly9fnkqlMu0XAAAAAAAALFSLqhj86U9/mv/6r/9KV1dXTj311LzoRS/K17/+9anrDz/8cEZGRrJ27dokydq1a/O9730vjz/++NScO++8M5VKJSeeeOLUnL0/Y3LO5GcAAAAAAADAUrCgi8EPfOAD+eY3v5lHHnkk99xzT/7kT/4k5XI573jHO9LZ2ZkLLrggl156ae66667cd999Of/887N27dq84Q1vSJK86U1vyoknnph3v/vdefDBB3PHHXfkqquuysUXX5zly5cnSS688ML86Ec/yoc+9KFs27YtN954YzZv3pxLLrmknV86AAAAAAAAzKkF/YzBnTt35h3veEf+53/+J8cee2xOO+20/Pu//3uOPfbYJMnHP/7xdHR05G1ve1ueeuqprF+/PjfeeOPU+8vlcr70pS/loosuytq1a/OSl7wk5557bv76r/96as4rXvGK3H777bnkkksyMDCQarWav//7v8/69evn/esFAAAAAACAw6XUarVa7Q6xFDQajXR2dmZ8fNzzBgEAAAAAAJgXB9NRLeijRAEAAAAAAIC5oRgEAAAAAACAAlAMAgAAAAAAQAEoBgEAAAAAAKAAFIMAAAAAAABQAIpBAAAAAAAAKADFIAAAAAAAABSAYhAAAAAAAAAKQDEIAAAAAAAABaAYBAAAAAAAgAJQDAIAAAAAAEABKAYBAAAAAACgABSDAAAAAAAAUACKQQAAAAAAACgAxSAAAAAAAAAUgGIQAAAAAAAACkAxCAAAAAAAAAWgGAQAAAAAAIACWNbuAAAAAPBCNSeaqY/UM7Z7LF0rulLrqaXcUW53LAAAgAVJMQgAAMCiNLR1KP3D/dnZ2Dk1Vq1UM7BhIL2re9uYDAAAYGFylCgAAACLztDWofRt7ptWCibJaGM0fZv7MrR1qE3JAAAAFi7FIAAAAItKc6KZ/uH+tNKacW1ybOPwxjQnmvMdDQAAYEFTDAIAALCo1EfqM3YK7q2VVnY0dqQ+Up/HVAAAAAufYhAAAIBFZWz32JzOAwAAKArFIAAAAItK14quOZ0HAABQFIpBAAAAFpVaTy3VSjWllPZ5vZRSuivdqfXU5jkZAADAwqYYBAAAYFEpd5QzsGEgSWaUg5OvN23YlHJHed6zAQAALGSKQQAAABad3tW9GTxnMCsrK6eNVyvVDJ4zmN7VvW1KBgAAsHCVWq1Wq90hloJGo5HOzs6Mj4+nUqm0Ow4AAEAhNCeaqY/UM7Z7LF0rulLrqdkpCAAAFMrBdFTL5ikTAAAAzLlyRznrVq1rdwwAAIBFwVGiAAAAAAAAUAB2DAIAAMBeHE8KAAAsVYpBAAAA+KWhrUPpH+7PzsbOqbFqpZqBDQPpXd3bxmQAAACHzlGiAAAAkD2lYN/mvmmlYJKMNkbTt7kvQ1uH2pQMAABgbigGAQAAKLzmRDP9w/1ppTXj2uTYxuGNaU405zsaAADAnFEMAgAAUHj1kfqMnYJ7a6WVHY0dqY/U5zEVAADA3FIMAgAAUHhju8fmdB4AAMBCpBgEAACg8LpWdM3pPAAAgIVIMQgAAEDh1XpqqVaqKaW0z+ullNJd6U6tpzbPyQAAAOaOYhAAAIDCK3eUM7BhIElmlIOTrzdt2JRyR3neswEAAMwVxSAAAAAk6V3dm8FzBrOysnLaeLVSzeA5g+ld3dumZAAAAHOj1Gq1Wu0OsRQ0Go10dnZmfHw8lUql3XEAAAB4gZoTzdRH6hnbPZauFV2p9dTsFAQAABasg+mols1TJgAAAFgUyh3lrFu1rt0xAAAA5pyjRAEAAAAAAKAAFIMAAAAAAABQAIpBAAAAAAAAKADFIAAAAAAAABSAYhAAAAAAAAAKQDEIAAAAAAAABaAYBAAAAAAAgAJQDAIAAAAAAEABKAYBAAAAAACgABSDAAAAAAAAUACKQQAAAAAAACgAxSAAAAAAAAAUwLJ2BwAAAABmrznRTH2knrHdY+la0ZVaTy3ljnK7YwEAAIuAYhAAAAAWiaGtQ+kf7s/Oxs6psWqlmoENA+ld3dvGZAAAwGLgKFEAAABYBIa2DqVvc9+0UjBJRhuj6dvcl6GtQ21KBgAALBaKQQAAAFjgmhPN9A/3p5XWjGuTYxuHN6Y50ZzvaAAAwCKiGAQAAIAFrj5Sn7FTcG+ttLKjsSP1kfo8pgIAABYbxSAAAAAscGO7x+Z0HgAAUEyKQQAAAFjgulZ0zek8AACgmBSDAAAAsMDVemqpVqoppbTP66WU0l3pTq2nNs/JAACAxUQxCAAAAAtcuaOcgQ0DSTKjHJx8vWnDppQ7yvOeDQAAWDwUgwAAALAI9K7uzeA5g1lZWTltvFqpZvCcwfSu7m1TMgAAYLEotVqtVrtDLAWNRiOdnZ0ZHx9PpVJpdxwAAACWqOZEM/WResZ2j6VrRVdqPTU7BQEAoMAOpqNaNk+ZAAAAgDlQ7ihn3ap17Y4BAAAsQo4SBQAAAAAAgAJQDAIAAAAAAEABKAYBAAAAAACgABSDAAAAAAAAUADL2h0AAAAAWNiaE83UR+oZ2z2WrhVdqfXUUu4otzsWAABwkBSDAAAAwH4NbR1K/3B/djZ2To1VK9UMbBhI7+reNiYDAAAOlqNEAQAAgH0a2jqUvs1900rBJBltjKZvc1+Gtg61KRkAAPBCKAYBAACAGZoTzfQP96eV1oxrk2MbhzemOdGc72gAAMALpBgEAAAAZqiP1GfsFNxbK63saOxIfaQ+j6kAAIBDoRgEAAAAZhjbPTan8wAAgPZTDAIAAAAzdK3omtN5AABA+ykGAQAAgBlqPbVUK9WUUtrn9VJK6a50p9ZTm+dkAADAC6UYBAAAAGYod5QzsGEgSWaUg5OvN23YlHJHed6zAQAAL4xiEAAAANin3tW9GTxnMCsrK6eNVyvVDJ4zmN7VvW1KBgAAvBClVqvVaneIpaDRaKSzszPj4+OpVCrtjgMAAABzpjnRTH2knrHdY+la0ZVaT81OQQAAWCAOpqNaNk+ZAAAAgEWq3FHOulXr2h0DAAA4RI4SBQAAAAAAgAJQDAIAAAAAAEABKAYBAAAAAACgABSDAAAAAAAAUACKQQAAAAAAACgAxSAAAAAAAAAUgGIQAAAAAAAACkAxCAAAAAAAAAWgGAQAAAAAAIACUAwCAAAAAABAASgGAQAAAAAAoAAUgwAAAAAAAFAAikEAAAAAAAAogGXtDgAAAABwuDUnmqmP1DO2eyxdK7pS66ml3FFudywAAJhXikEAAABgSRvaOpT+4f7sbOycGqtWqhnYMJDe1b1tTAYAAPPLUaIAAADAkjW0dSh9m/umlYJJMtoYTd/mvgxtHWpTMgAAmH+KQQAAAGBJak400z/cn1ZaM65Njm0c3pjmRHO+owEAQFsoBgEAAIAlqT5Sn7FTcG+ttLKjsSP1kfo8pgIAgPZRDAIAAABL0tjusTmdBwAAi51iEAAAAFiSulZ0zek8AABY7BSDAAAAwJJU66mlWqmmlNI+r5dSSnelO7We2jwnAwCA9lAMAgAAAEtSuaOcgQ0DSTKjHJx8vWnDppQ7yvOeDQAA2kExCAAAACxZvat7M3jOYFZWVk4br1aqGTxnML2re9uUDAAA5l+p1Wq12h1iKWg0Guns7Mz4+HgqlUq74wAAAAB7aU40Ux+pZ2z3WLpWdKXWU7NTEACAJeFgOqpl85QJAAAAoG3KHeWsW7Wu3TEAAKCtHCUKAAAAAAAABaAYBAAAAAAAgAJQDAIAAAAAAEABKAYBAAAAAACgABSDAAAAAAAAUACKQQAAAAAAACgAxSAAAAAAAAAUgGIQAAAAAAAACmBZuwMAAAAAFF2zmdTrydhY0tWV1GpJudzuVAAALDWKQQAAAIA2GhpK+vuTnTt/NVatJgMDSW9v+3IBALD0OEoUAAAAoE2GhpK+vumlYJKMju4ZHxpqTy4AAJYmxSAAAABAGzSbe3YKtlozr02Obdy4Zx4AAMwFxSAAAABAG9TrM3cK7q3VSnbs2DMPAADmgmIQAAAAoA3GxuZ2HgAAPB/FIAAAAEAbdHXN7TwAAHg+ikEAAACANqjVkmo1KZX2fb1USrq798wDAIC5oBgEAAAAaINyORkY2PP755aDk683bdozDwAA5oJiEAAAAKBNenuTwcFk5crp49XqnvHe3vbkAgBgaVrW7gAAAAAARdbbm7zlLUm9noyN7XmmYK1mpyAAAHNPMQgAAADQZuVysm7d/P+5zYlm6iP1jO0eS9eKrtR6ail3aCQBAJYqxSAAAABAAQ1tHUr/cH92NnZOjVUr1QxsGEjvameYAgAsRZ4xCAAAAFAwQ1uH0re5b1opmCSjjdH0be7L0NahNiUDAOBwUgwCAAAAFEhzopn+4f600ppxbXJs4/DGNCea8x0NAIDDTDEIAAAAUCD1kfqMnYJ7a6WVHY0dqY/U5zEVAADzQTEIAAAAUCBju8fmdB4AAIuHYhAAAACgQLpWdM3pPAAAFg/FIAAAAECB1HpqqVaqKaW0z+ullNJd6U6tpzbPyQAAONwUgwAAAAAFUu4oZ2DDQJLMKAcnX2/asCnljvK8ZwMA4PBSDAIAAAAUTO/q3gyeM5iVlZXTxquVagbPGUzv6t42JQMA4HAqtVqtVrtDLAWNRiOdnZ0ZHx9PpVJpdxwAAACA59WcaKY+Us/Y7rF0rehKradmpyAAwCJzMB3VsnnKBAAAAMACU+4oZ92qde2OAQDAPHGUKAAAAAAAABSAYhAAAAAAAAAKQDEIAAAAAAAABaAYBAAAAAAAgAJQDAIAAAAAAEABKAYBAAAAAACgABSDAAAAAAAAUACKQQAAAAAAACiAZe0OAAAAAABzoTnRTH2knrHdY+la0ZVaTy3ljnK7YwEALBiKQQAAAAAWvaGtQ+kf7s/Oxs6psWqlmoENA+ld3dvGZAAAC4ejRAEAAABY1Ia2DqVvc9+0UjBJRhuj6dvcl6GtQ21KBgCwsCgGAQAAAFi0mhPN9A/3p5XWjGuTYxuHN6Y50ZzvaAAAC45iEAAAAIBFqz5Sn7FTcG+ttLKjsSP1kfo8pgIAWJgUgwAAAAAsWmO7x+Z0HgDAUqYYBAAAAGDR6lrRNafzAACWMsUgAAAAAItWraeWaqWaUkr7vF5KKd2V7tR6avOcDABg4VEMAgAAALBolTvKGdgwkCQzysHJ15s2bEq5ozzv2QAAFhrFIAAAAACLWu/q3gyeM5iVlZXTxquVagbPGUzv6t42JQMAWFhKrVar1e4QS0Gj0UhnZ2fGx8dTqVTaHQcAAACgcJoTzdRH6hnbPZauFV2p9dTsFAQAlryD6aiWzVMmAAAAADisyh3lrFu1rt0xAAAWLEeJAgAAAAAAQAEoBgEAAAAAAKAAFIMAAAAAAABQAIpBAAAAAAAAKADFIAAAAAAAABSAYhAAAAAAAAAKQDEIAAAAAAAABaAYBAAAAAAAgAJQDAIAAAAAAEABKAYBAAAAAACgABSDAAAAAAAAUACKQQAAAAAAACgAxSAAAAAAAAAUwLJ2BwAAAAAAFobmRDP1kXrGdo+la0VXaj21lDvK7Y4FAMwRxSAAAAAAkKGtQ+kf7s/Oxs6psWqlmoENA+ld3dvGZADAXHGUKAAAAAAU3NDWofRt7ptWCibJaGM0fZv7MrR1qE3JAIC5pBgEAAAAgAJrTjTTP9yfVlozrk2ObRzemOZEc76jAQBzTDEIAAAAAAVWH6nP2Cm4t1Za2dHYkfpIfR5TAQCHg2IQAAAAAApsbPfYnM4DABYuxSAAAAAAFFjXiq45nQcALFzL2h0AAAAAAGifWk8t1Uo1o43RfT5nsJRSqpVqaj21NqQDoEiaE83UR+oZ2z2WrhVdqfXUUu4otzvWkqIYBAAAAIACK3eUM7BhIH2b+1JKaVo5WEopSbJpw6Z5vTHrxjBA8QxtHUr/cP+0595WK9UMbBhI7+reNiZbWkqtVmvmjwFx0BqNRjo7OzM+Pp5KpdLuOAAAAABwUPZ1Q7a70p1NGzbN6w3ZfeVY+X968v+O+Xz+7xG/n66upFZLynpCgCVjaOtQ+jb3zdi5PvkDKoPnDCoHD+BgOirF4BxRDAIAAACw2LV7p94+bwz/x58kwwNJo3tqqFpNBgaSXveIARa95kQzqwZWTfuBkL1NHmm9vX+73eP7cTAdlaNEAQAAAIAke44VXbdqXVv+7OZEM/3D/TNLwc2DM+aOjiZ9fcngoHIQYLGrj9T3WwomSSut7GjsSH2k3rZ/Ry0lHe0OAAAAAAAw48bwRMeenYJJnnsbc/IMtI0bk2ZzXuIBcJiM7R6b03kcmGIQAAAAAGi7GTd8/7v2y+ND930Ls9VKduxI6vXDnw2Aw6drRdeczuPAFIMAAAAAQNvNuOH709ndAB6zgQRgUav11FKtVFNKaZ/XSymlu9KdWk9tnpMtTYpBAAAAAKDtZtwY/j+za/y6bCABWNTKHeUMbNhzdPRzy8HJ15s2bEq5ozzv2ZYixSAAAAAA0HYzbgy/vJ5UdiSZ2Of8Uinp7k5qNpAALHq9q3szeM5gVlZWThuvVqoZPGcwvat725Rs6Sm1WpOP6uVQNBqNdHZ2Znx8PJVKpd1xAAAAAGBRGto6lP7h/uxs7Ez+40+SzYO/vPKrPQ6lX24oGRxMet0rBlgymhPN1EfqGds9lq4VXan11OwUnIWD6agUg3NEMQgAAAAAc2PvG8P/+W8n5bMfeW127vzV8XLd3cmmTUpBAEgUg22hGAQAAACAw6PZTOr1ZGxszzMFa7WkbAMJACQ5uI5q2TxlAgAAAAB4QcrlZN26dqdYGByzB0uD72XaRTEIAAAAALAITHv+4i9VK9UMbBhI72rnqsJi4XuZdup4/ikAAAAAALTT0Nah9G3um1YkJMloYzR9m/sytHWoTcmAg+F7mXbzjME54hmDAAAAAMDh0JxoZtXAqhlFwqRSSqlWqtnev/2gjyKcPM5wtDGaJ37+RI496tisrKx0rCEcBofze5li84xBAAAAAIAloj5S32+RkCSttLKjsSP1kXrWrVo368/d13GGkxxrCHPvcH0vw8FwlCgAAAAAwAI2tntsTucl+z/OcNLOxk7HGsIcOxzfy3CwFIMAAAAAAAtY14quOZ3XnGimf7g/rTz/U6Y2Dm9Mc6I5q88FDmyuv5fhhXCUKAAAAADAAlbrqaVaqWa0MbrPMm/yuWS1ntqsPu/5jjOctFSONWw2k3o9GRtLurqSWi0pH8Tj2w71/UU3+RzLsd1j6VrRVejnV8719zK8EIpBAAAAAIAFrNxRzsCGgfRt7ksppWmFQimlJMmmDZtmXbYc7DGFi/lYw6GhpL8/2blXD1qtJgMDSe8sHp94qO8/kMNROC60Em5fz7Es8vMr5/p7GV4IR4kCAAAAACxwvat7M3jOYFZWVk4br1aqGTxn8KBKloM9pnCxHms4NJT09U0v9ZJkdHTP+NDzPD7xUN//fJ+9alVy+unJO9+556+rVh3iZ24dyqqBVTn91tPzzqF35vRbT8+qgVVte07k/p5jOdoYLfTzK+fyexleiFKr1Xr+g6QL5FOf+lSuv/767Nq1KyeffHJuuOGGvP71r3/e9zUajXR2dmZ8fDyVSmUekgIAAAAARTMXO8KaE82sGlj1vMeJTh5ruL1/+6LbwdRs7inanlvqTSqV9uz8275937v0DvX9BzJZOD73znxpz4axDA4e/G7EyRLuucdTTu5Cm+/C6fn+GVvM/2zNlYW2u5PF7WA6KjsG93Lbbbfl0ksvzTXXXJP7778/J598ctavX5/HH3+83dEAAAAAAFLuKGfdqnV5x0nvyLpV615QkTB5nOFkaXQgi/VYw3p9/6VesqeU27Fjz7zD8f79aTb3HE26r+06k2MbN+6ZN+vPnGimf7h/n8+smxzbOLwxzYmD+NBD9HzPsdz7+ZVFNRffy/BCKAb38rGPfSzvfe97c/755+fEE0/MTTfdlKOOOiqf+9zn2h0NAAAAAGDOTB5nWK1U93m9u9K9qI81HJvlYxH3N+9Q378/h6NwXIgl3GyfS7mYn18Ji9WydgdYKJ5++uncd999ueKKK6bGOjo6csYZZ2TLli0z5j/11FN56qmnpl43Go15yQkAAAAAMBd6V/fmLa95S+oj9Yw2RvPEz5/IsUcdm5WVlYv+WMOuWT4WcX/zDvX9+3M4CseFWMLN9rmUi/X5lbCYKQZ/6cc//nGazWaOP/74aePHH398tm3bNmP+Rz/60Vx33XXzFQ8AAAAAYM5NHme41NRqe54BODq672M7J58RWKsdnvfvz+EoHBdiCVfrqaVaqWa0MbrPI04nnzFY6znIv4HAIXOU6At0xRVXZHx8fOrXjh072h0JAAAAAIAk5XIyMLDn96XnPEpx8vWmTXvmHY73789k4fjcz9z7s7u7D65wnCzh9vfMyFJK6a50z2sJN/kcy8k//7l5ksX7/EpY7BSDv3TMMcekXC7nsccemzb+2GOP5YQTTpgxf/ny5alUKtN+AQAAAACwMPT2JoODycqV08er1T3jvc/z+MRDff++HI7CcaGWcJPPsVxZmf43sFqpLurnV8JiV2q19rURupjWrFmT17/+9bnhhhuSJBMTE+np6cn73ve+XH755Qd8b6PRSGdnZ8bHx5WEAAAAAAALRLOZ1Ot7ntvX1bVnN97BFG+H+v59GRpK+vuTnTt/NdbdvacUfCGFY5IMbR1K/3B/djZ+9aHdle5s2rCprSVcc6KZ+kg9Y7vH0rWia9E/vxIWooPpqBSDe7ntttty7rnn5uabb87rX//6bNq0KZs3b862bdtmPHvwuRSDAAAAAADM1uEoHJVwUEwH01Etm6dMi8Kf/umf5oknnsjVV1+dXbt25ZRTTsnw8PDzloIAAAAAAHAwyuVk3bo5/syOctatmuMPBZYUOwbniB2DAAAAAAAAzLeD6ag65ikTAAAAAAAA0EaKQQAAAAAAACgAxSAAAAAAAAAUgGIQAAAAAAAACkAxCAAAAAAAAAWgGAQAAAAAAIACUAwCAAAAAABAASgGAQAAAAAAoAAUgwAAAAAAAFAAikEAAAAAAAAoAMUgAAAAAAAAFIBiEAAAAAAAAApAMQgAAAAAAAAFoBgEAAAAAACAAlAMAgAAAAAAQAEoBgEAAAAAAKAAFIMAAAAAAABQAIpBAAAAAAAAKADFIAAAAAAAABSAYhAAAAAAAAAKQDEIAAAAAAAABaAYBAAAAAAAgAJQDAIAAAAAAEABKAYBAAAAAACgABSDAAAAAAAAUACKQQAAAAAAACgAxSAAAAAAAAAUgGIQAAAAAAAACkAxCAAAAAAAAAWgGAQAAAAAAIACUAwCAAAAAABAASgGAQAAAAAAoAAUgwAAAAAAAFAAikEAAAAAAAAoAMUgAAAAAAAAFIBiEAAAAAAAAApAMQgAAAAAAAAFoBgEAAAAAACAAlAMAgAAAAAAQAEoBgEAAAAAAKAAFIMAAAAAAABQAIpBAAAAAAAAKADFIAAAAAAAABSAYhAAAAAAAAAKYFm7AywVrVYrSdJoNNqcBAAAAAAAgKKY7KYmu6oDUQzOkd27dydJuru725wEAAAAAACAotm9e3c6OzsPOKfUmk19yPOamJjIo48+mhUrVqRUKrU7zqLRaDTS3d2dHTt2pFKptDsOsMhYQ4BDYQ0BDoU1BDgU1hDgUFhDgOdqtVrZvXt3Xvayl6Wj48BPEbRjcI50dHSkWq22O8aiValU/EsMeMGsIcChsIYAh8IaAhwKawhwKKwhwN6eb6fgpAPXhgAAAAAAAMCSoBgEAAAAAACAAlAM0lbLly/PNddck+XLl7c7CrAIWUOAQ2ENAQ6FNQQ4FNYQ4FBYQ4BDUWq1Wq12hwAAAAAAAAAOLzsGAQAAAAAAoAAUgwAAAAAAAFAAikEAAAAAAAAoAMUgAAAAAAAAFIBikHnzkY98JG984xtz1FFH5aUvfek+54yMjOTMM8/MUUcdleOOOy4f/OAH8+yzz06bc/fdd+d3f/d3s3z58vzGb/xGbrnllsMfHlhwfvCDH+Qtb3lLjjnmmFQqlZx22mm56667ps2ZzZoCFNftt9+eNWvW5Mgjj8zRRx+dt771rdOuW0OA5/PUU0/llFNOSalUygMPPDDt2ne/+93UarW8+MUvTnd3d/72b/+2PSGBBeWRRx7JBRdckFe84hU58sgj86pXvSrXXHNNnn766WnzrCHAgXzqU5/KqlWr8uIXvzhr1qzJvffe2+5IwCKiGGTePP300zn77LNz0UUX7fN6s9nMmWeemaeffjr33HNPbr311txyyy25+uqrp+Zs3749Z555Zk4//fQ88MAD2bhxY97znvfkjjvumK8vA1ggzjrrrDz77LP5xje+kfvuuy8nn3xyzjrrrOzatSvJ7NYUoLi+8IUv5N3vfnfOP//8PPjgg/m3f/u3vPOd75y6bg0BZuNDH/pQXvayl80YbzQaedOb3pSXv/zlue+++3L99dfn2muvzWc+85k2pAQWkm3btmViYiI333xzHnrooXz84x/PTTfdlL/8y7+cmmMNAQ7ktttuy6WXXpprrrkm999/f04++eSsX78+jz/+eLujAYtEqdVqtdodgmK55ZZbsnHjxjz55JPTxr/yla/krLPOyqOPPprjjz8+SXLTTTflsssuyxNPPJEjjjgil112WW6//fZ8//vfn3rf29/+9jz55JMZHh6ezy8DaKMf//jHOfbYY/Ov//qvqdVqSZLdu3enUqnkzjvvzBlnnDGrNQUopmeffTarVq3KddddlwsuuGCfc6whwPP5yle+kksvvTRf+MIX8trXvjbf+c53csoppyRJPv3pT+fKK6/Mrl27ptaLyy+/PF/84hezbdu2NqYGFqLrr78+n/70p/OjH/0oiTUEOLA1a9bk937v9/LJT34ySTIxMZHu7u68//3vz+WXX97mdMBiYMcgC8aWLVty0kknTd18S5L169en0WjkoYcemppzxhlnTHvf+vXrs2XLlnnNCrTXr//6r+c1r3lN/umf/ik/+9nP8uyzz+bmm2/Occcdl1NPPTXJ7NYUoJjuv//+jI6OpqOjI7/zO7+Trq6uvPnNb572g0fWEOBAHnvssbz3ve/N5z//+Rx11FEzrm/ZsiW///u/P+2HCNavX5+HH344P/nJT+YzKrAIjI+P59d+7demXltDgP15+umnc9999027P9rR0ZEzzjjD/VFg1hSDLBi7du2advMtydTryaMB9zen0WjkF7/4xfwEBdquVCrla1/7Wr7zne9kxYoVefGLX5yPfexjGR4eztFHH51kdmsKUEyTP41/7bXX5qqrrsqXvvSlHH300Vm3bl3+93//N4k1BNi/VquV8847LxdeeGFe97rX7XOONQSYrR/+8Ie54YYb8ud//udTY9YQYH9+/OMfp9ls7nONsD4As6UY5JBcfvnlKZVKB/zlmAtgtma7prRarVx88cU57rjjUq/Xc++99+atb31r/viP/zhjY2Pt/jKANpntGjIxMZEkufLKK/O2t70tp556av7xH/8xpVIp//Iv/9LmrwJol9muITfccEN2796dK664ot2RgQXkhdwfGR0dzYYNG3L22Wfnve99b5uSAwBFs6zdAVjc/uIv/iLnnXfeAee88pWvnNVnnXDCCbn33nunjT322GNT1yb/Ojm295xKpZIjjzxylqmBhWq2a8o3vvGNfOlLX8pPfvKTVCqVJMmNN96YO++8M7feemsuv/zyWa0pwNIy2zVk8gcITjzxxKnx5cuX55WvfGVGRkaSzO6/S4Cl5WD+O2TLli1Zvnz5tGuve93r8q53vSu33nrrfv+/JbGGwFJ1sPdHHn300Zx++ul54xvfmM985jPT5llDgP055phjUi6X97lGWB+A2VIMckiOPfbYHHvssXPyWWvXrs1HPvKRPP744znuuOOSJHfeeWcqlcrUjbu1a9fmy1/+8rT33XnnnVm7du2cZADaa7Zrys9//vMke87R31tHR8fUTqDZrCnA0jLbNeTUU0/N8uXL8/DDD+e0005LkjzzzDN55JFH8vKXvzyJNQSKaLZryCc+8Yl8+MMfnnr96KOPZv369bntttuyZs2aJHvWkCuvvDLPPPNMXvSiFyXZs4a85jWvmTr2HFhaDub+yOjoaE4//fSpUwue+/811hBgf4444oiceuqp+frXv563vvWtSZKJiYl8/etfz/ve9772hgMWDUeJMm9GRkbywAMPZGRkJM1mMw888EAeeOCB/PSnP02SvOlNb8qJJ56Yd7/73XnwwQdzxx135KqrrsrFF1889dO4F154YX70ox/lQx/6ULZt25Ybb7wxmzdvziWXXNLOLw2YZ2vXrs3RRx+dc889Nw8++GB+8IMf5IMf/GC2b9+eM888M8ns1hSgmCqVSi688MJcc801+epXv5qHH344F110UZLk7LPPTmINAfavp6cnv/VbvzX169WvfnWS5FWvelWq1WqS5J3vfGeOOOKIXHDBBXnooYdy2223ZWBgIJdeemk7owMLwOjoaNatW5eenp783d/9XZ544ons2rVr2rPBrCHAgVx66aX57Gc/m1tvvTVbt27NRRddlJ/97Gc5//zz2x0NWCRKrVar1e4QFMN5552XW2+9dcb4XXfdlXXr1iVJ/vu//zsXXXRR7r777rzkJS/Jueeem7/5m7/JsmW/2tx6991355JLLsl//Md/pFqt5q/+6q+e97gOYOn59re/nSuvvDLf/va388wzz+S1r31trr766rz5zW+emjObNQUopmeeeSZXXHFFPv/5z+cXv/hF1qxZk02bNuW1r33t1BxrCDAbjzzySF7xilfkO9/5Tk455ZSp8e9+97u5+OKL861vfSvHHHNM3v/+9+eyyy5rX1BgQbjlllv2e/N+71t01hDgQD75yU/m+uuvz65du3LKKafkE5/4xNTJBQDPRzEIAAAAAAAABeAoUQAAAAAAACgAxSAAAAAAAAAUgGIQAAAAAAAACkAxCAAAAAAAAAWgGAQAAAAAAIACUAwCAAAAAABAASgGAQAAAAAAoAAUgwAAAAAAAFAAikEAAAAAAAAoAMUgAAAA867ZbOaNb3xjent7p42Pj4+nu7s7V155ZZuSAQAALF2lVqvVancIAAAAiucHP/hBTjnllHz2s5/Nu971riTJn/3Zn+XBBx/Mt771rRxxxBFtTggAALC0KAYBAABom0984hO59tpr89BDD+Xee+/N2WefnW9961s5+eST2x0NAABgyVEMAgAA0DatVit/+Id/mHK5nO9973t5//vfn6uuuqrdsQAAAJYkxSAAAABttW3btqxevTonnXRS7r///ixbtqzdkQAAAJakjnYHAAAAoNg+97nP5aijjsr27duzc+fOdscBAABYsuwYBAAAoG3uueee/MEf/EG++tWv5sMf/nCS5Gtf+1pKpVKbkwEAACw9dgwCAADQFj//+c9z3nnn5aKLLsrpp5+ef/iHf8i9996bm266qd3RAAAAliQ7BgEAAGiL/v7+fPnLX86DDz6Yo446Kkly88035wMf+EC+973vZdWqVe0NCAAAsMQoBgEAAJh33/zmN/NHf/RHufvuu3PaaadNu7Z+/fo8++yzjhQFAACYY4pBAAAAAAAAKADPGAQAAAAAAIACUAwCAAAAAABAASgGAQAAAAAAoAAUgwAAAAAAAFAAikEAAAAAAAAoAMUgAAAAAAAAFIBiEAAAAAAAAApAMQgAAAAAAAAFoBgEAAAAAACAAlAMAgAAAAAAQAEoBgEAAAAAAKAA/j+lOH4nIwO+QwAAAABJRU5ErkJggg==\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["### 3.4 <PERSON><PERSON><PERSON>"], "metadata": {"id": "VRTBH0J6E7Zq"}}, {"cell_type": "code", "source": ["# <PERSON><PERSON>\n", "result = (\n", "    agent\n", "        .input({\n", "            \"origin_data_set\": data_set,\n", "            \"your_prediction\": prediction,\n", "        })\n", "        .output({\n", "            \"patterns\": (\"String\", \"Explain patterns you discovered from {origin_data_set}\")\n", "        })\n", "        .start()\n", ")\n", "print(\"[Patterns]\", result[\"patterns\"])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "KvrMLAWfFAvZ", "outputId": "bb881955-1552-4669-fd17-20fa7389b9ff"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[Patterns] There is a linear relationship between the two variables, with a positive correlation. The data points are clustered around a line, and the slope of the line is approximately -0.007.\n"]}]}, {"cell_type": "markdown", "source": ["## Conclusion\n", "\n", "Both LLMs can generate new data pairs according the given data set.\n", "\n", "GPT-3.5-turbo-1106 can consider about the data of a quadratic function should initially decreasing and then increasing. Gemini Pro also shows its considerations about the changes in slope.\n", "\n", "But when we try to let the models explain the patterns, they both got wrong or simply explain it as a linear relationship.\n", "\n", "It's not a rigorous experiment as we didn't try many times and record all the results. But maybe this show case can present some ability demos about LLMs and offering some insights.\n", "\n", "结论：\n", "\n", "1. 两个模型都能够根据给定的数据集生成一定程度上符合要求的新数据对，GPT-3.5-turbo-1106考虑到了二次函数先降后升的特性，Gemini Pro的预测数据里也看到了对斜率变化的考虑；\n", "\n", "2. 但是，当我们尝试让模型解释自己的发现的模式时，模型又只会简单地解释数据增减性上的简单关系，或是认为数据之间只有简单的线性关系，这和实际给出的数据预测结果是不一致的；\n", "\n", "3. 所以有可能从统计学、概率角度，模型能够从概率上给出符合某些模式的预测数据，但是它并不太能给出相应的可解释的原因。\n", "\n", "当然，这并不是一个严谨的试验，我们没有进行很多次重复试验并记录每一次的结果进行对比。只是希望用这样的一个演示给大家带来一些有意思的现象记录。"], "metadata": {"id": "Z5t9sI0-FRhr"}}]}