#!/usr/bin/env python3

"""
测试VAD模型是否能正常加载
"""
import sherpa_onnx

def test_vad_model():
    print("🔄 测试VAD模型加载...")
    
    try:
        vad_config = sherpa_onnx.VadModelConfig()
        vad_config.silero_vad.model = "model/silero_vad_new.onnx"
        vad_config.silero_vad.min_silence_duration = 0.25
        vad_config.silero_vad.min_speech_duration = 0.25
        vad_config.sample_rate = 16000
        
        print(f"VAD配置:")
        print(f"  模型路径: {vad_config.silero_vad.model}")
        print(f"  最小静音时长: {vad_config.silero_vad.min_silence_duration}")
        print(f"  最小语音时长: {vad_config.silero_vad.min_speech_duration}")
        print(f"  采样率: {vad_config.sample_rate}")
        
        window_size = vad_config.silero_vad.window_size
        print(f"  窗口大小: {window_size}")
        
        vad = sherpa_onnx.VoiceActivityDetector(vad_config, buffer_size_in_seconds=100)
        print("✓ VAD模型加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ VAD模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_devices():
    print("\n🔄 测试音频设备...")
    
    try:
        import sounddevice as sd
        print("✓ sounddevice 导入成功")
        
        devices = sd.query_devices()
        print(f"✓ 找到 {len(devices)} 个音频设备")
        
        if len(devices) == 0:
            print("❌ 没有找到音频设备")
            return False
            
        print("📋 可用的音频设备:")
        for i, device in enumerate(devices):
            input_channels = device.get('max_input_channels', 0)
            output_channels = device.get('max_output_channels', 0)
            print(f"  {i}: {device['name']}")
            print(f"     输入通道: {input_channels}, 输出通道: {output_channels}")
            print(f"     默认采样率: {device.get('default_samplerate', 'N/A')}")
        
        default_input_device_idx = sd.default.device[0]
        if default_input_device_idx is not None:
            print(f"🎤 默认输入设备: {devices[default_input_device_idx]['name']}")
        else:
            print("❌ 没有默认输入设备")
            
        return True
        
    except Exception as e:
        print(f"❌ 音频设备测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_stream():
    print("\n🔄 测试音频流创建...")
    
    try:
        import sounddevice as sd
        import numpy as np
        
        # 尝试创建一个简单的音频流
        with sd.InputStream(channels=1, dtype="float32", samplerate=16000) as stream:
            print("✓ 音频流创建成功")
            
            # 尝试读取一小段音频
            samples, overflowed = stream.read(1600)  # 0.1秒的音频
            print(f"✓ 成功读取音频数据，形状: {samples.shape}")
            
            if overflowed:
                print("⚠️ 检测到音频缓冲区溢出")
            
        return True
        
    except Exception as e:
        print(f"❌ 音频流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 开始系统测试...")
    
    # 测试VAD模型
    vad_ok = test_vad_model()
    
    # 测试音频设备
    audio_ok = test_audio_devices()
    
    # 测试音频流
    stream_ok = test_audio_stream()
    
    print("\n📊 测试结果:")
    print(f"VAD模型: {'✓' if vad_ok else '❌'}")
    print(f"音频设备: {'✓' if audio_ok else '❌'}")
    print(f"音频流: {'✓' if stream_ok else '❌'}")
    
    if vad_ok and audio_ok and stream_ok:
        print("\n🎉 所有测试通过，系统应该可以正常工作")
    else:
        print("\n⚠️ 存在问题，需要进一步排查")