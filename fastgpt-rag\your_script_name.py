import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams["font.sans-serif"] = ["Sim<PERSON>ei", "DejaVu Sans"]
rcParams["axes.unicode_minus"] = False


class ImageBrightnessAnalyzer:
    def __init__(self):
        pass

    def load_images(self, image_path1, image_path2):
        """加载两张图片"""
        try:
            self.img1 = cv2.imread(image_path1)
            self.img2 = cv2.imread(image_path2)

            if self.img1 is None or self.img2 is None:
                raise ValueError("无法加载图片，请检查文件路径")

            # 转换为RGB格式（matplotlib显示用）
            self.img1_rgb = cv2.cvtColor(self.img1, cv2.COLOR_BGR2RGB)
            self.img2_rgb = cv2.cvtColor(self.img2, cv2.COLOR_BGR2RGB)

            print(f"图片1尺寸: {self.img1.shape}")
            print(f"图片2尺寸: {self.img2.shape}")

        except Exception as e:
            print(f"加载图片时出错: {e}")
            return False
        return True

    def calculate_brightness_metrics(self, img):
        """计算图片的各种亮度指标"""
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 1. 平均亮度
        mean_brightness = np.mean(gray)

        # 2. 感知亮度（加权平均）
        b, g, r = cv2.split(img)
        perceived_brightness = np.mean(0.299 * r + 0.587 * g + 0.114 * b)

        # 3. 亮度标准差（对比度指标）
        brightness_std = np.std(gray)

        # 4. 亮度范围
        min_brightness = np.min(gray)
        max_brightness = np.max(gray)

        # 5. 中位数亮度
        median_brightness = np.median(gray)

        return {
            "mean": mean_brightness,
            "perceived": perceived_brightness,
            "std": brightness_std,
            "min": min_brightness,
            "max": max_brightness,
            "median": median_brightness,
            "range": max_brightness - min_brightness,
        }

    def calculate_histogram_distances(self):
        """计算直方图距离"""
        if not hasattr(self, "img1") or not hasattr(self, "img2"):
            print("请先加载图片")
            return None

        # 转换为灰度图
        gray1 = cv2.cvtColor(self.img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(self.img2, cv2.COLOR_BGR2GRAY)

        # 计算直方图
        hist1 = cv2.calcHist([gray1], [0], None, [256], [0, 256])
        hist2 = cv2.calcHist([gray2], [0], None, [256], [0, 256])

        # 归一化直方图
        hist1_norm = hist1 / np.sum(hist1)
        hist2_norm = hist2 / np.sum(hist2)

        # 计算各种距离度量
        distances = {}

        # 1. 相关系数 (Correlation) - 值越接近1越相似
        distances["correlation"] = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)

        # 2. 卡方距离 (Chi-Square) - 值越小越相似
        distances["chi_square"] = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CHISQR)

        # 3. 交集距离 (Intersection) - 值越大越相似
        distances["intersection"] = cv2.compareHist(hist1, hist2, cv2.HISTCMP_INTERSECT)

        # 4. Bhattacharyya距离 - 值越小越相似
        distances["bhattacharyya"] = cv2.compareHist(
            hist1, hist2, cv2.HISTCMP_BHATTACHARYYA
        )

        # 5. 欧几里得距离 (L2范数)
        distances["euclidean"] = np.sqrt(np.sum((hist1_norm - hist2_norm) ** 2))

        # 6. 曼哈顿距离 (L1范数)
        distances["manhattan"] = np.sum(np.abs(hist1_norm - hist2_norm))

        # 7. KL散度 (Kullback-Leibler Divergence)
        # 避免除零错误
        hist1_safe = hist1_norm + 1e-10
        hist2_safe = hist2_norm + 1e-10
        distances["kl_divergence"] = np.sum(
            hist1_safe * np.log(hist1_safe / hist2_safe)
        )

        # 8. JS散度 (Jensen-Shannon Divergence)
        m = (hist1_safe + hist2_safe) / 2
        distances["js_divergence"] = 0.5 * np.sum(
            hist1_safe * np.log(hist1_safe / m)
        ) + 0.5 * np.sum(hist2_safe * np.log(hist2_safe / m))

        return distances

    def compare_brightness(self):
        """对比两张图片的亮度"""
        if not hasattr(self, "img1") or not hasattr(self, "img2"):
            print("请先加载图片")
            return

        # 计算两张图片的亮度指标
        metrics1 = self.calculate_brightness_metrics(self.img1)
        metrics2 = self.calculate_brightness_metrics(self.img2)

        # 计算直方图距离
        hist_distances = self.calculate_histogram_distances()

        # 计算差异
        print("=" * 60)
        print("亮度对比分析结果")
        print("=" * 60)

        print(f"{'指标':<15} {'图片1':<10} {'图片2':<10} {'差值':<10} {'变化率':<10}")
        print("-" * 60)

        for key in metrics1.keys():
            diff = metrics2[key] - metrics1[key]
            change_rate = (diff / metrics1[key]) * 100 if metrics1[key] != 0 else 0

            print(
                f"{key:<15} {metrics1[key]:<10.2f} {metrics2[key]:<10.2f} "
                f"{diff:<10.2f} {change_rate:<10.2f}%"
            )

        # 显示直方图距离
        if hist_distances:
            print("\n" + "=" * 60)
            print("直方图距离度量")
            print("=" * 60)
            print(f"{'距离类型':<20} {'数值':<15} {'相似性评估'}")
            print("-" * 60)

            # 相关系数 (越接近1越相似)
            corr_desc = (
                "非常相似"
                if hist_distances["correlation"] > 0.9
                else (
                    "较相似"
                    if hist_distances["correlation"] > 0.7
                    else (
                        "中等相似"
                        if hist_distances["correlation"] > 0.5
                        else "差异较大"
                    )
                )
            )
            print(
                f"{'相关系数':<20} {hist_distances['correlation']:<15.4f} {corr_desc}"
            )

            # 卡方距离 (越小越相似)
            chi_desc = (
                "非常相似"
                if hist_distances["chi_square"] < 0.1
                else (
                    "较相似"
                    if hist_distances["chi_square"] < 1.0
                    else (
                        "中等相似"
                        if hist_distances["chi_square"] < 10.0
                        else "差异较大"
                    )
                )
            )
            print(f"{'卡方距离':<20} {hist_distances['chi_square']:<15.4f} {chi_desc}")

            # Bhattacharyya距离 (越小越相似)
            bhat_desc = (
                "非常相似"
                if hist_distances["bhattacharyya"] < 0.1
                else (
                    "较相似"
                    if hist_distances["bhattacharyya"] < 0.3
                    else (
                        "中等相似"
                        if hist_distances["bhattacharyya"] < 0.5
                        else "差异较大"
                    )
                )
            )
            print(
                f"{'Bhattacharyya距离':<20} {hist_distances['bhattacharyya']:<15.4f} {bhat_desc}"
            )

            # 欧几里得距离
            eucl_desc = (
                "非常相似"
                if hist_distances["euclidean"] < 0.1
                else (
                    "较相似"
                    if hist_distances["euclidean"] < 0.3
                    else "中等相似" if hist_distances["euclidean"] < 0.5 else "差异较大"
                )
            )
            print(
                f"{'欧几里得距离':<20} {hist_distances['euclidean']:<15.4f} {eucl_desc}"
            )

            # KL散度
            kl_desc = (
                "非常相似"
                if hist_distances["kl_divergence"] < 0.1
                else (
                    "较相似"
                    if hist_distances["kl_divergence"] < 0.5
                    else (
                        "中等相似"
                        if hist_distances["kl_divergence"] < 1.0
                        else "差异较大"
                    )
                )
            )
            print(f"{'KL散度':<20} {hist_distances['kl_divergence']:<15.4f} {kl_desc}")

            # JS散度
            js_desc = (
                "非常相似"
                if hist_distances["js_divergence"] < 0.1
                else (
                    "较相似"
                    if hist_distances["js_divergence"] < 0.3
                    else (
                        "中等相似"
                        if hist_distances["js_divergence"] < 0.5
                        else "差异较大"
                    )
                )
            )
            print(f"{'JS散度':<20} {hist_distances['js_divergence']:<15.4f} {js_desc}")

        # 总体评估
        print("\n" + "=" * 60)
        mean_diff = metrics2["mean"] - metrics1["mean"]
        if abs(mean_diff) < 5:
            change_desc = "基本无变化"
        elif mean_diff > 0:
            if mean_diff > 30:
                change_desc = "明显变亮"
            elif mean_diff > 15:
                change_desc = "适度变亮"
            else:
                change_desc = "轻微变亮"
        else:
            if mean_diff < -30:
                change_desc = "明显变暗"
            elif mean_diff < -15:
                change_desc = "适度变暗"
            else:
                change_desc = "轻微变暗"

        print(f"总体评估: {change_desc}")
        print(
            f"平均亮度变化: {mean_diff:.2f} (变化率: {(mean_diff/metrics1['mean']*100):.2f}%)"
        )

        # 基于直方图距离的综合评估
        if hist_distances:
            similarity_score = (
                hist_distances["correlation"]
                + (1 - hist_distances["bhattacharyya"])
                + (1 - hist_distances["js_divergence"])
            ) / 3

            if similarity_score > 0.8:
                hist_desc = "直方图分布非常相似"
            elif similarity_score > 0.6:
                hist_desc = "直方图分布较为相似"
            elif similarity_score > 0.4:
                hist_desc = "直方图分布中等相似"
            else:
                hist_desc = "直方图分布差异较大"

            print(f"直方图相似性: {hist_desc} (综合得分: {similarity_score:.3f})")

        return metrics1, metrics2, hist_distances

    def plot_histograms(self):
        """绘制亮度直方图对比"""
        if not hasattr(self, "img1") or not hasattr(self, "img2"):
            print("请先加载图片")
            return

        # 转换为灰度图
        gray1 = cv2.cvtColor(self.img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(self.img2, cv2.COLOR_BGR2GRAY)

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 显示原图
        axes[0, 0].imshow(self.img1_rgb)
        axes[0, 0].set_title("图片1", fontsize=14)
        axes[0, 0].axis("off")

        axes[0, 1].imshow(self.img2_rgb)
        axes[0, 1].set_title("图片2", fontsize=14)
        axes[0, 1].axis("off")

        # 绘制直方图
        axes[1, 0].hist(
            gray1.ravel(),
            bins=256,
            range=[0, 256],
            alpha=0.7,
            color="blue",
            label="图片1",
        )
        axes[1, 0].hist(
            gray2.ravel(),
            bins=256,
            range=[0, 256],
            alpha=0.7,
            color="red",
            label="图片2",
        )
        axes[1, 0].set_title("亮度直方图对比", fontsize=14)
        axes[1, 0].set_xlabel("亮度值")
        axes[1, 0].set_ylabel("像素数量")
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 绘制累积分布函数
        hist1, bins1 = np.histogram(gray1.ravel(), bins=256, range=[0, 256])
        hist2, bins2 = np.histogram(gray2.ravel(), bins=256, range=[0, 256])

        cdf1 = hist1.cumsum()
        cdf2 = hist2.cumsum()
        cdf1_normalized = cdf1 / cdf1[-1]
        cdf2_normalized = cdf2 / cdf2[-1]

        axes[1, 1].plot(bins1[:-1], cdf1_normalized, color="blue", label="图片1 CDF")
        axes[1, 1].plot(bins2[:-1], cdf2_normalized, color="red", label="图片2 CDF")
        axes[1, 1].set_title("累积分布函数对比", fontsize=14)
        axes[1, 1].set_xlabel("亮度值")
        axes[1, 1].set_ylabel("累积概率")
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    def calculate_rgb_histogram_distances(self):
        """计算RGB各通道的直方图距离"""
        if not hasattr(self, "img1") or not hasattr(self, "img2"):
            print("请先加载图片")
            return None

        channel_names = ["红色通道", "绿色通道", "蓝色通道"]
        rgb_distances = {}

        for i, channel_name in enumerate(channel_names):
            # 提取通道
            channel1 = self.img1_rgb[:, :, i]
            channel2 = self.img2_rgb[:, :, i]

            # 计算直方图
            hist1 = cv2.calcHist([channel1], [0], None, [256], [0, 256])
            hist2 = cv2.calcHist([channel2], [0], None, [256], [0, 256])

            # 计算距离
            correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
            bhattacharyya = cv2.compareHist(hist1, hist2, cv2.HISTCMP_BHATTACHARYYA)

            rgb_distances[channel_name] = {
                "correlation": correlation,
                "bhattacharyya": bhattacharyya,
            }

        return rgb_distances

    def plot_rgb_histograms(self):
        """绘制RGB通道直方图对比"""
        if not hasattr(self, "img1") or not hasattr(self, "img2"):
            print("请先加载图片")
            return

        colors = ["red", "green", "blue"]
        color_names = ["红色通道", "绿色通道", "蓝色通道"]

        fig, axes = plt.subplots(1, 3, figsize=(18, 5))

        # 计算RGB通道距离
        rgb_distances = self.calculate_rgb_histogram_distances()

        for i, (color, name) in enumerate(zip(colors, color_names)):
            # 提取通道
            channel1 = self.img1_rgb[:, :, i]
            channel2 = self.img2_rgb[:, :, i]

            # 绘制直方图
            axes[i].hist(
                channel1.ravel(),
                bins=256,
                range=[0, 256],
                alpha=0.7,
                color=color,
                label="图片1",
                density=True,
            )
            axes[i].hist(
                channel2.ravel(),
                bins=256,
                range=[0, 256],
                alpha=0.7,
                color=color,
                label="图片2",
                density=True,
                linestyle="--",
            )

            # 添加距离信息到标题
            if rgb_distances:
                corr = rgb_distances[name]["correlation"]
                bhat = rgb_distances[name]["bhattacharyya"]
                axes[i].set_title(
                    f"{name}直方图对比\n相关系数: {corr:.3f}, Bhat距离: {bhat:.3f}",
                    fontsize=10,
                )
            else:
                axes[i].set_title(f"{name}直方图对比", fontsize=12)

            axes[i].set_xlabel("像素值")
            axes[i].set_ylabel("密度")
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

        # 打印RGB通道距离分析
        if rgb_distances:
            print("\n" + "=" * 50)
            print("RGB通道直方图距离分析")
            print("=" * 50)
            print(f"{'通道':<12} {'相关系数':<12} {'Bhat距离':<12} {'相似性评估'}")
            print("-" * 50)

            for channel_name, distances in rgb_distances.items():
                corr = distances["correlation"]
                bhat = distances["bhattacharyya"]

                # 综合评估
                if corr > 0.9 and bhat < 0.1:
                    similarity = "非常相似"
                elif corr > 0.7 and bhat < 0.3:
                    similarity = "较相似"
                elif corr > 0.5 and bhat < 0.5:
                    similarity = "中等相似"
                else:
                    similarity = "差异较大"

                print(f"{channel_name:<12} {corr:<12.4f} {bhat:<12.4f} {similarity}")

        return rgb_distances

    def analyze_regions(self, regions=4):
        """分区域分析亮度变化"""
        if not hasattr(self, "img1") or not hasattr(self, "img2"):
            print("请先加载图片")
            return

        # 调整图片尺寸使其一致（取较小的尺寸）
        h1, w1 = self.img1.shape[:2]
        h2, w2 = self.img2.shape[:2]

        min_h, min_w = min(h1, h2), min(w1, w2)

        img1_resized = cv2.resize(self.img1, (min_w, min_h))
        img2_resized = cv2.resize(self.img2, (min_w, min_h))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1_resized, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2_resized, cv2.COLOR_BGR2GRAY)

        # 计算分区尺寸
        region_h = min_h // regions
        region_w = min_w // regions

        print(f"\n分区域亮度分析 ({regions}x{regions}网格):")
        print("=" * 50)

        brightness_changes = []

        for i in range(regions):
            row_changes = []
            for j in range(regions):
                # 提取区域
                y1, y2 = i * region_h, (i + 1) * region_h
                x1, x2 = j * region_w, (j + 1) * region_w

                region1 = gray1[y1:y2, x1:x2]
                region2 = gray2[y1:y2, x1:x2]

                # 计算平均亮度
                mean1 = np.mean(region1)
                mean2 = np.mean(region2)
                change = mean2 - mean1

                row_changes.append(change)

                print(
                    f"区域[{i},{j}]: {mean1:.1f} → {mean2:.1f} "
                    f"(变化: {change:+.1f})"
                )

            brightness_changes.append(row_changes)

        # 可视化分区域变化
        plt.figure(figsize=(10, 8))
        brightness_changes = np.array(brightness_changes)

        im = plt.imshow(brightness_changes, cmap="RdBu_r", center=0)
        plt.colorbar(im, label="亮度变化值")
        plt.title(f"分区域亮度变化热力图 ({regions}x{regions})")
        plt.xlabel("列")
        plt.ylabel("行")

        # 添加数值标注
        for i in range(regions):
            for j in range(regions):
                plt.text(
                    j,
                    i,
                    f"{brightness_changes[i, j]:+.1f}",
                    ha="center",
                    va="center",
                    color=(
                        "white"
                        if abs(brightness_changes[i, j])
                        > np.max(np.abs(brightness_changes)) / 2
                        else "black"
                    ),
                )

        plt.tight_layout()
        plt.show()


# 使用示例
def main():
    analyzer = ImageBrightnessAnalyzer()

    # 请替换为您的图片路径
    image_path1 = "frame_4046_2025_07_24_12_16_53.jpg"  # 第一张图片路径
    image_path2 = "frame_4046_2025_07_24_18_46_54.jpg"  # 第二张图片路径

    print("图片亮度对比分析工具")
    print("=" * 60)

    # 加载图片
    if analyzer.load_images(image_path1, image_path2):
        # 进行亮度对比分析
        analyzer.compare_brightness()

        # 绘制直方图
        analyzer.plot_histograms()

        # 绘制RGB通道直方图（包含距离分析）
        analyzer.plot_rgb_histograms()

        # 分区域分析
        analyzer.analyze_regions(regions=3)
    else:
        print("请检查图片路径是否正确")


if __name__ == "__main__":
    main()
