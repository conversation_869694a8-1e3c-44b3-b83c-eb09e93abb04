{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/generate_agent_powered_function_in_runtime_using_decorator.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Generate **Agent Powered Function** in Runtime using Decorator"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** English\n", "\n", "**Agent Components:** <PERSON>l\n", "\n", "**Description:**\n", "\n", "As a developer, have you ever dreamed about writing some definitions and annotation in code then \"boom!\" all in a sudden, some genies 🧞‍♂️ come out and make all your wishes happen? Notice that: the genies do not write the code for you, instead they just finish the work for you!\n", "\n", "Now Agently framework present a brand new feature \"agent auto function decorator\" for you in version 3.1.4! Use `@<agent instance>.auto_func` to decorate your function and feel the magic!\n", "\n", "Combining the tools-using abilities we enhanced the Agently agents recently, just open your mind and let's see how fantasy work you can let the agents help you to do.\n", "\n", "作为程序员，你是否曾经梦想着有一天，在编写代码的时候，你只需要写下一些定义和注释，然后就有某个神奇的精灵🧞‍♂️跳出来帮你把剩下的工作都完成了？注意哦，这里我们说的，并不是帮你把那些代码写完，而是**直接帮你把你定义的工作做完**哦！\n", "\n", "现在，Agently框架在圆周率版本（3.1.4）为您推出了这样的全新功能：\"agent智能函数装饰器\"，你只需要在写好定义和注释的空函数上方，使用`@<agent instance>.auto_func`这样一个函数装饰器，就可以感受到魔法一般的效果啦！\n", "\n", "结合Agently最近为agent添加的工具使用能力，思路打开，看看我们能让agent帮助我们做哪些神奇的事情吧。"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Step 1: Install Packages"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install Agently"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Demo Code"], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "markdown", "source": ["### DEMO 1: Function to get definition of a keyword\n", "案例1：获取给定关键词定义的函数（关键词：OpenAI）"], "metadata": {"id": "hNvZ9XWIZrpX"}}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "dbe79401-88c2-4892-f1ce-efbff0fa4d99"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'source': 'Wikipedia', 'definition': \"OpenAI is a U.S. artificial intelligence (AI) research organization founded in December 2015, researching artificial intelligence with the declared intention of developing 'safe and beneficial' artificial general intelligence, which it defines as 'highly autonomous systems that outperform humans at most economically valuable work'.\"}\n", "OpenAI is a U.S. artificial intelligence (AI) research organization founded in December 2015, researching artificial intelligence with the declared intention of developing 'safe and beneficial' artificial general intelligence, which it defines as 'highly autonomous systems that outperform humans at most economically valuable work'.\n"]}], "source": ["import Agently\n", "\n", "search_agent = (\n", "    Agently.create_agent()\n", "        .set_settings(\"model.OpenAI.auth\", { \"api_key\": \"\" })\n", ")\n", "\n", "search_agent.use_public_tools(\"search_definition\")\n", "\n", "@search_agent.auto_func\n", "def find_definition(concept_keyword:str) -> {\"source\": (\"String\", ), \"definition\": (\"String\", )}:\n", "    \"\"\"Search your knowledge or the internet to find out the definition of {concept_keyword}.\"\"\"\n", "    return\n", "\n", "result = find_definition(\"OpenAI\")\n", "print(result)\n", "print(result[\"definition\"])"]}, {"cell_type": "markdown", "source": ["### DEMO 2: Function to calculate mathematical expression string\n", "案例2：能够计算自然语言给定的数学表达式的函数（算式：3+5✕(8+2)-2⁒2）"], "metadata": {"id": "9LNA6EF0aeid"}}, {"cell_type": "code", "source": ["import Agently\n", "\n", "calculate_agent = (\n", "    Agently.create_agent()\n", "        .set_settings(\"model.OpenAI.auth\", { \"api_key\": \"\" })\n", ")\n", "\n", "calculate_agent.use_public_tools(\"calculate\")\n", "\n", "@calculate_agent.auto_func\n", "def calculate_from_str(equation: str) -> { \"steps\": [(\"String\", \"one calculate step\")], \"value\": (\"Float\", ) }:\n", "    \"\"\"return calculation result of {equation}.\"\"\"\n", "    return\n", "print(calculate_from_str(\"3+5✕(8+2)-2⁒2\"))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2i3Lf0yMD9ag", "outputId": "e6c450b8-b6a1-40d6-ef8c-094b4479f4de"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{'steps': ['3 + 5 * (8 + 2) - 2 / 2', '3 + 5 * 10 - 2 / 2', '3 + 50 - 2 / 2', '3 + 50 - 1', '53 - 1', '52'], 'value': 52.0}\n"]}]}, {"cell_type": "markdown", "source": ["### DEMO 3: Function to generate SQL\n", "\n", "案例3：根据自然语言生成SQL的函数"], "metadata": {"id": "Pb6bvK5fasKh"}}, {"cell_type": "code", "source": ["from datetime import datetime\n", "import Agently\n", "\n", "# meta data\n", "meta_data = [\n", "    {\n", "        \"table_name\": \"user_info\",\n", "        \"columns\": [\n", "            { \"name\": \"user_id\", \"desc\": \"user identity code\", \"data type\": \"string\", \"example\": \"u_123456\" },\n", "            { \"name\": \"gender\", \"desc\": \"user gender\", \"data type\": \"int\", \"example\": 0, \"annotation\": \"0: female, 1: male\" },\n", "            { \"name\": \"age\", \"desc\": \"user age\", \"data type\": \"int\", \"example\": 18 },\n", "            { \"name\": \"customer_level\", \"desc\": \"user's customer level\", \"data type\": \"int\", \"example\": 4, \"annotation\": \"1: copper, 2: silver, 3: gold, 4: platinum, 5: vvip\" },\n", "            { \"name\": \"reg_time\", \"desc\": \"when did user register\", \"data type\": \"timestamp\", \"example\": 1701340135721 },\n", "            { \"name\": \"reg_channel\", \"desc\": \"which channel did user come from\", \"data type\": \"int\", \"example\": 0, \"annotation\": \"0: natural, 1: game ads, 2: video ads, 3: streaming live ads, 4: offline post ads\" },\n", "        ],\n", "    },\n", "    {\n", "        \"table_name\": \"order\",\n", "        \"columns\": [\n", "            { \"name\": \"order_id\", \"desc\": \"order identity code\", \"data type\": \"string\", \"example\": \"o_456789\" },\n", "            { \"name\": \"customer_user_id\", \"desc\": \"user identity of who paid this order\", \"data type\": \"string\", \"example\": \"u_234567\" },\n", "            { \"name\": \"item_name\", \"desc\": \"item's name\", \"data type\": \"string\", \"example\": \"HD Vanilla Icecream\" },\n", "            { \"name\": \"item_class_tags\", \"desc\": \"tags of item\", \"data type\": \"JSON String\", \"example\": \"[\\\"food\\\", \\\"snack\\\", \\\"frozen\\\"]\", \"annotation\": \"standard JSON string format\" },\n", "            { \"name\": \"price\", \"desc\": \"price of 1 item\", \"data type\": \"float\", \"example\": 1.35, \"annotation\": \"unit: $\" },\n", "            { \"name\": \"order_item_number\", \"desc\": \"how many items in this order\", \"data type\": \"int\", \"example\": 3 },\n", "            { \"name\": \"order_time\", \"desc\": \"when was this order created\", \"data type\": \"timestamp\", \"example\": 1701341789164 },\n", "            { \"name\": \"pay_time\", \"desc\": \"when was this order paid\", \"data type\": \"timestamp\", \"example\": 1701341835323 },\n", "        ],\n", "    }\n", "]\n", "\n", "# generate function\n", "SQL_generate_agent = (\n", "    Agently.create_agent()\n", "        .set_settings(\"model.OpenAI.auth\", { \"api_key\": \"\" })\n", ")\n", "\n", "SQL_generate_agent.info(\"current datetime\", datetime.now())\n", "\n", "@SQL_generate_agent.auto_func\n", "def generate_SQL(meta_data: dict, question: str) -> { \"SQL\": (\"String\", \"SQL Only\") }:\n", "    \"\"\"Generate SQL according {meta_data} to query data to response {question}.\"\"\"\n", "    return\n", "\n", "result = generate_SQL(\n", "    meta_data,\n", "    \"The average age of users those come to our website because of ads in games last year and how much money they spent on our website.\"\n", ")\n", "print(result[\"SQL\"])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputId": "55f4908f-8789-4915-ab0a-2fe2c4fefd9d"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["SELECT AVG(u.age) AS average_age, SUM(o.price * o.order_item_number) AS total_spent FROM user_info u INNER JOIN order o ON u.user_id = o.customer_user_id WHERE u.reg_time BETWEEN '2023-01-01' AND '2023-12-31' AND u.reg_channel = 1\n"]}]}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}