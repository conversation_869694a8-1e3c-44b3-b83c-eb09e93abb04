{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/using_tools_to_enhance_your_agent.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Using Tools to Enhance Your Agent"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** English\n", "\n", "**Agent Components:** <PERSON>l\n", "\n", "**Description:**\n", "\n", "Using Tools is the new feature in Agently v3.1.\n", "\n", "You can register your function as tools to agent instance or to global tool manager.\n", "\n", "You can also use default tools plugins in [`/plugins/tool/` dir](https://github.com/Maplemx/Agently/tree/main/src/plugins/tool) or create more tool package plugins. [Go to `/plugins/tool/`](https://github.com/Maplemx/Agently/tree/main/src/plugins/tool) to see how simple it is to create your own tool package plugins.\n", "\n", "Then you can let agent instance plan whether or not to use tools and in what order to use those tools.\n", "\n", "You can also use `.must_call()` alias to tell agent instance to generate a dict type result to help you calling a tool.\n", "\n", "This show case will demostrate how to do that.\n", "\n", "使用工具是Agently v3.1版本的重大功能性升级。\n", "\n", "你可以把自己编写的函数作为工具注册给Agent实例，或者注册到全局的工具管理器（tool manager）上。\n", "\n", "你也可以使用框架在[`/plugins/tool/`文件夹](https://github.com/Maplemx/Agently/tree/main/src/plugins/tool)中内置的工具集（tool package）插件，或者根据这些插件文件的格式，创建自己的工具集插件。[访问`/plugins/tool/`文件夹](https://github.com/Maplemx/Agently/tree/main/src/plugins/tool)你就会发现创建自己的工具集插件是一件多么简单的事情。\n", "\n", "然后，向Agent实例以`.register_tool()`注册工具，或是通过`.use_all_public_tools()`等指令告知Agent启用全部或者某些工具，你就可以让Agent实例自行决定是否在处理请求时使用工具了。\n", "\n", "当然，你也可以通过`.must_call()`指令让Agent实例生成一个字典类型的结果，帮助你来调用某个指定的工具。\n", "\n", "下面的案例将会逐一展示使用Agently框架v3.1版本是如何做到这些事情的。"], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Step 1: Install Packages"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install -U Agently\n", "!pip install pytz"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Demo Code"], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "markdown", "source": ["### DEMO 1: Browse web page using tool 'browse'"], "metadata": {"id": "lv2wS2_qtQeP"}}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "c63466a3-d962-4a11-865f-47b92c257eba"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The key points of the article are:\n", "1. Essential items to pack for travel, including documents, money, phone and charger, and personal items.\n", "2. Different packing techniques for clothing to maximize luggage space.\n", "3. Additional considerations based on the destination, such as beach or mountain travel.\n", "4. Tips for preparing luggage and packing efficiently.\n"]}], "source": ["import Agently\n", "\n", "# You can change other models\n", "# Read https://github.com/Maplemx/Agently/blob/main/docs/guidebook/application_development_handbook.ipynb\n", "# to explore how to switch models by simply changing some settings\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"model.OpenAI.auth.api_key\", \"\")\n", ")\n", "\n", "agent = agent_factory.create_agent()\n", "\n", "# You can also use .use_all_public_tools() to enable all tools for agent\n", "# But be caution: agent may plan to use more tools than your expectation\n", "result = (\n", "    agent\n", "        .use_public_tools([\"browse\"])\n", "        .input(\"https://zhuanlan.zhihu.com/p/33953044 what does this page say? List the key points.\")\n", "        .start()\n", ")\n", "print(result)"]}, {"cell_type": "markdown", "source": ["#### Current public tool list:\n", "\n", "- search_info 搜索信息\n", "- search_news 搜索新闻\n", "- search_definition 搜索名词定义\n", "- browse 浏览网页\n", "- calculate （使用Python代码）完成计算\n", "\n"], "metadata": {"id": "TRE4z1Ztvn7n"}}, {"cell_type": "markdown", "source": ["### DEMO 2: Resigter a customize tool to agent"], "metadata": {"id": "d2PXHqQ2xRfY"}}, {"cell_type": "code", "source": ["from datetime import datetime\n", "import Agently\n", "\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"model.OpenAI.auth.api_key\", \"\")\n", ")\n", "\n", "# Define a tool to get current date and time\n", "def get_datetime():\n", "    return datetime.now()\n", "\n", "agent = agent_factory.create_agent()\n", "\n", "# When you register tool to agent instance\n", "# the agent instance will automatically plan to use the tool\n", "result = (\n", "    agent\n", "        .register_tool(\n", "            tool_name=\"now\",\n", "            desc=\"get current date and time\",\n", "            args={},\n", "            func=get_datetime,\n", "        )\n", "        .input(\"what time is it now?\")\n", "        .start()\n", ")\n", "print(result)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SoF_RZSaxeY_", "outputId": "7462f196-1daf-43c3-e056-53efe6829d16"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The current time is 2:46 PM on January 3rd, 2024.\n"]}]}, {"cell_type": "markdown", "source": ["You can also register the tool to global tool manager like this:"], "metadata": {"id": "XL65eteKymik"}}, {"cell_type": "code", "source": ["from datetime import datetime\n", "import Agently\n", "\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"model.OpenAI.auth.api_key\", \"\")\n", ")\n", "\n", "def get_datetime():\n", "    return datetime.now()\n", "\n", "# Register tool to global tool manager\n", "Agently.global_tool_manager.register(\n", "    tool_name=\"now\",\n", "    desc=\"get current date and time\",\n", "    args={},\n", "    func=get_datetime\n", ")\n", "\n", "agent = agent_factory.create_agent()\n", "\n", "# Then use .use_public_tools() to state the tool name that you want to use\n", "result = (\n", "    agent\n", "        .use_public_tools([\"now\"])\n", "        .input(\"what time is it now?\")\n", "        .start()\n", ")\n", "print(result)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "CByBuHQryrHo", "outputId": "dd5ee626-d05a-45b9-a42d-798d7e1f6643"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["The current time is 14:52:57\n"]}]}, {"cell_type": "markdown", "source": ["### DEMO 3: Call a tool using .must_call()"], "metadata": {"id": "WDS_iJ7n0I-7"}}, {"cell_type": "code", "source": ["import pytz\n", "import Agently\n", "\n", "agent_factory = (\n", "    Agently.AgentFactory()\n", "        .set_settings(\"model.OpenAI.auth.api_key\", \"\")\n", ")\n", "\n", "# Let's slightly modify this funciton\n", "def get_datetime(timezone):\n", "    tz = pytz.timezone(timezone)\n", "    return datetime.now().astimezone(tz)\n", "\n", "# Now it required an argument\n", "Agently.global_tool_manager.register(\n", "    tool_name=\"now\",\n", "    desc=\"get current date and time\",\n", "    args={\n", "        \"timezone\": (\n", "            \"String\",\n", "            \"[*Required]timezone string use in pytz.timezone() in Python\"\n", "        )\n", "    },\n", "    func=get_datetime\n", ")\n", "\n", "agent = agent_factory.create_agent()\n", "\n", "# Use .must_call() to generate calling dict result without enough information\n", "result = (\n", "    agent\n", "        .must_call(\"now\")\n", "        .input(\"what time is it now?\")\n", "        .start()\n", ")\n", "print(\"Without enough information:\\n\", result)\n", "\n", "# Try again with enough information\n", "result = (\n", "    agent\n", "        .must_call(\"now\")\n", "        .input(\"what time is it now? I'm in Beijing, China.\")\n", "        .start()\n", ")\n", "print(\"\\nWith enough information:\\n\", result)\n", "if result[\"can_call\"]:\n", "    print(get_datetime(**result[\"args\"]))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "sfmj_tA10QI7", "outputId": "b9c4b727-69e2-428b-eba1-2f3010e4a8ff"}, "execution_count": 18, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Without enough information:\n", " {'can_call': False, 'args': {'timezone': None}, 'question': \"What timezone are you in? (e.g. 'America/New_York')\"}\n", "\n", "With enough information:\n", " {'can_call': True, 'args': {'timezone': 'Asia/Shanghai'}, 'question': ''}\n", "2024-01-03 23:04:54.426656+08:00\n"]}]}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}