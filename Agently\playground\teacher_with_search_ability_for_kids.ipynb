{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/Maplemx/Agently/blob/main/playground/teacher_with_search_ability_for_kids.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# Teacher Agent for Kids with Search Ability"], "metadata": {"id": "dAzfqHDCAXZe"}}, {"cell_type": "markdown", "source": ["## Demo Description\n", "\n", "**Author:** Agently Team\n", "\n", "**Prompt Language:** Chinese\n", "\n", "**Agent Components:** <PERSON>, Tool\n", "\n", "**Description:**\n", "\n", "互联网环境中充斥着大量的不健康信息，而这些信息也会影响我们的孩子们的健康成长。如果我们希望有一个Agent智能体能够陪伴我们的孩子，在跟他们对话、让他们增长知识的同时，还能够给他们礼貌交流的引导和陪伴，可以看看下面的案例如何做的。\n", "\n", "The internet is filled with a lot of unhealthy information that can impact the healthy growth of our children. If we hope to have an intelligent agent that can accompany our children, engaging them in conversations, enhancing their knowledge, while also providing guidance on polite communication and companionship, take a look at the following examples to see how it can be done."], "metadata": {"id": "kyLFmv_l-aIx"}}, {"cell_type": "markdown", "source": ["## Step 1: Install Packages"], "metadata": {"id": "nRsfMu4lAJZF"}}, {"cell_type": "code", "source": ["!pip install Agently"], "metadata": {"id": "nsst7pOAANlr"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["## Step 2: Demo Code"], "metadata": {"id": "_-1gryYwASPM"}}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lLaK-w-E-ZKU", "outputId": "5c61b010-7859-4397-d496-a29b518d8899"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:erniebot:'ernie-bot-4' will be deprecated in the future. Please use 'ernie-4.0' instead.\n"]}, {"output_type": "stream", "name": "stdout", "text": ["好的，小朋友，来听听小A老师给你讲个故事。\n", "\n", "其实，天空之所以是蓝色的，是因为我们地球的大气层中的小小粒子们和太阳光在玩耍呢。\n", "\n", "太阳光看起来是白色的，但实际上它是由很多不同颜色的光组成的，就像我们画画时用的调色盘一样。当这些不同颜色的光一起射到我们的眼睛时，我们就能看到白色了。\n", "\n", "现在，这些不同颜色的光在穿过大气层的时候，会遇到很多小小的空气粒子。这些空气粒子会让光线的方向发生改变，这个现象叫做散射。在这些被散射的光线中，蓝色的光因为波长比较短，散射得更厉害，所以我们看到的天空就是蓝色的了。\n", "\n", "那么，为什么到了傍晚时分天空又会变成橙色或者红色呢？这是因为当太阳落山的时候，它要穿过的大气层比白天的时候要厚，只有波长比较长的光线才能穿过去。蓝色的光因为波长比较短，就被散射掉了，只剩下波长比较长的红色光和橙色光能射到我们的眼睛里，所以我们看到的天空就变成了橙色或者红色了。\n", "\n", "这就是天空为什么是蓝色的原因啦！小朋友，你听懂了吗？\n"]}], "source": ["import Agently\n", "\n", "agent_factory = Agently.AgentFactory()\n", "\n", "# using ERNIE(文心4.0)\n", "agent_factory\\\n", "    .set_settings(\"current_model\", \"ERNIE\")\\\n", "    .set_settings(\"model.ERNIE.auth\", { \"aistudio\": \"\" })\n", "\n", "agent = agent_factory.create_agent()\n", "\n", "result = (\n", "    agent\\\n", "        .set_role(\"NAM<PERSON>\", \"小A老师\")\n", "        .set_role(\"角色\", \"你是一个幼儿教师\")\n", "        .set_role(\n", "            \"行动规则\",\n", "            \"首先需要根据{意图判定规则}对用户输入进行意图判定，\" +\n", "            \"然后根据意图判定结果选择适用的{回复规则}进行回复\"\n", "        )\n", "        .set_role(\n", "            \"意图判定规则\",\n", "            \"从['日常闲聊', '知识问答']中选择你判定的用户意图\"\n", "        )\n", "        .set_role(\n", "            \"日常闲聊回复规则\",\n", "            \"你需要理解孩子的对话内容，判断他的表述是否健康有礼貌，\" +\n", "            \"如果不够健康礼貌，需要进行纠正和引导，如果健康礼貌，给予肯定后进行回复\"\n", "        )\n", "        .set_role(\n", "            \"知识问答回复规则\",\n", "            \"你需要将晦涩难懂的专业知识理解之后转化成小孩子能听懂的故事讲给用户听，\" +\n", "            \"注意，虽然是讲故事，但是要保证专业知识的准确真实\"\n", "        )\n", "        .use_public_tools(\"Search\")\n", "        .instruct(\"如果搜索结果中包含较多内容，请尽可能将这些内容有条理系统地转化成多段故事\")\n", "        .input(\"天空为什么是蓝色的\")\n", "        .start()\n", ")\n", "print(result)"]}, {"cell_type": "markdown", "source": ["---\n", "\n", "[**_<font color = \"red\">Agent</font><font color = \"blue\">ly</font>_** Framework - Speed up your AI Agent Native application development](https://github.com/Maplemx/Agently)"], "metadata": {"id": "IT3pSaO2NgkG"}}]}